"use strict"
Object.defineProperty(exports, "__esModule", { value: true })
exports.importQuery = exports.definitionQuery = void 0
/*
- function declarations (with associated comments)
- method declarations (with associated comments)
- type specifications
*/
exports.definitionQuery = `
(
  (comment)* @doc
  .
  (function_declaration
    (identifier) @definition.function) @definition.function
  (#strip! @doc "^//\\s*")
  (#set-adjacent! @doc @definition.function)
)

(
  (comment)* @doc
  .
  (method_declaration
    (field_identifier) @definition.method) @definition.method
  (#strip! @doc "^//\\s*")
  (#set-adjacent! @doc @definition.method)
)

(type_spec
  (type_identifier) @definition.type) @definition.type
`
/*
- Go import statements and package declarations
*/
exports.importQuery = `
; import "package"
(import_declaration
  (import_spec
    path: (interpreted_string_literal) @import.source)) @import.statement

; import alias "package"
(import_declaration
  (import_spec
    name: (package_identifier) @import.name
    path: (interpreted_string_literal) @import.source)) @import.statement

; import . "package" (dot import)
(import_declaration
  (import_spec
    name: (dot) @import.name
    path: (interpreted_string_literal) @import.source)) @import.statement
`
// Default export for backward compatibility
exports.default = exports.definitionQuery
