import * as vscode from 'vscode'
import { QaxChangeDetector } from '../services/QaxChangeDetector'
import { QaxAnalysisContext, QaxChangeType, DEFAULT_QAX_NEXT_EDIT_CONFIG } from '../types/QaxNextEditTypes'

describe('QaxChangeDetector - Diff Algorithm Fix', () => {
	let detector: QaxChangeDetector

	beforeEach(() => {
		detector = new QaxChangeDetector(DEFAULT_QAX_NEXT_EDIT_CONFIG)
	})

	it('should correctly detect full variable rename instead of single character', async () => {
		// Simulate the original problem: showAddNextEventModal -> showAddNewtEventModal
		const beforeContent = 'const modal = showAddNextEventModal();'
		const afterContent = 'const modal = showAddNewtEventModal();'
		
		// Create mock document
		const mockDocument = {
			getText: () => afterContent,
			lineAt: (line: number) => ({
				text: afterContent.split('\n')[line] || '',
				lineNumber: line,
				range: new vscode.Range(line, 0, line, afterContent.split('\n')[line]?.length || 0),
				rangeIncludingLineBreak: new vscode.Range(line, 0, line + 1, 0),
				firstNonWhitespaceCharacterIndex: 0,
				isEmptyOrWhitespace: false
			}),
			offsetAt: (position: vscode.Position) => {
				const lines = afterContent.split('\n')
				let offset = 0
				for (let i = 0; i < position.line; i++) {
					offset += lines[i].length + 1 // +1 for newline
				}
				return offset + position.character
			},
			positionAt: (offset: number) => {
				const lines = afterContent.split('\n')
				let currentOffset = 0
				for (let line = 0; line < lines.length; line++) {
					if (currentOffset + lines[line].length >= offset) {
						return new vscode.Position(line, offset - currentOffset)
					}
					currentOffset += lines[line].length + 1 // +1 for newline
				}
				return new vscode.Position(0, 0)
			}
		} as any

		// Create analysis context
		const context: QaxAnalysisContext = {
			filePath: '/test/file.js',
			languageId: 'javascript',
			document: mockDocument,
			beforeContent,
			afterContent,
			changes: [
				{
					range: new vscode.Range(0, 14, 0, 35), // Range covering "showAddNextEventModal"
					rangeLength: 21, // Length of "showAddNextEventModal"
					rangeOffset: 14, // Offset in the document
					text: 'showAddNewtEventModal' // New text
				}
			]
		}

		// Analyze changes
		const result = await detector.analyzeChanges(context)

		// Verify the result
		expect(result.detectedChanges).toHaveLength(1)
		
		const change = result.detectedChanges[0]
		expect(change.type).toBe(QaxChangeType.VARIABLE_RENAME)
		expect(change.oldValue).toBe('showAddNextEventModal')
		expect(change.newValue).toBe('showAddNewtEventModal')
		expect(change.confidence).toBeGreaterThan(0.5)
		
		// Verify that it's not just detecting single character change
		expect(change.oldValue).not.toBe('x')
		expect(change.newValue).not.toBe('w')
	})

	it('should handle edge cases in identifier expansion', async () => {
		// Test case where single character change is not part of an identifier
		const beforeContent = 'const x = 1;'
		const afterContent = 'const y = 1;'
		
		const mockDocument = {
			getText: () => afterContent,
			offsetAt: (position: vscode.Position) => position.character,
			positionAt: (offset: number) => new vscode.Position(0, offset)
		} as any

		const context: QaxAnalysisContext = {
			filePath: '/test/file.js',
			languageId: 'javascript',
			document: mockDocument,
			beforeContent,
			afterContent,
			changes: [
				{
					range: new vscode.Range(0, 6, 0, 7),
					rangeLength: 1,
					rangeOffset: 6,
					text: 'y'
				}
			]
		}

		const result = await detector.analyzeChanges(context)

		// Should still detect the variable rename correctly
		expect(result.detectedChanges).toHaveLength(1)
		const change = result.detectedChanges[0]
		expect(change.oldValue).toBe('x')
		expect(change.newValue).toBe('y')
	})
})
