"use strict"
Object.defineProperty(exports, "__esModule", { value: true })
exports.importQuery = exports.definitionQuery = void 0
/*
- class declarations
- method declarations (including initializers and deinitializers)
- property declarations
- function declarations
*/
exports.definitionQuery = `
(class_declaration
  name: (type_identifier) @name) @definition.class

(protocol_declaration
  name: (type_identifier) @name) @definition.interface

(class_declaration
    (class_body
        [
            (function_declaration
                name: (simple_identifier) @name
            )
            (subscript_declaration
                (parameter (simple_identifier) @name)
            )
            (init_declaration "init" @name)
            (deinit_declaration "deinit" @name)
        ]
    )
) @definition.method

(class_declaration
    (class_body
        [
            (property_declaration
                (pattern (simple_identifier) @name)
            )
        ]
    )
) @definition.property

(property_declaration
    (pattern (simple_identifier) @name)
) @definition.property

(function_declaration
    name: (simple_identifier) @name) @definition.function
`
/*
- Swift import statements
*/
exports.importQuery = `
; import Module
(import_declaration
  (identifier) @import.name) @import.statement

; import class Module.Class
(import_declaration
  "class"
  (identifier) @import.name) @import.statement

; import struct Module.Struct
(import_declaration
  "struct"
  (identifier) @import.name) @import.statement

; import func Module.function
(import_declaration
  "func"
  (identifier) @import.name) @import.statement

; import var Module.variable
(import_declaration
  "var"
  (identifier) @import.name) @import.statement

; import typealias Module.TypeAlias
(import_declaration
  "typealias"
  (identifier) @import.name) @import.statement
`
// Default export for backward compatibility
exports.default = exports.definitionQuery
