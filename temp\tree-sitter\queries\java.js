"use strict"
Object.defineProperty(exports, "__esModule", { value: true })
exports.importQuery = exports.definitionQuery = void 0
/*
- class declarations
- method declarations
- interface declarations
*/
exports.definitionQuery = `
(class_declaration
  name: (identifier) @name.definition.class) @definition.class

(method_declaration
  name: (identifier) @name.definition.method) @definition.method

(interface_declaration
  name: (identifier) @name.definition.interface) @definition.interface
`
/*
- Java import statements and static imports
*/
exports.importQuery = `
; import package.Class;
(import_declaration
  (identifier) @import.name) @import.statement

; import package.*;
(import_declaration
  (identifier) @import.source
  (asterisk)) @import.statement

; import static package.Class.method;
(import_declaration
  "static"
  (identifier) @import.name) @import.statement
`
// Default export for backward compatibility
exports.default = exports.definitionQuery
