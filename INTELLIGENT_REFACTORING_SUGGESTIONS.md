# 智能重构建议系统

## 功能概述

实现了一个智能的重构建议系统，能够根据用户修改的位置类型（定义 vs 引用）提供不同的建议策略，并自动跳过当前修改位置。

## 核心特性

### 1. 位置类型识别
- **定义位置检测**：判断用户修改的是符号的定义位置
- **引用位置检测**：判断用户修改的是符号的引用位置
- **LSP集成**：使用LSP的定义查找功能进行准确判断

### 2. 智能建议策略

#### A. 定义位置修改 → 建议更新所有引用
```
用户修改：function showAddNextEventModal() → function showAddNewEventModal()
建议：
  ✅ 更新第15行的函数调用
  ✅ 更新第23行的JSX中的调用
  ✅ 更新其他文件中的引用
```

#### B. 引用位置修改 → 优先建议更新定义
```
用户修改：showAddNextEventModal(date) → showAddNewEventModal(date)
建议：
  🔥 更新第5行的函数定义 (高优先级)
  ✅ 更新第23行的其他引用
  ✅ 更新其他文件中的引用
```

### 3. 当前位置过滤
- **精确重叠检测**：避免建议修改用户刚刚修改的位置
- **范围比较**：使用VSCode Range API进行精确的位置比较
- **多文件支持**：正确处理跨文件的引用关系

## 技术实现

### 1. LSP服务增强

#### 新增方法
```typescript
// 获取原始内容中的定义位置
async getDefinitionsFromOriginalContent(
    originalContent: string,
    filePath: string,
    position: vscode.Position,
    languageId: string
): Promise<vscode.Location[]>

// 判断位置是否是定义位置
async isDefinitionLocation(
    originalContent: string,
    filePath: string,
    position: vscode.Position,
    languageId: string
): Promise<boolean>
```

#### 核心逻辑
```typescript
// 1. 创建临时文档（包含原始内容）
const tempDocument = await vscode.workspace.openTextDocument({
    content: originalContent,
    language: languageId
});

// 2. 在临时文档上查询定义
const definitions = await vscode.commands.executeCommand(
    "vscode.executeDefinitionProvider",
    tempDocument.uri,
    position
);

// 3. 映射回原始文档
const mappedDefinitions = definitions.map(def => 
    new vscode.Location(originalUri, def.range)
);
```

### 2. 变更检测增强

#### 元数据扩展
```typescript
metadata: {
    detectionMethod: "symbol_based",
    symbolName: symbolChange.symbolName,
    symbolType: symbolChange.symbolType,
    context: symbolChange.context,
    references: references,           // 原符号的引用
    definitions: definitions,         // 原符号的定义
    isDefinitionLocation: isDefinition, // 是否是定义位置
}
```

### 3. 建议引擎重构

#### 策略分发
```typescript
// 根据修改位置类型决定建议策略
if (isDefinitionLocation) {
    return this.generateDefinitionBasedSuggestions(change, context, references, definitions);
} else {
    return this.generateReferenceBasedSuggestions(change, context, references, definitions);
}
```

#### 定义修改建议
```typescript
private async generateDefinitionBasedSuggestions(
    change: QaxChangeDetection,
    context: QaxAnalysisContext,
    references: vscode.Location[],
    definitions: vscode.Location[]
): Promise<QaxJumpSuggestion[]>
```
- 遍历所有引用位置
- 跳过当前修改位置（定义位置）
- 生成引用更新建议

#### 引用修改建议
```typescript
private async generateReferenceBasedSuggestions(
    change: QaxChangeDetection,
    context: QaxAnalysisContext,
    references: vscode.Location[],
    definitions: vscode.Location[]
): Promise<QaxJumpSuggestion[]>
```
- **优先处理定义位置**（priority = 10）
- 然后处理其他引用位置（较低优先级）
- 跳过当前修改位置

### 4. 位置过滤逻辑

#### 精确重叠检测
```typescript
private isCurrentChangeLocation(
    reference: vscode.Location, 
    change: QaxChangeDetection, 
    context: QaxAnalysisContext
): boolean {
    // 检查文件路径
    if (reference.uri.fsPath !== context.filePath) {
        return false;
    }

    // 检查范围重叠
    const refRange = reference.range;
    const changeRange = change.range;
    
    // 精确的重叠检测逻辑
    return this.rangesOverlap(refRange, changeRange);
}
```

## 用户体验

### 场景1：修改函数定义
```javascript
// 用户修改
function showAddNextEventModal(date) { ... }  // <- 用户在这里修改
```

**系统响应**：
- 🔍 检测到：定义位置修改
- 📋 建议：更新所有调用此函数的地方
- ✅ 跳过：当前定义位置

### 场景2：修改函数调用
```javascript
// 用户修改
showAddNextEventModal(today);  // <- 用户在这里修改
```

**系统响应**：
- 🔍 检测到：引用位置修改
- 📋 建议：
  - 🔥 **优先**：更新函数定义
  - ✅ **其次**：更新其他调用位置
- ✅ 跳过：当前调用位置

## 优势

### 1. 智能化
- **上下文感知**：理解修改的语义意图
- **策略差异化**：不同位置类型采用不同策略
- **优先级排序**：重要的建议排在前面

### 2. 准确性
- **LSP集成**：利用语言服务器的语义分析
- **原始内容查询**：避免查询已修改的符号
- **精确过滤**：准确跳过当前修改位置

### 3. 用户友好
- **清晰描述**：每个建议都有明确的描述
- **相对路径**：显示友好的文件路径
- **优先级指示**：用户知道哪些建议更重要

## 工作流程

```mermaid
graph TD
    A[用户修改符号] --> B[检测符号变化]
    B --> C[查询原符号的引用和定义]
    C --> D{判断修改位置类型}
    D -->|定义位置| E[生成引用更新建议]
    D -->|引用位置| F[生成定义优先建议]
    E --> G[过滤当前位置]
    F --> G
    G --> H[按优先级排序]
    H --> I[显示建议给用户]
```

## 总结

这个智能重构建议系统解决了以下关键问题：

1. ✅ **位置感知**：知道用户修改的是定义还是引用
2. ✅ **策略智能**：根据位置类型提供不同的建议策略
3. ✅ **精确过滤**：准确跳过当前修改位置
4. ✅ **优先级排序**：重要的建议优先显示
5. ✅ **原符号查询**：使用原始内容避免查询新符号

现在用户可以享受到真正智能的重构体验，系统能够理解用户的意图并提供最相关的建议。
