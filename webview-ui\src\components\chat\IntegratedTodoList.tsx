import React, { useState, useMemo, useCallback, useEffect, useRef } from "react"
import { VSCodeButton } from "@vscode/webview-ui-toolkit/react"
import styled from "styled-components"
import { vscode } from "@/utils/vscode"

// Todo status mapping to match backend
export type TodoStatus = "pending" | "in_progress" | "completed"

// Todo item interface matching backend
export interface TodoItem {
	id: string
	content: string
	status: TodoStatus
}

// Styled components (reusing from original TodoTaskList)
const TodoContainer = styled.div<{ isExpanded: boolean }>`
	position: relative;
	background: var(--vscode-input-background);
	border: 1px solid var(--vscode-input-border);
	border-radius: 2px 2px 0 0;
	margin: 0 15px;
	margin-bottom: 0;
	transition: all 0.2s ease-in-out;
	max-height: ${(props) => (props.isExpanded ? "280px" : "24px")};
	overflow: hidden;
	box-sizing: border-box;
`

const TodoHeader = styled.div<{ isExpanded: boolean }>`
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0px 8px;
	background: var(--vscode-input-background);
	border-bottom: ${(props) => (props.isExpanded ? "1px solid var(--vscode-input-border)" : "none")};
	user-select: none;
	min-height: 24px;
`

const TodoHeaderLeft = styled.div`
	display: flex;
	align-items: center;
	gap: 0px;
`

const TodoHeaderCenter = styled.div`
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 12px;
	font-weight: 500;
	color: var(--vscode-foreground);
`

const TodoHeaderRight = styled.div`
	display: flex;
	align-items: center;
	gap: 4px;
`

const HeaderButton = styled(VSCodeButton)`
	padding: 0 !important;
	height: 20px !important;
	min-width: 20px !important;
	margin: 0 !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
`

const TodoList = styled.div`
	padding: 8px 12px 12px 12px;
	max-height: 240px;
	overflow-y: auto;

	/* 确保滚动条样式 */
	&::-webkit-scrollbar {
		width: 6px;
	}

	&::-webkit-scrollbar-track {
		background: var(--vscode-scrollbarSlider-background);
	}

	&::-webkit-scrollbar-thumb {
		background: var(--vscode-scrollbarSlider-background);
		border-radius: 3px;
	}

	&::-webkit-scrollbar-thumb:hover {
		background: var(--vscode-scrollbarSlider-hoverBackground);
	}
`

const TaskItem = styled.div`
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 4px 0;
	border-bottom: 1px solid var(--vscode-widget-border);

	&:last-child {
		border-bottom: none;
	}
`

const TaskIcon = styled.div<{ status: TodoStatus }>`
	width: 16px;
	height: 16px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	color: ${(props) => {
		switch (props.status) {
			case "completed":
				return "var(--vscode-testing-iconPassed)"
			case "in_progress":
				return "var(--vscode-testing-iconQueued)"
			default:
				return "var(--vscode-foreground)"
		}
	}};
`

const TaskContent = styled.div`
	flex: 1;
	min-width: 0;
`

interface TaskTitleProps {
	status: TodoStatus
}

const TaskTitle = styled.div<TaskTitleProps>`
	font-size: 12px;
	color: ${(props) => {
		switch (props.status) {
			case "completed":
				return "var(--vscode-testing-iconPassed)" // 绿色
			case "in_progress":
				return "var(--vscode-testing-iconQueued)" // 黄色
			default:
				return "var(--vscode-foreground)" // 默认颜色
		}
	}};
	text-decoration: ${(props) => (props.status === "completed" ? "line-through" : "none")};
	opacity: ${(props) => (props.status === "completed" ? 0.8 : 1)};
`

const TaskActions = styled.div`
	display: flex;
	align-items: center;
	gap: 2px;
	opacity: 0;
	transition: opacity 0.2s;

	${TaskItem}:hover & {
		opacity: 1;
	}
`

const TaskActionButton = styled(VSCodeButton)`
	padding: 0 !important;
	height: 12px !important;
	min-width: 12px !important;
	margin: 0 !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	font-size: 10px !important;
`

const EmptyState = styled.div`
	text-align: center;
	color: var(--vscode-descriptionForeground);
	font-size: 12px;
	padding: 20px;
`

interface IntegratedTodoListProps {
	todos?: TodoItem[]
	onTodoUpdate?: (todos: TodoItem[]) => void
}

// 单独的 TodoItem 组件，使用 React.memo 优化渲染
const TodoItemComponent = React.memo<{
	todo: TodoItem
	onStatusChange: (id: string) => void
	onDelete: (id: string) => void
}>(
	({ todo, onStatusChange, onDelete }) => {
		const getTaskIcon = (status: TodoStatus) => {
			switch (status) {
				case "completed":
					return "codicon-check"
				case "in_progress":
					return "codicon-sync"
				default:
					return "codicon-circle-outline"
			}
		}

		return (
			<TaskItem key={todo.id}>
				<TaskIcon status={todo.status} onClick={() => onStatusChange(todo.id)}>
					<span className={`codicon ${getTaskIcon(todo.status)}`} />
				</TaskIcon>
				<TaskContent>
					<TaskTitle status={todo.status}>{todo.content}</TaskTitle>
				</TaskContent>
				<TaskActions className="task-actions">
					<TaskActionButton appearance="icon" onClick={() => onDelete(todo.id)} aria-label="Delete todo">
						<span className="codicon codicon-trash" />
					</TaskActionButton>
				</TaskActions>
			</TaskItem>
		)
	},
	(prevProps, nextProps) => {
		// 自定义比较函数：只有当 todo 的内容真正发生变化时才重新渲染
		return (
			prevProps.todo.id === nextProps.todo.id &&
			prevProps.todo.content === nextProps.todo.content &&
			prevProps.todo.status === nextProps.todo.status
		)
	},
)

const IntegratedTodoList: React.FC<IntegratedTodoListProps> = ({ todos = [], onTodoUpdate }) => {
	const [isExpanded, setIsExpanded] = useState(false)
	const [internalTodos, setInternalTodos] = useState<TodoItem[]>([])
	const prevTodosRef = useRef<TodoItem[]>([])

	const toggleExpanded = () => {
		setIsExpanded(!isExpanded)
	}

	// 增量更新逻辑：只更新发生变化的任务项
	useEffect(() => {
		const prevTodos = prevTodosRef.current
		const newTodos = todos

		// 如果是第一次加载，直接设置
		if (prevTodos.length === 0) {
			setInternalTodos([...newTodos])
			prevTodosRef.current = [...newTodos]
			return
		}

		// 创建当前任务的映射
		const currentTodoMap = new Map(internalTodos.map((todo) => [todo.id, todo]))

		// 检查是否需要完全重建列表（长度变化或顺序变化）
		const needsRebuild =
			prevTodos.length !== newTodos.length || newTodos.some((todo, index) => prevTodos[index]?.id !== todo.id)

		if (needsRebuild) {
			setInternalTodos([...newTodos])
			prevTodosRef.current = [...newTodos]
			return
		}

		// 增量更新：只更新发生变化的任务
		let hasChanges = false
		const updatedTodos = [...internalTodos]

		for (let i = 0; i < newTodos.length; i++) {
			const newTodo = newTodos[i]
			const prevTodo = prevTodos[i]
			const currentTodo = currentTodoMap.get(newTodo.id)

			// 如果任务发生了变化（相对于之前的状态）
			if (prevTodo && currentTodo && (prevTodo.content !== newTodo.content || prevTodo.status !== newTodo.status)) {
				updatedTodos[i] = { ...newTodo }
				hasChanges = true
				console.log(`[TodoList] 增量更新任务: ${newTodo.id}, 状态: ${prevTodo.status} -> ${newTodo.status}`)
			}
		}

		// 只有在有变化时才更新状态
		if (hasChanges) {
			setInternalTodos(updatedTodos)
		}

		prevTodosRef.current = [...newTodos]
	}, [todos]) // 移除 internalTodos 依赖，避免循环更新

	// 使用 useMemo 缓存计算结果
	const todoStats = useMemo(() => {
		const completed = internalTodos.filter((t) => t.status === "completed").length
		const total = internalTodos.length
		return { completed, total }
	}, [internalTodos])

	const handleAddTodo = useCallback(() => {
		const content = prompt("Enter todo item:")
		if (content && content.trim()) {
			const newTodo: TodoItem = {
				id: Date.now().toString(),
				content: content.trim(),
				status: "pending",
			}
			const updatedTodos = [...internalTodos, newTodo]

			// 立即更新内部状态
			setInternalTodos(updatedTodos)

			// 通知父组件
			onTodoUpdate?.(updatedTodos)

			// Send to backend
			vscode.postMessage({
				type: "updateTodoList",
				todos: updatedTodos,
			})
		}
	}, [internalTodos, onTodoUpdate])

	const handleToggleTodoStatus = useCallback(
		(todoId: string) => {
			const todo = internalTodos.find((t) => t.id === todoId)
			if (!todo) return

			let newStatus: TodoStatus
			switch (todo.status) {
				case "pending":
					newStatus = "in_progress"
					break
				case "in_progress":
					newStatus = "completed"
					break
				case "completed":
					newStatus = "pending"
					break
				default:
					newStatus = "pending"
			}

			const updatedTodos = internalTodos.map((t) => (t.id === todoId ? { ...t, status: newStatus } : t))

			// 立即更新内部状态
			setInternalTodos(updatedTodos)

			// 通知父组件
			onTodoUpdate?.(updatedTodos)

			// Send to backend
			vscode.postMessage({
				type: "updateTodoList",
				todos: updatedTodos,
			})
		},
		[internalTodos, onTodoUpdate],
	)

	const handleDeleteTodo = useCallback(
		(todoId: string) => {
			if (confirm("Are you sure you want to delete this todo item?")) {
				const updatedTodos = internalTodos.filter((todo) => todo.id !== todoId)

				// 立即更新内部状态
				setInternalTodos(updatedTodos)

				// 通知父组件
				onTodoUpdate?.(updatedTodos)

				// Send to backend
				vscode.postMessage({
					type: "deleteTodoItem",
					todoId: todoId,
				})
			}
		},
		[internalTodos, onTodoUpdate],
	)

	// 使用 useMemo 缓存渲染的组件列表，只有当 internalTodos 数组真正变化时才重新创建
	const renderedTodos = useMemo(() => {
		return internalTodos.map((todo) => (
			<TodoItemComponent key={todo.id} todo={todo} onStatusChange={handleToggleTodoStatus} onDelete={handleDeleteTodo} />
		))
	}, [internalTodos, handleToggleTodoStatus, handleDeleteTodo])

	return (
		<TodoContainer isExpanded={isExpanded}>
			<TodoHeader isExpanded={isExpanded}>
				<TodoHeaderLeft>
					<HeaderButton appearance="icon" onClick={toggleExpanded} aria-label={isExpanded ? "Collapse" : "Expand"}>
						<span
							className={`codicon ${isExpanded ? "codicon-chevron-down" : "codicon-chevron-right"}`}
							style={{ fontSize: "12px" }}
						/>
					</HeaderButton>
					<span className="codicon codicon-checklist" style={{ fontSize: "12px" }} />
				</TodoHeaderLeft>

				<TodoHeaderCenter>
					任务列表({todoStats.completed}/{todoStats.total})
				</TodoHeaderCenter>

				<TodoHeaderRight>
					<HeaderButton appearance="icon" onClick={handleAddTodo} aria-label="Add todo">
						<span className="codicon codicon-add" style={{ fontSize: "12px" }} />
					</HeaderButton>
				</TodoHeaderRight>
			</TodoHeader>

			{isExpanded && (
				<TodoList>
					{internalTodos.length === 0 ? <EmptyState>No todo items yet. Click + to add one.</EmptyState> : renderedTodos}
				</TodoList>
			)}
		</TodoContainer>
	)
}

export default IntegratedTodoList
