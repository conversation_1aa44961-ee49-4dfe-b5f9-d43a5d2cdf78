/**
 * Complete workflow test for QaxNextEdit with all new features
 * Tests: incremental analysis, caching, dependency analysis, suggestion restoration
 */

import * as assert from "assert"

// Mock VS Code API
const mockVscode = {
	workspace: {
		onDidChangeTextDocument: () => ({ dispose: () => {} }),
		onDidOpenTextDocument: () => ({ dispose: () => {} }),
		onDidCloseTextDocument: () => ({ dispose: () => {} }),
		onDidChangeConfiguration: () => ({ dispose: () => {} }),
		getConfiguration: (section?: string) => ({
			get: (key: string, defaultValue?: any) => {
				const configs: any = {
					useQaxNextEdit: true,
					enabled: true,
					enableLSPIntegration: true,
					enableASTAnalysis: true,
					debounceDelayMs: 50,
					maxSuggestions: 8,
					confidenceThreshold: 0.7,
				}
				return configs[key] !== undefined ? configs[key] : defaultValue
			},
			update: () => Promise.resolve(),
		}),
		openTextDocument: async (uri: any) => ({
			uri: uri,
			languageId: "javascript",
			getText: () => "function showEventDetail(event) { console.log(event); }\nshowEventDetail(myEvent);",
			offsetAt: (position: any) => position.line * 100 + position.character,
		}),
		textDocuments: [],
	},
	window: {
		onDidChangeActiveTextEditor: () => ({ dispose: () => {} }),
		createStatusBarItem: () => ({
			text: "",
			show: () => {},
			hide: () => {},
			dispose: () => {},
		}),
		createTextEditorDecorationType: () => ({
			dispose: () => {},
		}),
		activeTextEditor: {
			document: {
				uri: { fsPath: "test.js" },
			},
		},
	},
	languages: {
		onDidChangeDiagnostics: () => ({ dispose: () => {} }),
		registerHoverProvider: () => ({ dispose: () => {} }),
	},
	commands: {
		executeCommand: async () => null,
		registerCommand: () => ({ dispose: () => {} }),
	},
	StatusBarAlignment: { Right: 2 },
	TextEditorRevealType: { InCenter: 1 },
	SymbolKind: { Variable: 12, Function: 11, Class: 4, Method: 5 },
	ConfigurationTarget: { Global: 1 },
	ThemeColor: class {
		constructor(public id: string) {}
	},
	MarkdownString: class {
		constructor(public value: string = "") {
			this.isTrusted = false
		}
		isTrusted = false
		appendMarkdown(value: string) {
			this.value += value
			return this
		}
		appendCodeblock(value: string, language?: string) {
			this.value += `\n\`\`\`${language || ""}\n${value}\n\`\`\`\n`
			return this
		}
	},
	Hover: class {
		constructor(
			public contents: any,
			public range?: any,
		) {}
	},
	Uri: {
		file: (path: string) => ({ fsPath: path, scheme: "file" }),
	},
	Range: class {
		constructor(
			public start: { line: number; character: number },
			public end: { line: number; character: number },
		) {}

		get isEmpty() {
			return this.start.line === this.end.line && this.start.character === this.end.character
		}

		isEqual(other: any) {
			return (
				this.start.line === other.start.line &&
				this.start.character === other.start.character &&
				this.end.line === other.end.line &&
				this.end.character === other.end.character
			)
		}
	},
	Position: class {
		constructor(
			public line: number,
			public character: number,
		) {}

		isEqual(other: any) {
			return this.line === other.line && this.character === other.character
		}
	},
	Selection: class {
		constructor(
			public start: { line: number; character: number },
			public end: { line: number; character: number },
		) {}
	},
	Location: class {
		constructor(
			public uri: any,
			public range: any,
		) {}
	},
}

// Apply mocks
const vscode = mockVscode as any
;(global as any).vscode = vscode

// Test counter
let testCount = 0
let passedCount = 0
let failedCount = 0

function test(name: string, fn: () => void | Promise<void>): Promise<void> | void {
	testCount++
	console.log(`\n🧪 Workflow Test ${testCount}: ${name}`)

	try {
		const result = fn()
		if (result instanceof Promise) {
			return result
				.then(() => {
					console.log(`✅ PASSED: ${name}`)
					passedCount++
				})
				.catch((error) => {
					console.log(`❌ FAILED: ${name}`)
					console.log(`   Error: ${error.message}`)
					failedCount++
				})
		} else {
			console.log(`✅ PASSED: ${name}`)
			passedCount++
		}
	} catch (error) {
		console.log(`❌ FAILED: ${name}`)
		console.log(`   Error: ${(error as Error).message}`)
		failedCount++
	}
}

async function runCompleteWorkflowTests() {
	console.log("🚀 QaxNextEdit Complete Workflow Tests")
	console.log("=".repeat(60))

	// Test 1: Initial file analysis and caching
	test("Should perform initial analysis and cache results", async () => {
		// Mock QaxNextEdit service state
		const mockService = {
			state: {
				isEnabled: true,
				isAnalyzing: false,
				pendingChanges: new Map(),
				cachedResults: new Map(),
				lastAnalysisTime: null,
			},
			incrementalCache: new Map(),
			fileDependencies: new Map(),
			asyncAnalysisQueue: new Set(),
		}

		const filePath = "d:/code/calendar/public/calendar.js"

		// Simulate initial analysis
		const analysisResult = {
			detectedChanges: [
				{
					type: "function_rename",
					filePath,
					range: new vscode.Range({ line: 0, character: 9 }, { line: 0, character: 24 }),
					oldValue: "showEventDetail",
					newValue: "showMyEventDetail",
					confidence: 0.95,
				},
			],
			jumpSuggestions: [
				{
					id: "suggestion-1",
					filePath,
					range: new vscode.Range({ line: 1, character: 0 }, { line: 1, character: 15 }),
					description: "Update function call from 'showEventDetail' to 'showMyEventDetail'",
					changeType: "function_rename",
					priority: 9,
					suggestedEdit: {
						range: new vscode.Range({ line: 1, character: 0 }, { line: 1, character: 15 }),
						newText: "showMyEventDetail",
						description: "Update function call",
					},
					relatedChange: {
						type: "function_rename",
						filePath,
						range: new vscode.Range({ line: 1, character: 0 }, { line: 1, character: 15 }),
						oldValue: "showEventDetail",
						newValue: "showMyEventDetail",
						confidence: 0.95,
					},
				},
			],
			analysisTime: 150,
			confidence: 0.95,
			metadata: {
				lspAvailable: true,
				astParsed: true,
			},
		}

		// Cache the result
		mockService.state.cachedResults.set(filePath, analysisResult)
		mockService.incrementalCache.set(filePath, {
			baseAnalysis: analysisResult,
			modifications: [],
			lastModified: new Date(),
		})

		// Verify caching
		assert.ok(mockService.state.cachedResults.has(filePath), "Should cache analysis result")
		assert.ok(mockService.incrementalCache.has(filePath), "Should cache for incremental analysis")

		const cached = mockService.state.cachedResults.get(filePath)
		assert.strictEqual(cached.jumpSuggestions.length, 1, "Should cache 1 suggestion")
		assert.strictEqual(cached.confidence, 0.95, "Should cache confidence score")

		console.log("    ✓ Initial analysis cached successfully")
		console.log(
			`    ✓ Cached ${cached.jumpSuggestions.length} suggestions with ${Math.round(cached.confidence * 100)}% confidence`,
		)
	})

	// Test 2: Incremental analysis workflow
	test("Should perform incremental analysis for small changes", async () => {
		const mockService = {
			incrementalCache: new Map(),
			state: { cachedResults: new Map() },
		}

		const filePath = "test.js"

		// Set up base analysis
		const baseAnalysis = {
			detectedChanges: [],
			jumpSuggestions: [
				{
					id: "base-1",
					filePath,
					range: new vscode.Range({ line: 0, character: 0 }, { line: 0, character: 10 }),
					description: "Base suggestion",
					changeType: "variable_rename",
					priority: 8,
				},
			],
			analysisTime: 100,
			confidence: 0.8,
			metadata: { lspAvailable: true, astParsed: true },
		}

		mockService.incrementalCache.set(filePath, {
			baseAnalysis,
			modifications: [],
			lastModified: new Date(),
		})

		// Simulate incremental change
		const incrementalChange = {
			filePath,
			document: null,
			changes: [{ rangeLength: 5, text: "newVar" }],
			beforeContent: "oldVar",
			afterContent: "newVar",
			languageId: "javascript",
		}

		// Check if incremental analysis is possible
		const canIncremental = (filePath: string, context: any) => {
			const cached = mockService.incrementalCache.get(filePath)
			if (!cached) return false

			const timeDiff = Date.now() - cached.lastModified.getTime()
			if (timeDiff > 5 * 60 * 1000) return false

			if (cached.modifications.length > 10) return false

			const hasComplexChanges = context.changes.some((change: any) => change.rangeLength > 100 || change.text.length > 100)

			return !hasComplexChanges
		}

		assert.strictEqual(canIncremental(filePath, incrementalChange), true, "Should allow incremental analysis")

		// Perform incremental update
		const cached = mockService.incrementalCache.get(filePath)
		cached.modifications.push(incrementalChange)
		cached.lastModified = new Date()

		// Simulate updated result
		const updatedResult = {
			...baseAnalysis,
			jumpSuggestions: [
				...baseAnalysis.jumpSuggestions,
				{
					id: "incremental-1",
					filePath,
					range: new vscode.Range({ line: 1, character: 0 }, { line: 1, character: 6 }),
					description: "Incremental suggestion",
					changeType: "variable_rename",
					priority: 9,
				},
			],
			analysisTime: baseAnalysis.analysisTime + 25,
			confidence: Math.max(baseAnalysis.confidence, 0.9),
		}

		mockService.state.cachedResults.set(filePath, updatedResult)

		// Verify incremental update
		assert.strictEqual(cached.modifications.length, 1, "Should track 1 modification")

		const result = mockService.state.cachedResults.get(filePath)
		assert.strictEqual(result.jumpSuggestions.length, 2, "Should have 2 suggestions after incremental update")
		assert.strictEqual(result.analysisTime, 125, "Should accumulate analysis time")
		assert.strictEqual(result.confidence, 0.9, "Should update confidence")

		console.log("    ✓ Incremental analysis performed successfully")
		console.log(`    ✓ Updated to ${result.jumpSuggestions.length} suggestions in ${result.analysisTime}ms`)
	})

	// Test 3: Dependency analysis workflow
	test("Should analyze file dependencies asynchronously", async () => {
		const mockService = {
			fileDependencies: new Map(),
			asyncAnalysisQueue: new Set(),
			state: { cachedResults: new Map() },
		}

		const mainFile = "main.js"
		const dependencies = ["utils.js", "constants.js", "types.js"]

		// Set up dependencies
		mockService.fileDependencies.set(mainFile, new Set(dependencies))

		// Simulate async dependency analysis
		const scheduleAsyncAnalysis = async (filePath: string) => {
			const deps = mockService.fileDependencies.get(filePath) || new Set()

			for (const depPath of deps) {
				if (!mockService.asyncAnalysisQueue.has(depPath) && !mockService.state.cachedResults.has(depPath)) {
					mockService.asyncAnalysisQueue.add(depPath)

					// Simulate async processing
					setTimeout(() => {
						mockService.state.cachedResults.set(depPath, {
							detectedChanges: [],
							jumpSuggestions: [],
							analysisTime: 50,
							confidence: 0.7,
							metadata: { lspAvailable: true, astParsed: true },
						})
						mockService.asyncAnalysisQueue.delete(depPath)
					}, 10)
				}
			}
		}

		await scheduleAsyncAnalysis(mainFile)

		// Check initial queue state
		assert.strictEqual(mockService.asyncAnalysisQueue.size, 3, "Should queue 3 dependencies")

		// Wait for async processing
		await new Promise((resolve) => setTimeout(resolve, 50))

		// Verify results
		assert.strictEqual(mockService.asyncAnalysisQueue.size, 0, "Queue should be empty after processing")
		assert.strictEqual(mockService.state.cachedResults.size, 3, "Should have analyzed 3 dependencies")

		dependencies.forEach((dep) => {
			assert.ok(mockService.state.cachedResults.has(dep), `Should have analyzed ${dep}`)
		})

		console.log("    ✓ Dependency analysis completed successfully")
		console.log(`    ✓ Analyzed ${dependencies.length} dependencies asynchronously`)
	})

	// Test 4: Suggestion restoration workflow
	test("Should restore suggestions when reopening files", async () => {
		const mockService = {
			state: { cachedResults: new Map() },
			uiProvider: {
				showSuggestions: (suggestions: any[]) => {
					console.log(`    ✓ UI showing ${suggestions.length} restored suggestions`)
				},
			},
		}

		const filePath = "reopened.js"

		// Set up cached suggestions
		const cachedSuggestions = [
			{
				id: "restore-1",
				filePath,
				range: new vscode.Range({ line: 0, character: 0 }, { line: 0, character: 15 }),
				description: "Restore function rename suggestion",
				changeType: "function_rename",
				priority: 9,
			},
			{
				id: "restore-2",
				filePath,
				range: new vscode.Range({ line: 2, character: 5 }, { line: 2, character: 20 }),
				description: "Restore variable update suggestion",
				changeType: "variable_rename",
				priority: 8,
			},
		]

		mockService.state.cachedResults.set(filePath, {
			detectedChanges: [],
			jumpSuggestions: cachedSuggestions,
			analysisTime: 200,
			confidence: 0.88,
			metadata: { lspAvailable: true, astParsed: true },
		})

		// Simulate file reopening
		const handleDocumentOpened = (filePath: string) => {
			const cachedResult = mockService.state.cachedResults.get(filePath)
			if (cachedResult && cachedResult.jumpSuggestions.length > 0) {
				// Show UI if this is the active file
				const activeEditor = vscode.window.activeTextEditor
				if (activeEditor && activeEditor.document.uri.fsPath === filePath) {
					mockService.uiProvider.showSuggestions(cachedResult.jumpSuggestions)
				}

				return {
					restored: true,
					suggestions: cachedResult.jumpSuggestions,
					count: cachedResult.jumpSuggestions.length,
					confidence: cachedResult.confidence,
				}
			}
			return { restored: false, suggestions: [], count: 0, confidence: 0 }
		}

		// Set active editor to match file
		vscode.window.activeTextEditor.document.uri.fsPath = filePath

		const result = handleDocumentOpened(filePath)

		assert.strictEqual(result.restored, true, "Should restore suggestions")
		assert.strictEqual(result.count, 2, "Should restore 2 suggestions")
		assert.strictEqual(result.confidence, 0.88, "Should restore confidence score")
		assert.strictEqual(result.suggestions[0].changeType, "function_rename", "Should restore function rename")
		assert.strictEqual(result.suggestions[1].changeType, "variable_rename", "Should restore variable rename")

		console.log("    ✓ Suggestion restoration completed successfully")
		console.log(`    ✓ Restored ${result.count} suggestions with ${Math.round(result.confidence * 100)}% confidence`)
	})

	// Test 5: Complete integration workflow
	test("Should handle complete workflow from change to suggestion", async () => {
		// Simulate the real scenario: showEventDetail -> showMyEventDetail
		const mockWorkflow = {
			// Step 1: Detect change
			detectChange: (beforeContent: string, afterContent: string) => {
				const changes = []
				if (beforeContent.includes("showEventDetail") && afterContent.includes("showMyEventDetail")) {
					changes.push({
						type: "function_rename",
						oldValue: "showEventDetail",
						newValue: "showMyEventDetail",
						confidence: 0.95,
					})
				}
				return changes
			},

			// Step 2: Generate suggestions
			generateSuggestions: (changes: any[], filePath: string) => {
				return changes.map((change, index) => ({
					id: `workflow-${index}`,
					filePath,
					range: new vscode.Range({ line: 1, character: 0 }, { line: 1, character: 15 }),
					description: `Change: ${change.oldValue} ➜ ${change.newValue}`,
					changeType: change.type,
					priority: Math.round(change.confidence * 10),
					suggestedEdit: {
						range: new vscode.Range({ line: 1, character: 0 }, { line: 1, character: 15 }),
						newText: change.newValue,
						description: `Update ${change.type}`,
					},
					relatedChange: change,
				}))
			},

			// Step 3: Convert to NextEdit format
			convertToNextEditFormat: (suggestions: any[]) => {
				return suggestions.map((suggestion) => ({
					id: suggestion.id,
					type: "modify",
					description: suggestion.description,
					location: {
						anchor: `${suggestion.relatedChange.oldValue}(myEvent);`,
						position: "replace",
					},
					patch: {
						oldContent: suggestion.relatedChange.oldValue,
						newContent: suggestion.relatedChange.newValue,
					},
				}))
			},
		}

		const filePath = "d:/code/calendar/public/calendar.js"
		const beforeContent = "function showEventDetail(event) { console.log(event); }\nshowEventDetail(myEvent);"
		const afterContent = "function showMyEventDetail(event) { console.log(event); }\nshowEventDetail(myEvent);"

		// Execute workflow
		const changes = mockWorkflow.detectChange(beforeContent, afterContent)
		assert.strictEqual(changes.length, 1, "Should detect 1 change")
		assert.strictEqual(changes[0].type, "function_rename", "Should detect function rename")

		const suggestions = mockWorkflow.generateSuggestions(changes, filePath)
		assert.strictEqual(suggestions.length, 1, "Should generate 1 suggestion")
		assert.strictEqual(suggestions[0].priority, 10, "Should have high priority")

		const nextEditSuggestions = mockWorkflow.convertToNextEditFormat(suggestions)
		assert.strictEqual(nextEditSuggestions.length, 1, "Should convert 1 suggestion")
		assert.strictEqual(nextEditSuggestions[0].type, "modify", "Should be modify type")
		assert.strictEqual(
			nextEditSuggestions[0].description,
			"Change: showEventDetail ➜ showMyEventDetail",
			"Should have correct description",
		)

		console.log("    ✓ Complete workflow executed successfully")
		console.log(`    ✓ Detected: ${changes[0].oldValue} → ${changes[0].newValue}`)
		console.log(`    ✓ Generated: ${suggestions.length} suggestions`)
		console.log(`    ✓ Converted: ${nextEditSuggestions.length} NextEdit suggestions`)
	})

	// Wait for any async operations
	await new Promise((resolve) => setTimeout(resolve, 100))

	// Print results
	console.log("\n" + "=".repeat(60))
	console.log("📊 Complete Workflow Test Results:")
	console.log(`  ✅ Passed: ${passedCount}`)
	console.log(`  ❌ Failed: ${failedCount}`)
	console.log(`  📈 Success Rate: ${Math.round((passedCount / testCount) * 100)}%`)

	if (failedCount === 0) {
		console.log("\n🎉 All complete workflow tests passed!")
		console.log("🚀 QaxNextEdit complete workflow is functioning correctly!")
		console.log("\n✨ Features verified:")
		console.log("   • Initial analysis and caching")
		console.log("   • Incremental analysis for small changes")
		console.log("   • Asynchronous dependency analysis")
		console.log("   • Suggestion restoration for reopened files")
		console.log("   • Complete integration workflow")
		return true
	} else {
		console.log(`\n💥 ${failedCount} complete workflow test(s) failed!`)
		return false
	}
}

// Run complete workflow tests
runCompleteWorkflowTests()
	.then((success) => {
		process.exit(success ? 0 : 1)
	})
	.catch((error) => {
		console.error("💥 Error running complete workflow tests:", error)
		process.exit(1)
	})
