"use strict"
Object.defineProperty(exports, "__esModule", { value: true })
exports.importQuery = exports.definitionQuery = void 0
/*
- struct declarations
- union declarations
- function declarations
- typedef declarations
*/
exports.definitionQuery = `
(struct_specifier (type_identifier) @definition.class body:(_)) @definition.class

(declaration type: (union_specifier (type_identifier) @definition.class)) @definition.class

(function_declarator declarator: (identifier) @name.definition.function) @definition.function

(type_definition declarator: (type_identifier) @name.definition.type) @definition.type
`
/*
- C #include statements
*/
exports.importQuery = `
; #include <header.h>
(preproc_include
  path: (system_lib_string) @import.source) @import.statement

; #include "header.h"
(preproc_include
  path: (string_literal) @import.source) @import.statement
`
// Default export for backward compatibility
exports.default = exports.definitionQuery
