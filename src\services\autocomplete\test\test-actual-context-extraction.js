#!/usr/bin/env node

/**
 * 测试实际的extractIntelligentContext函数
 * 直接调用编译后的TypeScript代码
 */

const fs = require("fs")
const path = require("path")

// 尝试直接导入编译后的模块
async function testActualContextExtraction() {
	console.log("🧪 测试实际的extractIntelligentContext函数")
	console.log("=".repeat(80))

	try {
		// 检查dist目录结构
		const distPath = path.join(__dirname, "dist")
		if (!fs.existsSync(distPath)) {
			console.log("❌ dist目录不存在，请先运行 npm run compile")
			return false
		}

		console.log("📁 dist目录内容:")
		const distFiles = fs.readdirSync(distPath)
		distFiles.forEach((file) => {
			console.log(`   - ${file}`)
		})

		// 查找contextExtractor相关文件
		const contextExtractorFiles = distFiles.filter((file) => file.includes("contextExtractor") || file.includes("context"))

		if (contextExtractorFiles.length === 0) {
			console.log("⚠️  在dist目录中未找到contextExtractor相关文件")
			console.log("   这可能是因为esbuild将所有代码打包到了extension.js中")
		}

		// 尝试查看extension.js的内容片段
		const extensionPath = path.join(distPath, "extension.js")
		if (fs.existsSync(extensionPath)) {
			const extensionContent = fs.readFileSync(extensionPath, "utf8")

			// 检查是否包含extractIntelligentContext函数
			if (extensionContent.includes("extractIntelligentContext")) {
				console.log("✅ 在extension.js中找到extractIntelligentContext函数")

				// 查找函数定义的位置
				const functionMatch = extensionContent.match(/function extractIntelligentContext[^}]+}/)
				if (functionMatch) {
					console.log("📄 函数定义片段:")
					console.log(functionMatch[0].substring(0, 200) + "...")
				}
			} else {
				console.log("❌ 在extension.js中未找到extractIntelligentContext函数")
			}

			// 检查是否包含tree-sitter相关代码
			if (extensionContent.includes("tree-sitter") || extensionContent.includes("Parser")) {
				console.log("✅ 在extension.js中找到tree-sitter相关代码")
			} else {
				console.log("❌ 在extension.js中未找到tree-sitter相关代码")
			}
		}

		console.log("\n🔍 分析实际实现与测试的差异:")
		console.log("1. 测试程序使用的是简化的模拟逻辑")
		console.log("2. 实际的extractIntelligentContext函数使用tree-sitter进行AST解析")
		console.log("3. 由于esbuild打包，无法直接导入和调用单个函数")

		console.log("\n💡 建议的验证方法:")
		console.log("1. 在VS Code扩展环境中进行实际测试")
		console.log("2. 添加调试日志到实际的contextExtractor.ts文件")
		console.log("3. 创建VS Code扩展的单元测试")

		return true
	} catch (error) {
		console.log(`❌ 测试失败: ${error.message}`)
		console.log(`错误堆栈: ${error.stack}`)
		return false
	}
}

// 创建一个VS Code扩展测试建议
function createVSCodeTestSuggestion() {
	console.log("\n📝 VS Code扩展测试建议:")
	console.log("=".repeat(80))

	const testSuggestion = `
// 在VS Code扩展中添加以下测试代码到contextExtractor.ts

export async function testContextExtraction() {
    const testCases = [
        {
            content: \`function test() {
    console.log("test");
}\`,
            cursorLine: 1,
            cursorChar: 4,
            expectedStrategy: 'meaningful-parent'
        }
    ];
    
    for (const testCase of testCases) {
        // 创建临时文档
        const doc = await vscode.workspace.openTextDocument({
            content: testCase.content,
            language: 'javascript'
        });
        
        const position = new vscode.Position(testCase.cursorLine, testCase.cursorChar);
        const result = await extractIntelligentContext(doc, position, 20);
        
        console.log('测试结果:', {
            strategy: result.strategy,
            contextLines: result.contextCode.split('\\n').length,
            expectedStrategy: testCase.expectedStrategy,
            matches: result.strategy === testCase.expectedStrategy
        });
    }
}
`

	console.log(testSuggestion)

	console.log("\n🚀 执行步骤:")
	console.log("1. 将上述测试代码添加到contextExtractor.ts")
	console.log("2. 在扩展的activate函数中调用testContextExtraction()")
	console.log("3. 重新编译并在VS Code中加载扩展")
	console.log("4. 查看VS Code的开发者控制台输出")
}

// 分析当前实现的潜在问题
function analyzeImplementationIssues() {
	console.log("\n🔍 实现分析:")
	console.log("=".repeat(80))

	console.log("基于代码审查，发现的潜在问题:")
	console.log("")

	console.log("1. 📄 contextExtractor.ts 第54-56行的光标位置计算:")
	console.log("   cursorCharInContext: position.line === contextStartPos.line")
	console.log("     ? position.character - contextStartPos.character")
	console.log("     : position.character,")
	console.log("   ⚠️  当光标不在第一行时，应该使用position.character而不是相对位置")
	console.log("")

	console.log("2. 📄 findInterDeclarationContext函数 (第177-246行):")
	console.log("   - 逻辑看起来正确，但需要验证getTopLevelDeclarations的实现")
	console.log("   - tree-sitter的AST节点类型可能与预期不匹配")
	console.log("")

	console.log("3. 📄 getTopLevelDeclarations函数 (第251-291行):")
	console.log("   - 包含了很多AST节点类型，但可能遗漏了某些语言特定的类型")
	console.log("   - 只遍历根节点的直接子节点，这是正确的")
	console.log("")

	console.log("4. 🌳 Tree-sitter解析器:")
	console.log("   - 需要确认解析器是否正确加载和初始化")
	console.log("   - 不同语言的AST节点类型可能不同")
	console.log("")

	console.log("💡 建议的改进:")
	console.log("1. 添加详细的调试日志到每个关键函数")
	console.log("2. 验证tree-sitter解析器的AST输出")
	console.log("3. 测试不同语言的AST节点类型")
	console.log("4. 修复光标位置计算的潜在问题")
}

// 主函数
async function main() {
	const success = await testActualContextExtraction()
	createVSCodeTestSuggestion()
	analyzeImplementationIssues()

	console.log("\n🎯 结论:")
	console.log("=".repeat(80))
	console.log("虽然我们的模拟测试显示了80%的成功率，但要验证实际的")
	console.log("extractIntelligentContext函数是否完全正确，需要:")
	console.log("")
	console.log("1. ✅ 在VS Code扩展环境中进行真实测试")
	console.log("2. ✅ 添加详细的调试日志")
	console.log("3. ✅ 验证tree-sitter解析器的输出")
	console.log("4. ✅ 修复发现的潜在问题")
	console.log("")
	console.log("当前的模拟测试只能验证逻辑框架，不能保证实际实现的正确性。")

	return success
}

if (require.main === module) {
	main()
		.then((success) => {
			process.exit(success ? 0 : 1)
		})
		.catch((error) => {
			console.error("执行失败:", error)
			process.exit(1)
		})
}
