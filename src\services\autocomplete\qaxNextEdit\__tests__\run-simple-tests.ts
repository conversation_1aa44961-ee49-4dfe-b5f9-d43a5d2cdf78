/**
 * Simple test runner for QaxNextEdit tests
 * Runs tests that don't require complex mocking
 */

import { spawn } from "child_process"
import { join } from "path"

const testFiles = ["basic-test.ts", "functional-test.ts", "integration-test.ts", "complete-workflow-test.ts"]

async function runTest(testFile: string): Promise<{ success: boolean; output: string }> {
	return new Promise((resolve) => {
		const testPath = join(__dirname, testFile)
		const child = spawn("npx", ["tsx", testPath], {
			cwd: process.cwd(),
			stdio: "pipe",
		})

		let output = ""
		let errorOutput = ""

		child.stdout?.on("data", (data) => {
			output += data.toString()
		})

		child.stderr?.on("data", (data) => {
			errorOutput += data.toString()
		})

		child.on("close", (code) => {
			resolve({
				success: code === 0,
				output: output + (errorOutput ? "\nErrors:\n" + errorOutput : ""),
			})
		})
	})
}

async function runAllTests() {
	console.log("🚀 Running QaxNextEdit Test Suite")
	console.log("=".repeat(50))

	let totalTests = 0
	let passedTests = 0

	for (const testFile of testFiles) {
		console.log(`\n📋 Running ${testFile}...`)

		try {
			const result = await runTest(testFile)

			if (result.success) {
				console.log(`✅ ${testFile} PASSED`)
				passedTests++
			} else {
				console.log(`❌ ${testFile} FAILED`)
				console.log(result.output)
			}

			totalTests++
		} catch (error) {
			console.log(`💥 ${testFile} ERROR: ${error}`)
			totalTests++
		}
	}

	console.log("\n" + "=".repeat(50))
	console.log("📊 Test Suite Results:")
	console.log(`  ✅ Passed: ${passedTests}`)
	console.log(`  ❌ Failed: ${totalTests - passedTests}`)
	console.log(`  📈 Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`)

	if (passedTests === totalTests) {
		console.log("\n🎉 All tests passed!")
		process.exit(0)
	} else {
		console.log("\n💥 Some tests failed!")
		process.exit(1)
	}
}

runAllTests().catch(console.error)
