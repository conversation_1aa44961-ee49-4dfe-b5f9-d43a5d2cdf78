module.exports = {
	preset: "ts-jest",
	testEnvironment: "node",
	roots: ["<rootDir>"],
	testMatch: ["**/__tests__/**/*.test.ts"],
	transform: {
		"^.+\\.ts$": "ts-jest",
	},
	collectCoverageFrom: [
		"../services/**/*.ts",
		"../types/**/*.ts",
		"../QaxNextEditService.ts",
		"../QaxNextEditUIProvider.ts",
		"../QaxNextEditProvider.ts",
		"!**/__tests__/**",
		"!**/node_modules/**",
	],
	coverageDirectory: "./coverage",
	coverageReporters: ["text", "lcov", "html"],
	coverageThreshold: {
		global: {
			branches: 50,
			functions: 50,
			lines: 50,
			statements: 50,
		},
	},
	setupFilesAfterEnv: ["<rootDir>/jest.setup.js"],
	moduleNameMapping: {
		"^vscode$": "<rootDir>/vscode-mock.js",
	},
	testTimeout: 30000,
}
