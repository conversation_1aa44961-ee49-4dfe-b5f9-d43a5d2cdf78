import { build<PERSON><PERSON><PERSON><PERSON><PERSON>, build<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SingleCompletionHandler } from "../../../api"
import { AutocompleteConfigManager } from "../AutocompleteConfigManager"

/**
 * Shared API Handler Manager for autocomplete services
 * This ensures Next Edit and Autocomplete use the same API handler instance
 */
export class ApiHandlerManager {
	private static instance: ApiHandlerManager | null = null
	private apiHandler: ApiHandler | null = null
	private fimHandler: SingleCompletionHandler | null = null
	private lastSettings: any = null

	private constructor() {}

	static getInstance(): ApiHandlerManager {
		if (!ApiHandlerManager.instance) {
			ApiHandlerManager.instance = new ApiHandlerManager()
		}
		return ApiHandlerManager.instance
	}

	/**
	 * Get current API handler, creating it if needed
	 */
	getApiHandler(): ApiHandler | null {
		const configManager = AutocompleteConfigManager.instance
		const settings = configManager.getSettings()

		// Check if we need to recreate the handler
		if (this.needsRecreation(settings)) {
			this.createHandlers(settings)
			this.lastSettings = { ...settings }
		}

		return this.apiHandler
	}

	/**
	 * Get current FIM handler, creating it if needed
	 */
	getFimHandler(): SingleCompletionHandler | null {
		const configManager = AutocompleteConfigManager.instance
		const settings = configManager.getSettings()

		// Check if we need to recreate the handler
		if (this.needsRecreation(settings)) {
			this.createHandlers(settings)
			this.lastSettings = { ...settings }
		}

		return this.fimHandler
	}

	/**
	 * Force recreation of handlers (useful when settings change)
	 */
	recreateHandlers(): void {
		const configManager = AutocompleteConfigManager.instance
		const settings = configManager.getSettings()
		this.createHandlers(settings)
		this.lastSettings = { ...settings }
		console.log("🚀🔄 ApiHandlerManager: Recreated handlers")
	}

	/**
	 * Check if handlers need to be recreated
	 */
	private needsRecreation(settings: any): boolean {
		if (!this.lastSettings) {
			return true
		}

		// Check if relevant settings have changed
		const relevantFields = [
			"provider",
			"apiKey",
			"apiBaseUrl",
			"modelId",
			"requestTimeoutMs",
			"fim.apiKey",
			"fim.baseUrl",
			"fim.modelId",
		]

		for (const field of relevantFields) {
			const currentValue = this.getNestedValue(settings, field)
			const lastValue = this.getNestedValue(this.lastSettings, field)
			if (currentValue !== lastValue) {
				console.log(`🚀🔄 ApiHandlerManager: Setting changed - ${field}: ${lastValue} -> ${currentValue}`)
				return true
			}
		}

		return false
	}

	/**
	 * Get nested value from object using dot notation
	 */
	private getNestedValue(obj: any, path: string): any {
		return path.split(".").reduce((current, key) => current?.[key], obj)
	}

	/**
	 * Create API and FIM handlers based on settings
	 */
	private createHandlers(settings: any): void {
		// Reset handlers
		this.apiHandler = null
		this.fimHandler = null

		const DEFAULT_MODEL = "google/gemini-2.5-flash-preview-05-20"

		try {
			if (settings.provider === "fim" && settings.fim?.apiKey && settings.fim?.baseUrl) {
				this.fimHandler = buildFimHandler({
					apiKey: settings.fim.apiKey,
					baseUrl: settings.fim.baseUrl,
					requestTimeoutMs: settings.requestTimeoutMs,
					customHeaders: settings.customHeaders,
					maxTokens: settings.maxTokens,
					temperature: settings.temperature,
				})
			} else if (settings.apiKey) {
				this.apiHandler = buildApiHandler({
					apiProvider: "openai", // Use OpenAI-compatible interface
					openAiApiKey: settings.apiKey,
					openAiBaseUrl: settings.apiBaseUrl || "https://aip.b.qianxin-inc.cn/v2",
					openAiModelId: settings.modelId || DEFAULT_MODEL,
					requestTimeoutMs: settings.requestTimeoutMs,
				})
			}
		} catch (error) {
			console.error("🚀❌ ApiHandlerManager: Failed to create handlers:", error)
		}
	}

	/**
	 * Dispose all handlers
	 */
	dispose(): void {
		this.apiHandler = null
		this.fimHandler = null
		this.lastSettings = null
		ApiHandlerManager.instance = null
		console.log("🚀🗑️ ApiHandlerManager: Disposed")
	}
}
