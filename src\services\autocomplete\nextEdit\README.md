# Next Edit Service

Next Edit 是一个智能代码编辑建议服务，它在后台运行，为开发者提供下一步编辑的智能建议。

## 功能特性

### 核心功能
1. **智能建议生成** - 基于文件修改历史和上下文，使用 AI 模型生成下一步编辑建议
2. **实时文件监听** - 监听文件变更，自动触发建议生成
3. **浮动 UI 提示** - 在编辑器中显示建议，支持 Apply/Ignore 操作
4. **防抖机制** - 避免频繁修改导致过多 API 请求
5. **大文件支持** - 对大文件只提取相关上下文，提高性能
6. **建议持久化** - 建议在文件关闭后保留，直到文件再次修改

### 建议类型
- **代码补全** - 完成未完成的实现、添加缺失的方法/属性
- **错误处理** - 添加缺失的错误处理、验证或异常处理
- **类型安全** - 添加类型注解、空值检查或改进类型使用
- **测试** - 添加单元测试、集成测试或测试用例
- **文档** - 添加缺失的文档字符串、注释或内联文档
- **Bug 修复** - 修复明显的 bug 或逻辑错误

## 使用方法

### 自动触发
1. 在支持的文件中进行编辑
2. 服务会自动检测文件变更并生成建议
3. 如果有建议，会自动跳转到建议位置并选中相关代码
4. 在代码上方显示两行浮动条：
   - **第一行**：🔧 扩展图标 + [Apply (Ctrl+;)] | [Ignore] | [Next] | [Explain] 按钮 + 计数器
   - **第二行**：💡 详细的修改理由说明
5. 支持键盘快捷键：
   - `Ctrl+;` - 应用建议（主要快捷键）
6. 点击按钮操作：
   - **Apply** - 应用当前建议
   - **Ignore** - 忽略当前建议，移到下一个
   - **Next** - 跳转到下一个建议
   - **Explain** - 显示详细说明文档

### 手动命令
- `nextEdit.toggle` - 启用/禁用 Next Edit 服务
- `nextEdit.showSuggestions` - 显示当前文件的建议
- `nextEdit.clearSuggestions` - 清除当前文件的建议
- `nextEdit.nextSuggestion` - 跳转到下一个建议
- `nextEdit.previousSuggestion` - 跳转到上一个建议

### 浮动条 UI
- 在代码修改位置的上方显示两行浮动条
- **第一行**：操作按钮区域
  - 🔧 扩展图标
  - [Apply (Ctrl+;)] - 应用建议，支持快捷键
  - [Ignore] - 忽略建议
  - [Next] - 下一个建议
  - [Explain] - 显示详细说明
  - (1/3) - 当前建议计数
- **第二行**：💡 修改理由说明
- 浮动条直接显示在需要修改的代码上方，不遮挡编辑区域
- 点击按钮直接执行对应操作，无需额外弹窗

### 状态栏
- 显示当前建议的信息：`💡 Next Edit: 1/3 - Add error handling`

## 配置

Next Edit 服务使用与自动完成相同的 API 配置：

```json
{
  "qax-code.autocomplete.enabled": true,
  "qax-code.autocomplete.apiKey": "your-api-key",
  "qax-code.autocomplete.apiBaseUrl": "https://api.example.com/v1",
  "qax-code.autocomplete.modelId": "your-model-id"
}
```

### 默认配置
- **防抖延迟**: 2000ms
- **最大建议数**: 5个
- **上下文窗口**: 前后200行
- **启用状态**: 跟随自动完成设置

## 架构设计

### 核心组件
1. **NextEditService** - 主服务类，管理整个生命周期
2. **NextEditContextCollector** - 收集文件修改上下文
3. **NextEditRecommendationEngine** - 处理 AI 模型交互
4. **NextEditUIProvider** - 管理浮动 UI 组件
5. **NextEditProvider** - 服务注册和配置管理

### 数据流
```
文件修改 → 上下文收集 → AI 推荐 → UI 展示 → 用户操作 → 应用/忽略
```

### 集成点
- **AutocompleteTaskManager** - 同步禁用状态
- **VSCode Events** - 文件变更、编辑器焦点
- **API Handler** - 复用现有 AI 基础设施

## 开发说明

### 文件结构
```
src/services/autocomplete/nextEdit/
├── types/
│   └── NextEditTypes.ts          # 类型定义
├── NextEditService.ts            # 主服务类
├── NextEditContextCollector.ts   # 上下文收集器
├── NextEditRecommendationEngine.ts # AI 推荐引擎
├── NextEditUIProvider.ts         # UI 提供者
├── NextEditProvider.ts           # 服务提供者
└── README.md                     # 说明文档
```

### 扩展点
1. **新建议类型** - 在 `NextEditCategory` 枚举中添加
2. **自定义 UI** - 扩展 `NextEditUIProvider` 类
3. **上下文增强** - 扩展 `NextEditContextCollector` 类
4. **AI 模型集成** - 扩展 `NextEditRecommendationEngine` 类

## 注意事项

1. **性能考虑** - 大文件只提取局部上下文，避免过大的 API 请求
2. **API 限制** - 使用防抖机制避免频繁请求
3. **用户体验** - 建议应该保守且安全，避免破坏性修改
4. **错误处理** - 所有 API 调用都有错误处理，失败时不影响编辑体验

## 故障排除

### 常见问题
1. **建议不显示** - 检查 API 配置和网络连接
2. **建议质量差** - 调整上下文窗口大小或模型参数
3. **性能问题** - 增加防抖延迟或减少最大建议数

### 调试信息
服务会在控制台输出详细的调试信息：
- `🚀🔍 NextEdit: Requesting suggestions for <file>`
- `🚀✨ NextEdit: Generated <count> suggestions`
- `🚀🔍 NextEditService enabled/disabled`

## 未来改进

1. **学习用户偏好** - 根据用户的 Apply/Ignore 行为调整建议
2. **多语言支持** - 扩展到更多编程语言
3. **团队协作** - 基于团队代码风格生成建议
4. **性能优化** - 缓存和增量更新机制
5. **高级 UI** - 更丰富的预览和编辑界面
