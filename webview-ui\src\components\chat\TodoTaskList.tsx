import React, { useState } from "react"
import { VSCodeButton } from "@vscode/webview-ui-toolkit/react"
import styled from "styled-components"

// Task status enum
export enum TaskStatus {
	NEW = "new",
	IN_PROGRESS = "in_progress",
	COMPLETED = "completed",
	FAILED = "failed",
}

// Task interface
export interface TodoTask {
	id: string
	title: string
	description?: string
	status: TaskStatus
	createdAt: Date
	updatedAt: Date
}

// Tab types
export enum TabType {
	TASK_LIST = "task_list",
	CODE_LIST = "code_list",
}

// Styled components
const TodoContainer = styled.div<{ isExpanded: boolean }>`
	position: relative;
	background: var(--vscode-input-background);
	border: 1px solid var(--vscode-input-border);
	border-radius: 2px 2px 0 0;
	margin: 0 15px;
	margin-bottom: 0;
	transition: all 0.2s ease-in-out;
	max-height: ${(props) => (props.isExpanded ? "280px" : "32px")};
	overflow: hidden;
	box-sizing: border-box;
`

const TodoHeader = styled.div<{ isExpanded: boolean }>`
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 4px 8px;
	background: var(--vscode-input-background);
	border-bottom: ${(props) => (props.isExpanded ? "1px solid var(--vscode-input-border)" : "none")};
	user-select: none;
	min-height: 24px;
`

const TodoHeaderLeft = styled.div`
	display: flex;
	align-items: center;
	gap: 4px;
`

const TodoHeaderCenter = styled.div`
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 12px;
	font-weight: 500;
	color: var(--vscode-foreground);
`

const TodoHeaderRight = styled.div`
	display: flex;
	align-items: center;
	gap: 4px;
`

const HeaderButton = styled(VSCodeButton)`
	padding: 2px 4px !important;
	height: 20px !important;
	min-width: 20px !important;
	margin: 0 !important;
`

const TabButton = styled(VSCodeButton)<{ isActive: boolean }>`
	padding: 2px 4px !important;
	height: 20px !important;
	min-width: 20px !important;
	margin: 0 !important;
	background: ${(props) => (props.isActive ? "var(--vscode-button-background)" : "transparent")} !important;
	color: ${(props) => (props.isActive ? "var(--vscode-button-foreground)" : "var(--vscode-foreground)")} !important;
`

const TodoList = styled.div`
	padding: 8px 12px;
	max-height: 240px;
	overflow-y: auto;
`

const TaskItem = styled.div`
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 6px 0;
	border-bottom: 1px solid var(--vscode-widget-border);

	&:last-child {
		border-bottom: none;
	}

	&:hover .task-actions {
		opacity: 1;
	}
`

const TaskIcon = styled.span<{ status: TaskStatus }>`
	width: 16px;
	height: 16px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 12px;
	flex-shrink: 0;

	color: ${(props) => {
		switch (props.status) {
			case TaskStatus.NEW:
				return "var(--vscode-charts-blue)"
			case TaskStatus.IN_PROGRESS:
				return "var(--vscode-charts-yellow)"
			case TaskStatus.COMPLETED:
				return "var(--vscode-charts-green)"
			case TaskStatus.FAILED:
				return "var(--vscode-errorForeground)"
			default:
				return "var(--vscode-foreground)"
		}
	}};
`

const TaskContent = styled.div`
	flex: 1;
	min-width: 0;
`

const TaskTitle = styled.div`
	font-size: 12px;
	color: var(--vscode-foreground);
	font-weight: 500;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
`

const TaskDescription = styled.div`
	font-size: 11px;
	color: var(--vscode-descriptionForeground);
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-top: 2px;
`

const TaskActions = styled.div`
	display: flex;
	gap: 4px;
	opacity: 0;
	transition: opacity 0.2s ease-in-out;
`

const TaskActionButton = styled(VSCodeButton)`
	padding: 2px 4px !important;
	height: 20px !important;
	min-width: 20px !important;
`

const EmptyState = styled.div`
	text-align: center;
	padding: 20px;
	color: var(--vscode-descriptionForeground);
	font-size: 11px;
`

// Helper function to get task icon based on status
const getTaskIcon = (status: TaskStatus): string => {
	switch (status) {
		case TaskStatus.NEW:
			return "codicon-circle-outline"
		case TaskStatus.IN_PROGRESS:
			return "codicon-sync"
		case TaskStatus.COMPLETED:
			return "codicon-check"
		case TaskStatus.FAILED:
			return "codicon-error"
		default:
			return "codicon-circle-outline"
	}
}

interface TodoTaskListProps {
	tasks?: TodoTask[]
	onTaskAdd?: (task: Omit<TodoTask, "id" | "createdAt" | "updatedAt">) => void
	onTaskUpdate?: (taskId: string, updates: Partial<TodoTask>) => void
	onTaskDelete?: (taskId: string) => void
}

const TodoTaskList: React.FC<TodoTaskListProps> = ({ tasks = [], onTaskAdd, onTaskUpdate, onTaskDelete }) => {
	const [isExpanded, setIsExpanded] = useState(false)
	const [activeTab, setActiveTab] = useState<TabType>(TabType.TASK_LIST)

	const toggleExpanded = () => {
		setIsExpanded(!isExpanded)
	}

	const handleAddTask = () => {
		const title = prompt("请输入任务标题:")
		if (title && title.trim()) {
			const description = prompt("请输入任务描述 (可选):")
			onTaskAdd?.({
				title: title.trim(),
				description: description?.trim() || undefined,
				status: TaskStatus.NEW,
			})
		}
	}

	const handleToggleTaskStatus = (taskId: string, currentStatus: TaskStatus) => {
		let newStatus: TaskStatus
		switch (currentStatus) {
			case TaskStatus.NEW:
				newStatus = TaskStatus.IN_PROGRESS
				break
			case TaskStatus.IN_PROGRESS:
				newStatus = TaskStatus.COMPLETED
				break
			case TaskStatus.COMPLETED:
				newStatus = TaskStatus.NEW
				break
			case TaskStatus.FAILED:
				newStatus = TaskStatus.NEW
				break
			default:
				newStatus = TaskStatus.NEW
		}
		onTaskUpdate?.(taskId, { status: newStatus, updatedAt: new Date() })
	}

	const handleDeleteTask = (taskId: string) => {
		if (confirm("确定要删除这个任务吗？")) {
			onTaskDelete?.(taskId)
		}
	}

	const getTabTitle = () => {
		switch (activeTab) {
			case TabType.TASK_LIST:
				return "Current Task List"
			case TabType.CODE_LIST:
				return "Code List"
			default:
				return "Current Task List"
		}
	}

	return (
		<TodoContainer isExpanded={isExpanded}>
			<TodoHeader isExpanded={isExpanded}>
				<TodoHeaderLeft>
					<HeaderButton appearance="icon" onClick={toggleExpanded} aria-label={isExpanded ? "收起列表" : "展开列表"}>
						<span
							className={`codicon ${isExpanded ? "codicon-chevron-down" : "codicon-chevron-right"}`}
							style={{ fontSize: "12px" }}
						/>
					</HeaderButton>

					<TabButton
						appearance="icon"
						isActive={activeTab === TabType.TASK_LIST}
						onClick={() => setActiveTab(TabType.TASK_LIST)}
						aria-label="任务列表">
						<span className="codicon codicon-checklist" style={{ fontSize: "12px" }} />
					</TabButton>

					<TabButton
						appearance="icon"
						isActive={activeTab === TabType.CODE_LIST}
						onClick={() => setActiveTab(TabType.CODE_LIST)}
						aria-label="代码列表">
						<span className="codicon codicon-file-code" style={{ fontSize: "12px" }} />
					</TabButton>
				</TodoHeaderLeft>

				<TodoHeaderCenter>{getTabTitle()}</TodoHeaderCenter>

				<TodoHeaderRight>
					<HeaderButton appearance="icon" onClick={handleAddTask} disabled={!onTaskAdd} aria-label="添加任务">
						<span className="codicon codicon-add" style={{ fontSize: "12px" }} />
					</HeaderButton>

					<HeaderButton appearance="icon" aria-label="更多选项">
						<span className="codicon codicon-kebab-vertical" style={{ fontSize: "12px" }} />
					</HeaderButton>
				</TodoHeaderRight>
			</TodoHeader>

			{isExpanded && (
				<TodoList>
					{activeTab === TabType.TASK_LIST && (
						<>
							{tasks.length === 0 ? (
								<EmptyState>暂无任务</EmptyState>
							) : (
								tasks.map((task) => (
									<TaskItem key={task.id}>
										<TaskIcon
											status={task.status}
											onClick={() => handleToggleTaskStatus(task.id, task.status)}
											style={{ cursor: onTaskUpdate ? "pointer" : "default" }}>
											<span className={`codicon ${getTaskIcon(task.status)}`} />
										</TaskIcon>
										<TaskContent>
											<TaskTitle>{task.title}</TaskTitle>
											{task.description && <TaskDescription>{task.description}</TaskDescription>}
										</TaskContent>
										<TaskActions className="task-actions">
											<TaskActionButton
												appearance="icon"
												onClick={() => handleDeleteTask(task.id)}
												disabled={!onTaskDelete}
												title="删除任务">
												<span className="codicon codicon-trash" style={{ fontSize: "10px" }} />
											</TaskActionButton>
										</TaskActions>
									</TaskItem>
								))
							)}
						</>
					)}

					{activeTab === TabType.CODE_LIST && <EmptyState>代码列表功能即将推出</EmptyState>}
				</TodoList>
			)}
		</TodoContainer>
	)
}

export default TodoTaskList
