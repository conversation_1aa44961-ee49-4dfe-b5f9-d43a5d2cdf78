# QaxNextEdit 编辑会话跟踪和防抖改进

## 问题分析

用户反馈的核心问题：
1. **中间状态检测**：系统检测到 `showAddNeEventModal` 等中间输入状态，而不是完整的编辑意图
2. **字符级别检测**：LSP 和其他检测方法仍在进行字符级别的检测
3. **缺乏编辑会话概念**：系统没有理解用户的完整编辑会话，只是对每个变更进行独立分析

## 解决方案

### 1. 编辑会话跟踪系统

实现了完整的编辑会话跟踪机制：

#### 新增配置项
```typescript
interface QaxNextEditConfig {
    // 原有配置...
    debounceDelayMs: 1500,              // 增加防抖延迟到1.5秒
    editSessionTimeoutMs: 3000,         // 编辑会话超时：3秒
    minEditSessionDurationMs: 500,      // 最小会话持续时间：500ms
    enableEditSessionTracking: true,    // 启用编辑会话跟踪
}
```

#### 编辑会话数据结构
```typescript
interface EditSession {
    startTime: number           // 会话开始时间
    lastEditTime: number       // 最后编辑时间
    initialContent: string     // 初始内容（会话开始时的内容）
    editCount: number          // 编辑次数
    isActive: boolean          // 会话是否活跃
}
```

### 2. 工作原理

#### 编辑会话生命周期
1. **会话开始**：用户开始编辑时创建新会话，记录初始内容
2. **会话维护**：每次编辑重置超时计时器，更新编辑计数
3. **会话结束**：超时后自动结束会话，触发基于完整会话的分析

#### 智能分析调度
```typescript
// 传统方式：每次变更都触发防抖分析
onChange -> debounce(800ms) -> analyze(beforeContent, afterContent)

// 新方式：基于编辑会话的分析
onChange -> trackSession -> resetTimeout(3000ms) -> 
sessionEnd -> analyze(initialContent, finalContent)
```

### 3. 核心改进

#### A. 符号级别检测优先级
- **符号检测**：优先级 100（最高）
- **AST检测**：优先级 90
- **改进文本差异**：优先级 80
- **LSP检测**：优先级 70
- **基础文本差异**：优先级 60

#### B. 智能合并逻辑
```typescript
// 按检测方法质量排序，过滤冗余的字符级别检测
private mergeDetectedChanges(changes: QaxChangeDetection[]): QaxChangeDetection[] {
    // 1. 按方法质量和置信度排序
    // 2. 检测重叠和冗余
    // 3. 优先保留高质量的符号级别检测
    // 4. 过滤掉字符级别的冗余检测
}
```

#### C. 编辑会话感知分析
```typescript
private scheduleSessionAwareAnalysis(filePath: string, context: QaxAnalysisContext): void {
    const session = this.editSessions.get(filePath)
    
    if (session && session.isActive) {
        // 会话进行中，延迟分析直到会话结束
        this.resetSessionTimeout(filePath)
    } else {
        // 没有活跃会话，使用传统防抖
        this.debouncedAnalyze(context)
    }
}
```

### 4. 预期效果

#### 用户场景：`showAddNextEventModal` → `showAddNewEventModal`

**之前的行为**：
```
输入 "showAddN" -> 分析 -> 检测到字符变化
输入 "showAddNe" -> 分析 -> 检测到字符变化  
输入 "showAddNew" -> 分析 -> 检测到字符变化
...
```

**现在的行为**：
```
开始编辑 "showAddNextEventModal" -> 创建编辑会话
输入过程中的所有变更 -> 重置会话超时，不立即分析
停止编辑3秒后 -> 会话结束 -> 分析完整变更：
  beforeContent: "showAddNextEventModal"
  afterContent: "showAddNewEventModal"
  结果: 检测到完整的符号重命名
```

### 5. 技术实现细节

#### 会话跟踪方法
```typescript
private trackEditSession(filePath: string, beforeContent: string, afterContent: string): void
private resetSessionTimeout(filePath: string): void
private endEditSession(filePath: string): void
```

#### 检测方法质量评分
```typescript
private getDetectionMethodScore(change: QaxChangeDetection): number {
    switch (method) {
        case 'symbol_based': return 100  // 最高优先级
        case 'ast': return 90
        case 'lsp': return 70           // 降低LSP优先级
        // ...
    }
}
```

#### 冗余检测过滤
```typescript
private shouldIncludeChange(change: QaxChangeDetection, existingChanges: QaxChangeDetection[]): boolean
private areChangesOverlapping(change1: QaxChangeDetection, change2: QaxChangeDetection): boolean
private isCharacterLevelRedundancy(change1: QaxChangeDetection, change2: QaxChangeDetection): boolean
```

### 6. 配置调整

- **防抖延迟**：从 800ms 增加到 1500ms
- **会话超时**：3000ms（3秒无编辑后结束会话）
- **最小会话时长**：500ms（过短的会话不触发分析）

### 7. 向后兼容性

- ✅ 可以通过 `enableEditSessionTracking: false` 禁用新功能
- ✅ 禁用时回退到传统防抖机制
- ✅ 所有现有API保持不变

## 总结

这个实现解决了用户提出的核心问题：

1. **✅ 跳过中间状态**：编辑会话跟踪确保只分析完整的编辑意图
2. **✅ 符号级别检测**：优先使用符号级别检测，过滤字符级别冗余
3. **✅ 智能防抖**：基于编辑会话的智能分析调度
4. **✅ 完整变更检测**：从编辑开始到结束的完整内容比较

现在系统能够正确识别从 `showAddNextEventModal` 到 `showAddNewEventModal` 的完整符号重命名，而不是中间的字符级别变化。
