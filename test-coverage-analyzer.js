#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Analyzes test coverage for QaxLSPService and provides detailed reporting
 */
class TestCoverageAnalyzer {
    constructor() {
        this.serviceFile = 'src/services/autocomplete/qaxNextEdit/services/QaxLSPService.ts';
        this.testFile = 'src/services/autocomplete/qaxNextEdit/services/__tests__/QaxLSPService.test.ts';
        this.coverageFile = 'coverage/lcov.info';
    }

    async analyze() {
        console.log('🔍 Analyzing QaxLSPService Test Coverage\n');
        
        // Analyze source code
        const sourceAnalysis = this.analyzeSourceCode();
        console.log('📊 Source Code Analysis:');
        console.log(`   Total Methods: ${sourceAnalysis.methods.length}`);
        console.log(`   Public Methods: ${sourceAnalysis.publicMethods.length}`);
        console.log(`   Private Methods: ${sourceAnalysis.privateMethods.length}`);
        console.log(`   Static Methods: ${sourceAnalysis.staticMethods.length}`);
        console.log(`   Lines of Code: ${sourceAnalysis.totalLines}\n`);

        // Analyze test code
        const testAnalysis = this.analyzeTestCode();
        console.log('🧪 Test Code Analysis:');
        console.log(`   Test Suites: ${testAnalysis.testSuites.length}`);
        console.log(`   Total Tests: ${testAnalysis.totalTests}`);
        console.log(`   Test Categories: ${testAnalysis.categories.join(', ')}`);
        console.log(`   Lines of Test Code: ${testAnalysis.totalLines}\n`);

        // Calculate coverage metrics
        const coverage = this.calculateCoverage(sourceAnalysis, testAnalysis);
        console.log('📈 Coverage Metrics:');
        console.log(`   Method Coverage: ${coverage.methodCoverage.toFixed(1)}%`);
        console.log(`   Public Method Coverage: ${coverage.publicMethodCoverage.toFixed(1)}%`);
        console.log(`   Private Method Coverage: ${coverage.privateMethodCoverage.toFixed(1)}%`);
        console.log(`   Edge Case Coverage: ${coverage.edgeCaseCoverage.toFixed(1)}%`);
        console.log(`   Error Handling Coverage: ${coverage.errorHandlingCoverage.toFixed(1)}%\n`);

        // Generate recommendations
        const recommendations = this.generateRecommendations(sourceAnalysis, testAnalysis, coverage);
        if (recommendations.length > 0) {
            console.log('💡 Recommendations for 95%+ Coverage:');
            recommendations.forEach((rec, index) => {
                console.log(`   ${index + 1}. ${rec}`);
            });
            console.log();
        }

        // Check if target coverage is met
        const overallCoverage = (coverage.methodCoverage + coverage.edgeCaseCoverage + coverage.errorHandlingCoverage) / 3;
        if (overallCoverage >= 95) {
            console.log('✅ TARGET COVERAGE ACHIEVED! (95%+)');
            console.log('🎉 QaxLSPService is comprehensively tested!');
        } else {
            console.log(`❌ Coverage below target: ${overallCoverage.toFixed(1)}% (need 95%+)`);
            console.log('🔧 Please implement the recommendations above');
        }

        return {
            sourceAnalysis,
            testAnalysis,
            coverage,
            recommendations,
            targetMet: overallCoverage >= 95
        };
    }

    analyzeSourceCode() {
        if (!fs.existsSync(this.serviceFile)) {
            throw new Error(`Source file not found: ${this.serviceFile}`);
        }

        const content = fs.readFileSync(this.serviceFile, 'utf8');
        const lines = content.split('\n');
        
        const methods = [];
        const publicMethods = [];
        const privateMethods = [];
        const staticMethods = [];

        // Extract methods using regex
        const methodRegex = /^\s*(public|private|static)?\s*(async\s+)?([\w]+)\s*\([^)]*\)\s*[:{]/gm;
        let match;
        
        while ((match = methodRegex.exec(content)) !== null) {
            const visibility = match[1] || 'public';
            const isAsync = !!match[2];
            const methodName = match[3];
            
            if (methodName === 'constructor' || methodName === 'class') continue;
            
            const method = {
                name: methodName,
                visibility,
                isAsync,
                isStatic: visibility === 'static'
            };
            
            methods.push(method);
            
            if (visibility === 'private') {
                privateMethods.push(method);
            } else if (visibility === 'static') {
                staticMethods.push(method);
            } else {
                publicMethods.push(method);
            }
        }

        return {
            methods,
            publicMethods,
            privateMethods,
            staticMethods,
            totalLines: lines.length
        };
    }

    analyzeTestCode() {
        if (!fs.existsSync(this.testFile)) {
            throw new Error(`Test file not found: ${this.testFile}`);
        }

        const content = fs.readFileSync(this.testFile, 'utf8');
        const lines = content.split('\n');
        
        // Extract test suites
        const testSuites = [];
        const suiteRegex = /describe\s*\(\s*['"`]([^'"`]+)['"`]/g;
        let match;
        
        while ((match = suiteRegex.exec(content)) !== null) {
            testSuites.push(match[1]);
        }

        // Count total tests
        const testRegex = /test\s*\(\s*['"`]([^'"`]+)['"`]/g;
        const tests = [];
        
        while ((match = testRegex.exec(content)) !== null) {
            tests.push(match[1]);
        }

        // Categorize tests
        const categories = new Set();
        tests.forEach(test => {
            if (test.includes('error') || test.includes('throw') || test.includes('fail')) {
                categories.add('Error Handling');
            }
            if (test.includes('null') || test.includes('undefined') || test.includes('empty')) {
                categories.add('Edge Cases');
            }
            if (test.includes('should return') || test.includes('should get')) {
                categories.add('Happy Path');
            }
            if (test.includes('mock') || test.includes('spy')) {
                categories.add('Mocking');
            }
        });

        return {
            testSuites,
            totalTests: tests.length,
            tests,
            categories: Array.from(categories),
            totalLines: lines.length
        };
    }

    calculateCoverage(sourceAnalysis, testAnalysis) {
        // Method coverage calculation
        const testedMethods = new Set();
        testAnalysis.tests.forEach(test => {
            sourceAnalysis.methods.forEach(method => {
                if (test.toLowerCase().includes(method.name.toLowerCase()) ||
                    testAnalysis.testSuites.some(suite => suite.includes(method.name))) {
                    testedMethods.add(method.name);
                }
            });
        });

        const methodCoverage = (testedMethods.size / sourceAnalysis.methods.length) * 100;
        
        // Public method coverage
        const testedPublicMethods = sourceAnalysis.publicMethods.filter(m => testedMethods.has(m.name));
        const publicMethodCoverage = sourceAnalysis.publicMethods.length > 0 ? 
            (testedPublicMethods.length / sourceAnalysis.publicMethods.length) * 100 : 100;
        
        // Private method coverage
        const testedPrivateMethods = sourceAnalysis.privateMethods.filter(m => testedMethods.has(m.name));
        const privateMethodCoverage = sourceAnalysis.privateMethods.length > 0 ? 
            (testedPrivateMethods.length / sourceAnalysis.privateMethods.length) * 100 : 100;

        // Edge case coverage (based on test categories and patterns)
        const edgeCaseTests = testAnalysis.tests.filter(test => 
            test.includes('null') || test.includes('undefined') || test.includes('empty') ||
            test.includes('invalid') || test.includes('boundary') || test.includes('edge')
        );
        const edgeCaseCoverage = Math.min((edgeCaseTests.length / sourceAnalysis.methods.length) * 100, 100);

        // Error handling coverage
        const errorTests = testAnalysis.tests.filter(test => 
            test.includes('error') || test.includes('throw') || test.includes('fail') ||
            test.includes('reject') || test.includes('exception')
        );
        const errorHandlingCoverage = Math.min((errorTests.length / sourceAnalysis.methods.length) * 100, 100);

        return {
            methodCoverage,
            publicMethodCoverage,
            privateMethodCoverage,
            edgeCaseCoverage,
            errorHandlingCoverage,
            testedMethods: Array.from(testedMethods)
        };
    }

    generateRecommendations(sourceAnalysis, testAnalysis, coverage) {
        const recommendations = [];

        // Check for untested methods
        const untestedMethods = sourceAnalysis.methods.filter(m => 
            !coverage.testedMethods.includes(m.name)
        );
        
        if (untestedMethods.length > 0) {
            recommendations.push(
                `Add tests for untested methods: ${untestedMethods.map(m => m.name).join(', ')}`
            );
        }

        // Check edge case coverage
        if (coverage.edgeCaseCoverage < 90) {
            recommendations.push(
                'Add more edge case tests (null/undefined inputs, boundary conditions, empty arrays)'
            );
        }

        // Check error handling coverage
        if (coverage.errorHandlingCoverage < 90) {
            recommendations.push(
                'Add more error handling tests (LSP failures, network errors, invalid inputs)'
            );
        }

        // Check private method coverage
        if (coverage.privateMethodCoverage < 85) {
            recommendations.push(
                'Add tests for private methods using (service as any).methodName() pattern'
            );
        }

        // Check for specific test patterns
        const hasAsyncTests = testAnalysis.tests.some(test => test.includes('async') || test.includes('await'));
        if (!hasAsyncTests && sourceAnalysis.methods.some(m => m.isAsync)) {
            recommendations.push(
                'Add specific async/await testing patterns for async methods'
            );
        }

        return recommendations;
    }
}

// Run the analyzer
if (require.main === module) {
    const analyzer = new TestCoverageAnalyzer();
    analyzer.analyze().catch(error => {
        console.error('❌ Analysis failed:', error.message);
        process.exit(1);
    });
}

module.exports = TestCoverageAnalyzer;
