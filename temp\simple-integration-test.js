"use strict"
/**
 * Simple integration test for QaxNextEdit with NextEdit
 */
var __createBinding =
	(this && this.__createBinding) ||
	(Object.create
		? function (o, m, k, k2) {
				if (k2 === undefined) k2 = k
				var desc = Object.getOwnPropertyDescriptor(m, k)
				if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
					desc = {
						enumerable: true,
						get: function () {
							return m[k]
						},
					}
				}
				Object.defineProperty(o, k2, desc)
			}
		: function (o, m, k, k2) {
				if (k2 === undefined) k2 = k
				o[k2] = m[k]
			})
var __setModuleDefault =
	(this && this.__setModuleDefault) ||
	(Object.create
		? function (o, v) {
				Object.defineProperty(o, "default", { enumerable: true, value: v })
			}
		: function (o, v) {
				o["default"] = v
			})
var __importStar =
	(this && this.__importStar) ||
	function (mod) {
		if (mod && mod.__esModule) return mod
		var result = {}
		if (mod != null)
			for (var k in mod)
				if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k)
		__setModuleDefault(result, mod)
		return result
	}
Object.defineProperty(exports, "__esModule", { value: true })
const assert = __importStar(require("assert"))
// Test the conversion logic directly
function testQaxToNextEditConversion() {
	console.log("🧪 Testing QaxNextEdit to NextEdit conversion...")
	// Mock types
	let QaxChangeType
	;(function (QaxChangeType) {
		QaxChangeType["VARIABLE_RENAME"] = "variable_rename"
		QaxChangeType["FUNCTION_CALL_DELETION"] = "function_call_deletion"
		QaxChangeType["VARIABLE_DELETION"] = "variable_deletion"
	})(QaxChangeType || (QaxChangeType = {}))
	let NextEditType
	;(function (NextEditType) {
		NextEditType["ADD"] = "add"
		NextEditType["MODIFY"] = "modify"
		NextEditType["DELETE"] = "delete"
	})(NextEditType || (NextEditType = {}))
	// Conversion function
	function convertQaxToNextEdit(qaxSuggestion) {
		let type = NextEditType.MODIFY
		let description = qaxSuggestion.description
		if (qaxSuggestion.relatedChange) {
			const change = qaxSuggestion.relatedChange
			if (change.type === QaxChangeType.VARIABLE_RENAME) {
				type = NextEditType.MODIFY
				description = `Change: ${change.oldValue} ➜ ${change.newValue || "updated"}`
			} else if (change.type === QaxChangeType.FUNCTION_CALL_DELETION || change.type === QaxChangeType.VARIABLE_DELETION) {
				type = NextEditType.DELETE
				description = `Remove: ${change.oldValue}`
			}
		}
		const anchor = `line_${qaxSuggestion.range.start.line + 1}_char_${qaxSuggestion.range.start.character}`
		return {
			id: qaxSuggestion.id,
			type,
			description,
			location: {
				anchor,
				position: type === NextEditType.DELETE ? "replace" : type === NextEditType.ADD ? "after" : "replace",
			},
			patch: {
				oldContent: "",
				newContent: qaxSuggestion.suggestedEdit?.newText || "",
			},
			reasoning: `QaxNextEdit detected ${qaxSuggestion.changeType} with ${Math.round((qaxSuggestion.relatedChange?.confidence || 0.8) * 100)}% confidence`,
			filePath: qaxSuggestion.filePath,
			createdAt: new Date(),
		}
	}
	// Test cases
	const testCases = [
		{
			name: "Variable rename",
			qaxSuggestion: {
				id: "test-1",
				filePath: "test.ts",
				range: { start: { line: 0, character: 4 }, end: { line: 0, character: 11 } },
				description: "Update variable name",
				changeType: QaxChangeType.VARIABLE_RENAME,
				priority: 8,
				suggestedEdit: {
					range: { start: { line: 0, character: 4 }, end: { line: 0, character: 11 } },
					newText: "newName",
					description: "Rename variable",
				},
				relatedChange: {
					type: QaxChangeType.VARIABLE_RENAME,
					filePath: "test.ts",
					range: { start: { line: 0, character: 4 }, end: { line: 0, character: 11 } },
					oldValue: "oldName",
					newValue: "newName",
					confidence: 0.9,
				},
			},
			expected: {
				type: NextEditType.MODIFY,
				description: "Change: oldName ➜ newName",
				anchor: "line_1_char_4",
				position: "replace",
				newContent: "newName",
			},
		},
		{
			name: "Function call deletion",
			qaxSuggestion: {
				id: "test-2",
				filePath: "test.ts",
				range: { start: { line: 5, character: 0 }, end: { line: 5, character: 10 } },
				description: "Remove function call",
				changeType: QaxChangeType.FUNCTION_CALL_DELETION,
				priority: 6,
				relatedChange: {
					type: QaxChangeType.FUNCTION_CALL_DELETION,
					filePath: "test.ts",
					range: { start: { line: 5, character: 0 }, end: { line: 5, character: 10 } },
					oldValue: "oldFunc()",
					confidence: 0.85,
				},
			},
			expected: {
				type: NextEditType.DELETE,
				description: "Remove: oldFunc()",
				anchor: "line_6_char_0",
				position: "replace",
				newContent: "",
			},
		},
	]
	// Run tests
	let passed = 0
	let failed = 0
	testCases.forEach((testCase, index) => {
		try {
			console.log(`\n  Test ${index + 1}: ${testCase.name}`)
			const result = convertQaxToNextEdit(testCase.qaxSuggestion)
			// Verify results
			assert.strictEqual(result.type, testCase.expected.type, "Type mismatch")
			assert.strictEqual(result.description, testCase.expected.description, "Description mismatch")
			assert.strictEqual(result.location.anchor, testCase.expected.anchor, "Anchor mismatch")
			assert.strictEqual(result.location.position, testCase.expected.position, "Position mismatch")
			assert.strictEqual(result.patch.newContent, testCase.expected.newContent, "New content mismatch")
			assert.ok(result.reasoning?.includes("QaxNextEdit detected"), "Reasoning missing")
			assert.strictEqual(result.filePath, testCase.qaxSuggestion.filePath, "File path mismatch")
			console.log(`    ✅ PASSED`)
			passed++
		} catch (error) {
			console.log(`    ❌ FAILED: ${error.message}`)
			failed++
		}
	})
	console.log(`\n📊 Conversion Test Results:`)
	console.log(`  ✅ Passed: ${passed}`)
	console.log(`  ❌ Failed: ${failed}`)
	console.log(`  📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`)
	return failed === 0
}
// Test configuration detection
function testConfigurationDetection() {
	console.log("\n🧪 Testing configuration detection...")
	// Mock VS Code configuration
	const mockConfig = {
		get: (key, defaultValue) => {
			const configs = {
				useQaxNextEdit: true,
				enabled: true,
				enableLSPIntegration: true,
				debounceDelayMs: 1500,
			}
			return configs[key] !== undefined ? configs[key] : defaultValue
		},
	}
	try {
		// Test configuration values
		assert.strictEqual(mockConfig.get("useQaxNextEdit"), true, "useQaxNextEdit should be true")
		assert.strictEqual(mockConfig.get("enabled"), true, "enabled should be true")
		assert.strictEqual(mockConfig.get("enableLSPIntegration"), true, "enableLSPIntegration should be true")
		assert.strictEqual(mockConfig.get("debounceDelayMs"), 1500, "debounceDelayMs should be 1500")
		assert.strictEqual(mockConfig.get("nonExistent", "default"), "default", "should return default for non-existent key")
		console.log("  ✅ Configuration detection PASSED")
		return true
	} catch (error) {
		console.log(`  ❌ Configuration detection FAILED: ${error.message}`)
		return false
	}
}
// Test suggestion format validation
function testSuggestionFormat() {
	console.log("\n🧪 Testing suggestion format validation...")
	const mockSuggestion = {
		id: "test-suggestion-1",
		type: "modify",
		description: "Change: oldName ➜ newName",
		location: {
			anchor: "line_1_char_4",
			position: "replace",
		},
		patch: {
			oldContent: "oldName",
			newContent: "newName",
		},
	}
	try {
		// Validate required fields
		assert.ok(mockSuggestion.id, "id should be present")
		assert.ok(mockSuggestion.type, "type should be present")
		assert.ok(mockSuggestion.description, "description should be present")
		assert.ok(mockSuggestion.location, "location should be present")
		assert.ok(mockSuggestion.location.anchor, "anchor should be present")
		assert.ok(mockSuggestion.location.position, "position should be present")
		assert.ok(mockSuggestion.patch, "patch should be present")
		assert.ok(typeof mockSuggestion.patch.oldContent === "string", "oldContent should be string")
		assert.ok(typeof mockSuggestion.patch.newContent === "string", "newContent should be string")
		// Validate types
		assert.ok(["add", "modify", "delete"].includes(mockSuggestion.type), "type should be valid")
		assert.ok(["before", "after", "replace"].includes(mockSuggestion.location.position), "position should be valid")
		console.log("  ✅ Suggestion format validation PASSED")
		return true
	} catch (error) {
		console.log(`  ❌ Suggestion format validation FAILED: ${error.message}`)
		return false
	}
}
// Main test runner
async function runSimpleIntegrationTests() {
	console.log("🚀 QaxNextEdit Simple Integration Tests")
	console.log("=".repeat(50))
	const results = [testQaxToNextEditConversion(), testConfigurationDetection(), testSuggestionFormat()]
	const passed = results.filter((r) => r).length
	const total = results.length
	console.log("\n" + "=".repeat(50))
	console.log("📊 Overall Integration Test Results:")
	console.log(`  ✅ Test Suites Passed: ${passed}/${total}`)
	console.log(`  📈 Success Rate: ${Math.round((passed / total) * 100)}%`)
	if (passed === total) {
		console.log("\n🎉 All integration tests passed!")
		console.log("🔗 QaxNextEdit integration is working correctly!")
		console.log("✨ Ready to use QaxNextEdit with NextEdit service!")
		return true
	} else {
		console.log(`\n💥 ${total - passed} test suite(s) failed!`)
		return false
	}
}
// Run tests
runSimpleIntegrationTests()
	.then((success) => {
		process.exit(success ? 0 : 1)
	})
	.catch((error) => {
		console.error("💥 Error running integration tests:", error)
		process.exit(1)
	})
