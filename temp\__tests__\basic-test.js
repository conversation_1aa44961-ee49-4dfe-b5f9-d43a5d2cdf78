"use strict"
/**
 * Basic functionality test for QaxNextEdit
 * Tests core functionality without complex dependencies
 */
var __createBinding =
	(this && this.__createBinding) ||
	(Object.create
		? function (o, m, k, k2) {
				if (k2 === undefined) k2 = k
				var desc = Object.getOwnPropertyDescriptor(m, k)
				if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
					desc = {
						enumerable: true,
						get: function () {
							return m[k]
						},
					}
				}
				Object.defineProperty(o, k2, desc)
			}
		: function (o, m, k, k2) {
				if (k2 === undefined) k2 = k
				o[k2] = m[k]
			})
var __setModuleDefault =
	(this && this.__setModuleDefault) ||
	(Object.create
		? function (o, v) {
				Object.defineProperty(o, "default", { enumerable: true, value: v })
			}
		: function (o, v) {
				o["default"] = v
			})
var __importStar =
	(this && this.__importStar) ||
	function (mod) {
		if (mod && mod.__esModule) return mod
		var result = {}
		if (mod != null)
			for (var k in mod)
				if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k)
		__setModuleDefault(result, mod)
		return result
	}
Object.defineProperty(exports, "__esModule", { value: true })
const assert = __importStar(require("assert"))
const QaxNextEditTypes_1 = require("../types/QaxNextEditTypes")
// Test counter
let testCount = 0
let passedCount = 0
let failedCount = 0
function test(name, fn) {
	testCount++
	console.log(`\n🧪 Test ${testCount}: ${name}`)
	try {
		const result = fn()
		if (result instanceof Promise) {
			return result
				.then(() => {
					console.log(`✅ PASSED: ${name}`)
					passedCount++
				})
				.catch((error) => {
					console.log(`❌ FAILED: ${name}`)
					console.log(`   Error: ${error.message}`)
					failedCount++
				})
		} else {
			console.log(`✅ PASSED: ${name}`)
			passedCount++
		}
	} catch (error) {
		console.log(`❌ FAILED: ${name}`)
		console.log(`   Error: ${error.message}`)
		failedCount++
	}
}
async function runTests() {
	console.log("🚀 Starting QaxNextEdit Basic Tests")
	console.log("=".repeat(50))
	// Test 1: Type definitions
	test("Type definitions should be correct", () => {
		assert.strictEqual(QaxNextEditTypes_1.QaxChangeType.VARIABLE_RENAME, "variable_rename")
		assert.strictEqual(QaxNextEditTypes_1.QaxChangeType.FUNCTION_PARAMETER_CHANGE, "function_parameter_change")
		assert.strictEqual(QaxNextEditTypes_1.QaxChangeType.FUNCTION_CALL_DELETION, "function_call_deletion")
		assert.strictEqual(QaxNextEditTypes_1.QaxChangeType.VARIABLE_DELETION, "variable_deletion")
		assert.strictEqual(QaxNextEditTypes_1.QaxChangeType.IMPORT_CHANGE, "import_change")
		assert.strictEqual(QaxNextEditTypes_1.QaxChangeType.TYPE_CHANGE, "type_change")
	})
	// Test 2: Default configuration
	test("Default configuration should have correct values", () => {
		assert.strictEqual(QaxNextEditTypes_1.DEFAULT_QAX_NEXT_EDIT_CONFIG.enabled, true)
		assert.strictEqual(QaxNextEditTypes_1.DEFAULT_QAX_NEXT_EDIT_CONFIG.enableLSPIntegration, true)
		assert.strictEqual(QaxNextEditTypes_1.DEFAULT_QAX_NEXT_EDIT_CONFIG.enableASTAnalysis, true)
		assert.strictEqual(QaxNextEditTypes_1.DEFAULT_QAX_NEXT_EDIT_CONFIG.debounceDelayMs, 1500)
		assert.strictEqual(QaxNextEditTypes_1.DEFAULT_QAX_NEXT_EDIT_CONFIG.maxSuggestions, 8)
		assert.strictEqual(QaxNextEditTypes_1.DEFAULT_QAX_NEXT_EDIT_CONFIG.confidenceThreshold, 0.7)
		assert.strictEqual(QaxNextEditTypes_1.DEFAULT_QAX_NEXT_EDIT_CONFIG.analysisDepth, "deep")
		assert.ok(Array.isArray(QaxNextEditTypes_1.DEFAULT_QAX_NEXT_EDIT_CONFIG.supportedLanguages))
		assert.ok(QaxNextEditTypes_1.DEFAULT_QAX_NEXT_EDIT_CONFIG.supportedLanguages.includes("typescript"))
		assert.ok(QaxNextEditTypes_1.DEFAULT_QAX_NEXT_EDIT_CONFIG.supportedLanguages.includes("javascript"))
		assert.ok(QaxNextEditTypes_1.DEFAULT_QAX_NEXT_EDIT_CONFIG.supportedLanguages.includes("python"))
	})
	// Test 3: Event types
	test("Event types should be defined", () => {
		assert.strictEqual(QaxNextEditTypes_1.QaxNextEditEventType.CHANGE_DETECTED, "change_detected")
		assert.strictEqual(QaxNextEditTypes_1.QaxNextEditEventType.SUGGESTIONS_GENERATED, "suggestions_generated")
		assert.strictEqual(QaxNextEditTypes_1.QaxNextEditEventType.SUGGESTION_APPLIED, "suggestion_applied")
		assert.strictEqual(QaxNextEditTypes_1.QaxNextEditEventType.SUGGESTION_IGNORED, "suggestion_ignored")
		assert.strictEqual(QaxNextEditTypes_1.QaxNextEditEventType.ANALYSIS_STARTED, "analysis_started")
		assert.strictEqual(QaxNextEditTypes_1.QaxNextEditEventType.ANALYSIS_COMPLETED, "analysis_completed")
		assert.strictEqual(QaxNextEditTypes_1.QaxNextEditEventType.ERROR_OCCURRED, "error_occurred")
	})
	// Test 4: Configuration validation
	test("Configuration should validate supported languages", () => {
		const supportedLanguages = QaxNextEditTypes_1.DEFAULT_QAX_NEXT_EDIT_CONFIG.supportedLanguages
		// Should include major programming languages
		const expectedLanguages = [
			"typescript",
			"javascript",
			"python",
			"java",
			"csharp",
			"cpp",
			"c",
			"rust",
			"go",
			"php",
			"ruby",
			"swift",
			"kotlin",
		]
		for (const lang of expectedLanguages) {
			assert.ok(supportedLanguages.includes(lang), `Should support ${lang}`)
		}
	})
	// Test 5: Configuration bounds
	test("Configuration should have reasonable bounds", () => {
		const config = QaxNextEditTypes_1.DEFAULT_QAX_NEXT_EDIT_CONFIG
		// Debounce delay should be reasonable (500ms - 5000ms)
		assert.ok(config.debounceDelayMs >= 500, "Debounce delay should be at least 500ms")
		assert.ok(config.debounceDelayMs <= 5000, "Debounce delay should be at most 5000ms")
		// Max suggestions should be reasonable (1-20)
		assert.ok(config.maxSuggestions >= 1, "Max suggestions should be at least 1")
		assert.ok(config.maxSuggestions <= 20, "Max suggestions should be at most 20")
		// Confidence threshold should be between 0 and 1
		assert.ok(config.confidenceThreshold >= 0, "Confidence threshold should be at least 0")
		assert.ok(config.confidenceThreshold <= 1, "Confidence threshold should be at most 1")
	})
	// Test 6: Mock VS Code Range functionality
	test("Mock VS Code Range should work correctly", () => {
		// This tests our mock implementation
		const Range = class {
			constructor(start, end) {
				this.start = start
				this.end = end
			}
			get isEmpty() {
				return this.start.line === this.end.line && this.start.character === this.end.character
			}
			isEqual(other) {
				return (
					this.start.line === other.start.line &&
					this.start.character === other.start.character &&
					this.end.line === other.end.line &&
					this.end.character === other.end.character
				)
			}
		}
		const range1 = new Range({ line: 0, character: 0 }, { line: 0, character: 10 })
		const range2 = new Range({ line: 0, character: 0 }, { line: 0, character: 10 })
		const range3 = new Range({ line: 0, character: 0 }, { line: 0, character: 0 })
		assert.ok(range1.isEqual(range2), "Equal ranges should be equal")
		assert.ok(!range1.isEqual(range3), "Different ranges should not be equal")
		assert.ok(range3.isEmpty, "Empty range should be empty")
		assert.ok(!range1.isEmpty, "Non-empty range should not be empty")
	})
	// Test 7: Change detection patterns
	test("Change detection patterns should be identifiable", () => {
		// Test variable rename pattern
		const isVariableRename = (oldText, newText) => {
			return /^[a-zA-Z_$][a-zA-Z0-9_$]*$/.test(oldText) && /^[a-zA-Z_$][a-zA-Z0-9_$]*$/.test(newText) && oldText !== newText
		}
		assert.ok(isVariableRename("oldName", "newName"), "Should detect variable rename")
		assert.ok(!isVariableRename("oldName", "oldName"), "Should not detect same name as rename")
		assert.ok(!isVariableRename("123invalid", "newName"), "Should not detect invalid identifier")
		// Test function call deletion pattern
		const isFunctionCallDeletion = (oldText, newText) => {
			return /\w+\s*\([^)]*\)/.test(oldText) && newText === ""
		}
		assert.ok(isFunctionCallDeletion("test()", ""), "Should detect function call deletion")
		assert.ok(isFunctionCallDeletion("func(a, b)", ""), "Should detect function call with params deletion")
		assert.ok(!isFunctionCallDeletion("test()", "other()"), "Should not detect replacement as deletion")
	})
	// Test 8: Priority calculation logic
	test("Priority calculation should work correctly", () => {
		const calculatePriority = (isSameFile, lineDifference, confidence, isTypeScript) => {
			let priority = 5 // Base priority
			if (isSameFile) priority += 3
			if (lineDifference <= 10) priority += 2
			else if (lineDifference <= 50) priority += 1
			priority += Math.floor(confidence * 3)
			if (isTypeScript) priority += 1
			return Math.max(1, Math.min(10, priority))
		}
		// Same file, close lines, high confidence, TypeScript
		assert.strictEqual(calculatePriority(true, 5, 0.9, true), 10)
		// Different file, far lines, low confidence, not TypeScript
		assert.strictEqual(calculatePriority(false, 100, 0.3, false), 5)
		// Edge cases
		assert.ok(calculatePriority(true, 0, 1.0, true) >= 1, "Priority should be at least 1")
		assert.ok(calculatePriority(true, 0, 1.0, true) <= 10, "Priority should be at most 10")
	})
	// Test 9: Confidence threshold filtering
	test("Confidence threshold filtering should work", () => {
		const filterByConfidence = (items, threshold) => {
			return items.filter((item) => item.confidence >= threshold)
		}
		const items = [{ confidence: 0.9 }, { confidence: 0.7 }, { confidence: 0.5 }, { confidence: 0.3 }]
		const filtered = filterByConfidence(items, 0.7)
		assert.strictEqual(filtered.length, 2, "Should filter items below threshold")
		assert.ok(
			filtered.every((item) => item.confidence >= 0.7),
			"All filtered items should meet threshold",
		)
	})
	// Test 10: Language support validation
	test("Language support should be comprehensive", () => {
		const supportedLanguages = QaxNextEditTypes_1.DEFAULT_QAX_NEXT_EDIT_CONFIG.supportedLanguages
		// Web development languages
		assert.ok(supportedLanguages.includes("typescript"), "Should support TypeScript")
		assert.ok(supportedLanguages.includes("javascript"), "Should support JavaScript")
		// Backend languages
		assert.ok(supportedLanguages.includes("python"), "Should support Python")
		assert.ok(supportedLanguages.includes("java"), "Should support Java")
		assert.ok(supportedLanguages.includes("csharp"), "Should support C#")
		assert.ok(supportedLanguages.includes("go"), "Should support Go")
		// Systems languages
		assert.ok(supportedLanguages.includes("rust"), "Should support Rust")
		assert.ok(supportedLanguages.includes("cpp"), "Should support C++")
		assert.ok(supportedLanguages.includes("c"), "Should support C")
		// Mobile languages
		assert.ok(supportedLanguages.includes("swift"), "Should support Swift")
		assert.ok(supportedLanguages.includes("kotlin"), "Should support Kotlin")
		// Scripting languages
		assert.ok(supportedLanguages.includes("php"), "Should support PHP")
		assert.ok(supportedLanguages.includes("ruby"), "Should support Ruby")
	})
	// Wait for any async tests to complete
	await new Promise((resolve) => setTimeout(resolve, 100))
	// Print results
	console.log("\n" + "=".repeat(50))
	console.log("📊 Test Results:")
	console.log(`  ✅ Passed: ${passedCount}`)
	console.log(`  ❌ Failed: ${failedCount}`)
	console.log(`  📈 Success Rate: ${Math.round((passedCount / testCount) * 100)}%`)
	if (failedCount === 0) {
		console.log("\n🎉 All tests passed!")
		return true
	} else {
		console.log(`\n💥 ${failedCount} test(s) failed!`)
		return false
	}
}
// Run tests
runTests()
	.then((success) => {
		process.exit(success ? 0 : 1)
	})
	.catch((error) => {
		console.error("💥 Error running tests:", error)
		process.exit(1)
	})
