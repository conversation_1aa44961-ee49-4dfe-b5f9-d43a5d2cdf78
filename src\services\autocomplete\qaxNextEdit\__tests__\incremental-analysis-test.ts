/**
 * Test for incremental analysis and caching functionality
 */

import * as assert from "assert"

// Mock VS Code API
const mockVscode = {
	workspace: {
		onDidChangeTextDocument: () => ({ dispose: () => {} }),
		onDidOpenTextDocument: () => ({ dispose: () => {} }),
		onDidCloseTextDocument: () => ({ dispose: () => {} }),
		onDidChangeConfiguration: () => ({ dispose: () => {} }),
		getConfiguration: (section?: string) => ({
			get: (key: string, defaultValue?: any) => {
				const configs: any = {
					enabled: true,
					enableLSPIntegration: true,
					enableASTAnalysis: true,
					debounceDelayMs: 100,
					maxSuggestions: 8,
					confidenceThreshold: 0.7,
				}
				return configs[key] !== undefined ? configs[key] : defaultValue
			},
			update: () => Promise.resolve(),
		}),
		openTextDocument: async (uri: any) => ({
			uri: uri,
			languageId: "typescript",
			getText: () => "function test() { return 'test'; }",
			offsetAt: (position: any) => position.line * 100 + position.character,
		}),
		textDocuments: [],
	},
	window: {
		onDidChangeActiveTextEditor: () => ({ dispose: () => {} }),
		createStatusBarItem: () => ({
			text: "",
			show: () => {},
			hide: () => {},
			dispose: () => {},
		}),
		createTextEditorDecorationType: () => ({
			dispose: () => {},
		}),
		activeTextEditor: null,
	},
	languages: {
		onDidChangeDiagnostics: () => ({ dispose: () => {} }),
		registerHoverProvider: () => ({ dispose: () => {} }),
	},
	commands: {
		executeCommand: async () => null,
		registerCommand: () => ({ dispose: () => {} }),
	},
	StatusBarAlignment: { Right: 2 },
	TextEditorRevealType: { InCenter: 1 },
	SymbolKind: { Variable: 12, Function: 11, Class: 4, Method: 5 },
	ConfigurationTarget: { Global: 1 },
	ThemeColor: class {
		constructor(public id: string) {}
	},
	MarkdownString: class {
		constructor(public value: string = "") {
			this.isTrusted = false
		}
		isTrusted = false
		appendMarkdown(value: string) {
			this.value += value
			return this
		}
		appendCodeblock(value: string, language?: string) {
			this.value += `\n\`\`\`${language || ""}\n${value}\n\`\`\`\n`
			return this
		}
	},
	Hover: class {
		constructor(
			public contents: any,
			public range?: any,
		) {}
	},
	Uri: {
		file: (path: string) => ({ fsPath: path, scheme: "file" }),
	},
	Range: class {
		constructor(
			public start: { line: number; character: number },
			public end: { line: number; character: number },
		) {}

		get isEmpty() {
			return this.start.line === this.end.line && this.start.character === this.end.character
		}

		isEqual(other: any) {
			return (
				this.start.line === other.start.line &&
				this.start.character === other.start.character &&
				this.end.line === other.end.line &&
				this.end.character === other.end.character
			)
		}
	},
	Position: class {
		constructor(
			public line: number,
			public character: number,
		) {}

		isEqual(other: any) {
			return this.line === other.line && this.character === other.character
		}
	},
	Selection: class {
		constructor(
			public start: { line: number; character: number },
			public end: { line: number; character: number },
		) {}
	},
	Location: class {
		constructor(
			public uri: any,
			public range: any,
		) {}
	},
}

// Apply mocks
const vscode = mockVscode as any
;(global as any).vscode = vscode

// Test counter
let testCount = 0
let passedCount = 0
let failedCount = 0

function test(name: string, fn: () => void | Promise<void>): Promise<void> | void {
	testCount++
	console.log(`\n🧪 Incremental Test ${testCount}: ${name}`)

	try {
		const result = fn()
		if (result instanceof Promise) {
			return result
				.then(() => {
					console.log(`✅ PASSED: ${name}`)
					passedCount++
				})
				.catch((error) => {
					console.log(`❌ FAILED: ${name}`)
					console.log(`   Error: ${error.message}`)
					failedCount++
				})
		} else {
			console.log(`✅ PASSED: ${name}`)
			passedCount++
		}
	} catch (error) {
		console.log(`❌ FAILED: ${name}`)
		console.log(`   Error: ${(error as Error).message}`)
		failedCount++
	}
}

async function runIncrementalAnalysisTests() {
	console.log("🚀 QaxNextEdit Incremental Analysis Tests")
	console.log("=".repeat(50))

	// Test 1: Incremental analysis detection
	test("Should detect when incremental analysis is possible", () => {
		// Mock incremental cache
		const mockCache = new Map()
		const filePath = "test.ts"

		// Set up base analysis
		const baseAnalysis = {
			detectedChanges: [],
			jumpSuggestions: [],
			analysisTime: 100,
			confidence: 0.8,
			metadata: { lspAvailable: true, astParsed: true },
		}

		mockCache.set(filePath, {
			baseAnalysis,
			modifications: [],
			lastModified: new Date(),
		})

		// Test incremental analysis conditions
		const canIncremental = (filePath: string, changes: any[]) => {
			const cached = mockCache.get(filePath)
			if (!cached) return false

			const timeDiff = Date.now() - cached.lastModified.getTime()
			if (timeDiff > 5 * 60 * 1000) return false

			if (cached.modifications.length > 10) return false

			const hasComplexChanges = changes.some((change) => change.rangeLength > 100 || change.text.length > 100)

			return !hasComplexChanges
		}

		// Test simple change
		const simpleChanges = [{ rangeLength: 10, text: "newName" }]
		assert.strictEqual(canIncremental(filePath, simpleChanges), true, "Should allow incremental for simple changes")

		// Test complex change
		const complexChanges = [{ rangeLength: 200, text: "very long text..." }]
		assert.strictEqual(canIncremental(filePath, complexChanges), false, "Should not allow incremental for complex changes")

		console.log("    ✓ Incremental analysis detection working")
	})

	// Test 2: Cache management
	test("Should manage cache correctly", () => {
		const mockCache = new Map()
		const filePath = "test.ts"

		// Add to cache
		const analysisResult = {
			detectedChanges: [],
			jumpSuggestions: [
				{
					id: "test-1",
					filePath,
					range: new vscode.Range({ line: 0, character: 0 }, { line: 0, character: 10 }),
					description: "Test suggestion",
					changeType: "variable_rename",
					priority: 8,
				},
			],
			analysisTime: 150,
			confidence: 0.9,
			metadata: { lspAvailable: true, astParsed: true },
		}

		mockCache.set(filePath, {
			baseAnalysis: analysisResult,
			modifications: [],
			lastModified: new Date(),
		})

		// Verify cache
		assert.ok(mockCache.has(filePath), "Cache should contain file")
		const cached = mockCache.get(filePath)
		assert.strictEqual(cached.baseAnalysis.jumpSuggestions.length, 1, "Should cache suggestions")
		assert.strictEqual(cached.modifications.length, 0, "Should start with no modifications")

		// Add modification
		cached.modifications.push({
			filePath,
			document: null,
			changes: [{ rangeLength: 5, text: "test" }],
			beforeContent: "old",
			afterContent: "new",
			languageId: "typescript",
		})

		assert.strictEqual(cached.modifications.length, 1, "Should track modifications")

		console.log("    ✓ Cache management working")
	})

	// Test 3: Dependency analysis
	test("Should handle file dependencies", () => {
		const mockDependencies = new Map()
		const filePath = "main.ts"

		// Mock dependency detection
		const getFileDependencies = (filePath: string) => {
			let dependencies = mockDependencies.get(filePath)
			if (!dependencies) {
				dependencies = new Set()

				// Simulate finding related files
				const relatedFiles = ["utils.ts", "types.ts", "constants.ts"]
				relatedFiles.forEach((file) => dependencies.add(file))

				mockDependencies.set(filePath, dependencies)
			}
			return dependencies
		}

		const dependencies = getFileDependencies(filePath)

		assert.strictEqual(dependencies.size, 3, "Should find 3 dependencies")
		assert.ok(dependencies.has("utils.ts"), "Should include utils.ts")
		assert.ok(dependencies.has("types.ts"), "Should include types.ts")
		assert.ok(dependencies.has("constants.ts"), "Should include constants.ts")

		console.log("    ✓ Dependency analysis working")
	})

	// Test 4: Suggestion restoration
	test("Should restore suggestions for reopened files", async () => {
		const mockCache = new Map()
		const filePath = "test.ts"

		// Set up cached suggestions
		const cachedSuggestions = [
			{
				id: "restore-1",
				filePath,
				range: new vscode.Range({ line: 1, character: 0 }, { line: 1, character: 10 }),
				description: "Cached suggestion 1",
				changeType: "variable_rename",
				priority: 9,
			},
			{
				id: "restore-2",
				filePath,
				range: new vscode.Range({ line: 2, character: 0 }, { line: 2, character: 15 }),
				description: "Cached suggestion 2",
				changeType: "function_call_deletion",
				priority: 7,
			},
		]

		mockCache.set(filePath, {
			detectedChanges: [],
			jumpSuggestions: cachedSuggestions,
			analysisTime: 200,
			confidence: 0.85,
			metadata: { lspAvailable: true, astParsed: true },
		})

		// Simulate file reopening
		const handleDocumentOpened = (filePath: string) => {
			const cachedResult = mockCache.get(filePath)
			if (cachedResult && cachedResult.jumpSuggestions.length > 0) {
				return {
					restored: true,
					suggestions: cachedResult.jumpSuggestions,
					count: cachedResult.jumpSuggestions.length,
				}
			}
			return { restored: false, suggestions: [], count: 0 }
		}

		const result = handleDocumentOpened(filePath)

		assert.strictEqual(result.restored, true, "Should restore suggestions")
		assert.strictEqual(result.count, 2, "Should restore 2 suggestions")
		assert.strictEqual(result.suggestions[0].id, "restore-1", "Should restore first suggestion")
		assert.strictEqual(result.suggestions[1].id, "restore-2", "Should restore second suggestion")

		console.log("    ✓ Suggestion restoration working")
		console.log(`    ✓ Restored ${result.count} suggestions`)
	})

	// Test 5: Async dependency analysis
	test("Should handle async dependency analysis", async () => {
		const mockAsyncQueue = new Set()
		const mockResults = new Map()

		// Simulate async analysis
		const scheduleAsyncAnalysis = (filePath: string, dependencies: string[]) => {
			dependencies.forEach((depPath) => {
				if (!mockAsyncQueue.has(depPath) && !mockResults.has(depPath)) {
					mockAsyncQueue.add(depPath)

					// Simulate async processing
					setTimeout(() => {
						mockResults.set(depPath, {
							detectedChanges: [],
							jumpSuggestions: [],
							analysisTime: 50,
							confidence: 0.7,
							metadata: { lspAvailable: true, astParsed: true },
						})
						mockAsyncQueue.delete(depPath)
					}, 10)
				}
			})
		}

		const dependencies = ["dep1.ts", "dep2.ts", "dep3.ts"]
		scheduleAsyncAnalysis("main.ts", dependencies)

		// Check queue
		assert.strictEqual(mockAsyncQueue.size, 3, "Should queue 3 dependencies")

		// Wait for async processing
		await new Promise((resolve) => setTimeout(resolve, 50))

		// Check results
		assert.strictEqual(mockAsyncQueue.size, 0, "Queue should be empty after processing")
		assert.strictEqual(mockResults.size, 3, "Should have 3 analysis results")

		console.log("    ✓ Async dependency analysis working")
	})

	// Test 6: Suggestion deduplication
	test("Should deduplicate suggestions correctly", () => {
		const suggestions = [
			{
				id: "dup-1",
				filePath: "test.ts",
				range: { start: { line: 1, character: 0 }, end: { line: 1, character: 10 } },
				changeType: "variable_rename",
			},
			{
				id: "dup-2",
				filePath: "test.ts",
				range: { start: { line: 1, character: 0 }, end: { line: 1, character: 10 } },
				changeType: "variable_rename",
			},
			{
				id: "dup-3",
				filePath: "test.ts",
				range: { start: { line: 2, character: 0 }, end: { line: 2, character: 5 } },
				changeType: "function_call_deletion",
			},
		]

		const deduplicateSuggestions = (suggestions: any[]) => {
			const seen = new Set()
			return suggestions.filter((suggestion) => {
				const key = `${suggestion.filePath}:${suggestion.range.start.line}:${suggestion.range.start.character}:${suggestion.changeType}`
				if (seen.has(key)) {
					return false
				}
				seen.add(key)
				return true
			})
		}

		const deduplicated = deduplicateSuggestions(suggestions)

		assert.strictEqual(deduplicated.length, 2, "Should remove 1 duplicate")
		assert.strictEqual(deduplicated[0].id, "dup-1", "Should keep first occurrence")
		assert.strictEqual(deduplicated[1].id, "dup-3", "Should keep unique suggestion")

		console.log("    ✓ Suggestion deduplication working")
	})

	// Wait for any async operations
	await new Promise((resolve) => setTimeout(resolve, 100))

	// Print results
	console.log("\n" + "=".repeat(50))
	console.log("📊 Incremental Analysis Test Results:")
	console.log(`  ✅ Passed: ${passedCount}`)
	console.log(`  ❌ Failed: ${failedCount}`)
	console.log(`  📈 Success Rate: ${Math.round((passedCount / testCount) * 100)}%`)

	if (failedCount === 0) {
		console.log("\n🎉 All incremental analysis tests passed!")
		console.log("🔄 Incremental analysis features are working correctly!")
		return true
	} else {
		console.log(`\n💥 ${failedCount} incremental analysis test(s) failed!`)
		return false
	}
}

// Run incremental analysis tests
runIncrementalAnalysisTests()
	.then((success) => {
		process.exit(success ? 0 : 1)
	})
	.catch((error) => {
		console.error("💥 Error running incremental analysis tests:", error)
		process.exit(1)
	})
