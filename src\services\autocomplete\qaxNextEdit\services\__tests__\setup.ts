// Jest setup file for QaxLSPService tests

// Global test setup
beforeAll(() => {
    // Suppress console warnings during tests
    const originalWarn = console.warn;
    console.warn = (...args: any[]) => {
        // Only suppress specific warnings we expect during testing
        const message = args[0];
        if (typeof message === 'string' && (
            message.includes('QaxLSPService:') ||
            message.includes('Failed to get') ||
            message.includes('LSP error')
        )) {
            return;
        }
        originalWarn.apply(console, args);
    };
});

// Global test teardown
afterAll(() => {
    // Restore console.warn
    jest.restoreAllMocks();
});

// Setup global test utilities
global.createMockPosition = (line: number, character: number) => ({
    line,
    character,
    isAfter: jest.fn(),
    isBefore: jest.fn(),
    isBeforeOrEqual: jest.fn(),
    isAfterOrEqual: jest.fn(),
    isEqual: jest.fn(),
    compareTo: jest.fn(),
    translate: jest.fn(),
    with: jest.fn(),
});

global.createMockRange = (startLine: number, startChar: number, endLine: number, endChar: number) => ({
    start: global.createMockPosition(startLine, startChar),
    end: global.createMockPosition(endLine, endChar),
    isEmpty: startLine === endLine && startChar === endChar,
    isSingleLine: startLine === endLine,
    contains: jest.fn(),
    isEqual: jest.fn(),
    intersection: jest.fn(),
    union: jest.fn(),
    with: jest.fn(),
});

global.createMockLocation = (filePath: string, startLine: number, startChar: number, endLine?: number, endChar?: number) => ({
    uri: { fsPath: filePath, toString: () => `file://${filePath}` },
    range: global.createMockRange(startLine, startChar, endLine || startLine, endChar || startChar),
});

global.createMockDocument = (filePath: string, languageId: string = 'javascript', content: string = '') => ({
    uri: { fsPath: filePath, toString: () => `file://${filePath}` },
    fileName: filePath,
    isUntitled: false,
    languageId,
    version: 1,
    isDirty: false,
    isClosed: false,
    save: jest.fn(),
    eol: 1,
    lineCount: content.split('\n').length,
    lineAt: jest.fn((line: number) => ({
        lineNumber: line,
        text: content.split('\n')[line] || '',
        range: global.createMockRange(line, 0, line, (content.split('\n')[line] || '').length),
        rangeIncludingLineBreak: global.createMockRange(line, 0, line + 1, 0),
        firstNonWhitespaceCharacterIndex: 0,
        isEmptyOrWhitespace: (content.split('\n')[line] || '').trim().length === 0,
    })),
    offsetAt: jest.fn(),
    positionAt: jest.fn(),
    getText: jest.fn(() => content),
    getWordRangeAtPosition: jest.fn(),
    validateRange: jest.fn(),
    validatePosition: jest.fn(),
});

// Declare global types for TypeScript
declare global {
    var createMockPosition: (line: number, character: number) => any;
    var createMockRange: (startLine: number, startChar: number, endLine: number, endChar: number) => any;
    var createMockLocation: (filePath: string, startLine: number, startChar: number, endLine?: number, endChar?: number) => any;
    var createMockDocument: (filePath: string, languageId?: string, content?: string) => any;
}
