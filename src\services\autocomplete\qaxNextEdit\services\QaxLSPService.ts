import * as vscode from "vscode"
import { QaxSymbolInfo, QaxChangeDetection, QaxChangeType } from "../types/QaxNextEditTypes"

/**
 * LSP 集成服务，用于获取语言服务器提供的符号信息、引用、定义等
 */
export class QaxLSPService {
	private static instance: QaxLSPService | null = null
	private disposables: vscode.Disposable[] = []

	private constructor() {
		this.setupEventHandlers()
	}

	public static getInstance(): QaxLSPService {
		if (!QaxLSPService.instance) {
			QaxLSPService.instance = new QaxLSPService()
		}
		return QaxLSPService.instance
	}

	public static dispose(): void {
		if (QaxLSPService.instance) {
			QaxLSPService.instance.dispose()
			QaxLSPService.instance = null
		}
	}

	private setupEventHandlers(): void {
		// 监听语言服务器状态变化
		this.disposables.push(
			vscode.languages.onDidChangeDiagnostics(() => {
				// 可以在这里处理诊断信息变化
			}),
		)
	}

	/**
	 * 获取文档中的所有符号
	 */
	async getDocumentSymbols(document: vscode.TextDocument): Promise<QaxSymbolInfo[]> {
		try {
			const symbols = await vscode.commands.executeCommand<vscode.DocumentSymbol[]>(
				"vscode.executeDocumentSymbolProvider",
				document.uri,
			)

			if (!symbols) {
				return []
			}

			return this.convertDocumentSymbols(symbols, document.uri)
		} catch (error) {
			console.warn("QaxLSPService: Failed to get document symbols:", error)
			return []
		}
	}

	/**
	 * 获取符号的所有引用
	 */
	async getReferences(document: vscode.TextDocument, position: vscode.Position): Promise<vscode.Location[]> {
		try {
			const references = await vscode.commands.executeCommand<vscode.Location[]>(
				"vscode.executeReferenceProvider",
				document.uri,
				position,
			)

			return references || []
		} catch (error) {
			console.warn("QaxLSPService: Failed to get references:", error)
			return []
		}
	}

	/**
	 * 使用原始内容获取符号的所有引用
	 * 这个方法解决了在修改后的位置查询原符号引用的问题
	 */
	async getReferencesFromOriginalContent(
		originalContent: string,
		filePath: string,
		position: vscode.Position,
		languageId: string
	): Promise<vscode.Location[]> {
		// 对于引用查找，我们可以使用一个更简单的方法：
		// 直接在当前文档的原始内容中进行文本搜索
		// 这避免了创建临时文档的复杂性和潜在的死循环问题

		try {
			console.log(`🔍 QaxLSPService: Getting references from original content using text search`)
			console.log(`  Position: ${position.line}:${position.character}`)
			console.log(`  Content length: ${originalContent.length}`)

			// 首先尝试从位置获取符号名称
			const symbolName = this.extractSymbolAtPosition(originalContent, position)
			if (!symbolName) {
				console.log(`🔍 QaxLSPService: Could not extract symbol name at position`)
				return []
			}

			console.log(`🔍 QaxLSPService: Extracted symbol name: "${symbolName}"`)

			// 在原始内容中搜索所有该符号的出现位置
			const references = this.findSymbolOccurrences(originalContent, symbolName, filePath)

			console.log(`🔍 QaxLSPService: Found ${references.length} references using text search`)
			return references

		} catch (error) {
			console.warn("QaxLSPService: Failed to get references from original content:", error)
			return []
		}
	}

	/**
	 * 从指定位置提取符号名称
	 */
	private extractSymbolAtPosition(content: string, position: vscode.Position): string | null {
		const lines = content.split('\n')
		if (position.line >= lines.length) {
			return null
		}

		const line = lines[position.line]
		if (position.character >= line.length) {
			return null
		}

		// 找到符号的边界
		let start = position.character
		let end = position.character

		// 向前找到符号开始
		while (start > 0 && this.isIdentifierChar(line[start - 1])) {
			start--
		}

		// 向后找到符号结束
		while (end < line.length && this.isIdentifierChar(line[end])) {
			end++
		}

		const symbolName = line.substring(start, end)
		return symbolName.length > 0 ? symbolName : null
	}

	/**
	 * 判断字符是否是标识符字符
	 */
	private isIdentifierChar(char: string): boolean {
		return /[a-zA-Z0-9_$]/.test(char)
	}

	/**
	 * 在内容中查找符号的所有出现位置
	 */
	private findSymbolOccurrences(content: string, symbolName: string, filePath: string): vscode.Location[] {
		const references: vscode.Location[] = []
		const lines = content.split('\n')
		const uri = vscode.Uri.file(filePath)

		// 创建正则表达式，只匹配完整的标识符
		const pattern = new RegExp(`\\b${symbolName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'g')

		for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
			const line = lines[lineIndex]
			let match: RegExpExecArray | null

			while ((match = pattern.exec(line)) !== null) {
				const startPos = new vscode.Position(lineIndex, match.index)
				const endPos = new vscode.Position(lineIndex, match.index + symbolName.length)
				const range = new vscode.Range(startPos, endPos)
				const location = new vscode.Location(uri, range)
				references.push(location)
			}
		}

		return references
	}

	/**
	 * 使用原始内容获取符号的定义位置
	 * 使用简化的启发式方法避免临时文档的复杂性
	 */
	async getDefinitionsFromOriginalContent(
		originalContent: string,
		filePath: string,
		position: vscode.Position,
		languageId: string
	): Promise<vscode.Location[]> {
		try {
			console.log(`🔍 QaxLSPService: Getting definitions from original content using heuristics`)
			console.log(`  Position: ${position.line}:${position.character}`)

			// 获取符号名称
			const symbolName = this.extractSymbolAtPosition(originalContent, position)
			if (!symbolName) {
				console.log(`🔍 QaxLSPService: Could not extract symbol name at position`)
				return []
			}

			console.log(`🔍 QaxLSPService: Looking for definitions of symbol: "${symbolName}"`)

			// 使用启发式方法查找定义
			const definitions = this.findSymbolDefinitions(originalContent, symbolName, filePath, languageId)

			console.log(`🔍 QaxLSPService: Found ${definitions.length} definitions using heuristics`)
			return definitions

		} catch (error) {
			console.warn("QaxLSPService: Failed to get definitions from original content:", error)
			return []
		}
	}

	/**
	 * 使用启发式方法查找符号定义
	 */
	private findSymbolDefinitions(content: string, symbolName: string, filePath: string, languageId: string): vscode.Location[] {
		const definitions: vscode.Location[] = []
		const lines = content.split('\n')
		const uri = vscode.Uri.file(filePath)

		// 根据语言类型使用不同的定义模式
		const definitionPatterns = this.getDefinitionPatterns(symbolName, languageId)

		for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
			const line = lines[lineIndex]

			for (const pattern of definitionPatterns) {
				const match = pattern.exec(line)
				if (match) {
					// 找到符号名称在匹配中的位置
					const symbolIndex = match[0].indexOf(symbolName)
					if (symbolIndex !== -1) {
						const startPos = new vscode.Position(lineIndex, match.index + symbolIndex)
						const endPos = new vscode.Position(lineIndex, match.index + symbolIndex + symbolName.length)
						const range = new vscode.Range(startPos, endPos)
						const location = new vscode.Location(uri, range)
						definitions.push(location)

						console.log(`  Found definition at line ${lineIndex + 1}: ${line.trim()}`)
					}
				}
				// 重置正则表达式的lastIndex
				pattern.lastIndex = 0
			}
		}

		return definitions
	}

	/**
	 * 根据语言获取定义模式
	 */
	private getDefinitionPatterns(symbolName: string, languageId: string): RegExp[] {
		const escapedSymbol = symbolName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')

		switch (languageId) {
			case 'javascript':
			case 'typescript':
				return [
					// 函数声明: function symbolName
					new RegExp(`\\bfunction\\s+${escapedSymbol}\\b`, 'g'),
					// 变量声明: const/let/var symbolName
					new RegExp(`\\b(?:const|let|var)\\s+${escapedSymbol}\\b`, 'g'),
					// 箭头函数: const symbolName =
					new RegExp(`\\bconst\\s+${escapedSymbol}\\s*=`, 'g'),
					// 类声明: class symbolName
					new RegExp(`\\bclass\\s+${escapedSymbol}\\b`, 'g'),
					// 方法定义: symbolName(
					new RegExp(`\\b${escapedSymbol}\\s*\\(`, 'g'),
				]

			case 'python':
				return [
					// 函数定义: def symbolName
					new RegExp(`\\bdef\\s+${escapedSymbol}\\b`, 'g'),
					// 类定义: class symbolName
					new RegExp(`\\bclass\\s+${escapedSymbol}\\b`, 'g'),
				]

			default:
				// 通用模式
				return [
					// 通用函数/变量定义模式
					new RegExp(`\\b(?:function|def|class|const|let|var)\\s+${escapedSymbol}\\b`, 'g'),
					new RegExp(`\\b${escapedSymbol}\\s*[=:]`, 'g'),
				]
		}
	}

	/**
	 * 判断位置是否是符号的定义位置
	 */
	async isDefinitionLocation(
		originalContent: string,
		filePath: string,
		position: vscode.Position,
		languageId: string
	): Promise<boolean> {
		try {
			const definitions = await this.getDefinitionsFromOriginalContent(
				originalContent,
				filePath,
				position,
				languageId
			)

			// 检查当前位置是否与任何定义位置重叠
			for (const def of definitions) {
				if (def.uri.fsPath === filePath) {
					// 检查位置是否在定义范围内
					if (this.isPositionInRange(position, def.range)) {
						console.log(`🔍 QaxLSPService: Position ${position.line}:${position.character} is a definition location`)
						return true
					}
				}
			}

			console.log(`🔍 QaxLSPService: Position ${position.line}:${position.character} is NOT a definition location`)
			return false
		} catch (error) {
			console.warn("QaxLSPService: Failed to check if position is definition:", error)
			return false
		}
	}

	/**
	 * 检查位置是否在范围内
	 */
	private isPositionInRange(position: vscode.Position, range: vscode.Range): boolean {
		if (position.line < range.start.line || position.line > range.end.line) {
			return false
		}

		if (position.line === range.start.line && position.character < range.start.character) {
			return false
		}

		if (position.line === range.end.line && position.character > range.end.character) {
			return false
		}

		return true
	}



	/**
	 * 获取符号的定义
	 */
	async getDefinitions(document: vscode.TextDocument, position: vscode.Position): Promise<vscode.Location[]> {
		try {
			const definitions = await vscode.commands.executeCommand<vscode.Location[]>(
				"vscode.executeDefinitionProvider",
				document.uri,
				position,
			)

			return definitions || []
		} catch (error) {
			console.warn("QaxLSPService: Failed to get definitions:", error)
			return []
		}
	}

	/**
	 * 获取符号的类型定义
	 */
	async getTypeDefinitions(document: vscode.TextDocument, position: vscode.Position): Promise<vscode.Location[]> {
		try {
			const typeDefinitions = await vscode.commands.executeCommand<vscode.Location[]>(
				"vscode.executeTypeDefinitionProvider",
				document.uri,
				position,
			)

			return typeDefinitions || []
		} catch (error) {
			console.warn("QaxLSPService: Failed to get type definitions:", error)
			return []
		}
	}

	/**
	 * 获取重命名信息
	 */
	async getRenameInfo(
		document: vscode.TextDocument,
		position: vscode.Position,
	): Promise<{
		range: vscode.Range
		placeholder: string
	} | null> {
		try {
			const renameInfo = await vscode.commands.executeCommand<{
				range: vscode.Range
				placeholder: string
			}>("vscode.prepareRename", document.uri, position)

			return renameInfo || null
		} catch (error) {
			console.warn("QaxLSPService: Failed to get rename info:", error)
			return null
		}
	}

	/**
	 * 执行重命名预览
	 */
	async previewRename(
		document: vscode.TextDocument,
		position: vscode.Position,
		newName: string,
	): Promise<vscode.WorkspaceEdit | null> {
		try {
			const workspaceEdit = await vscode.commands.executeCommand<vscode.WorkspaceEdit>(
				"vscode.executeDocumentRenameProvider",
				document.uri,
				position,
				newName,
			)

			return workspaceEdit || null
		} catch (error) {
			console.warn("QaxLSPService: Failed to preview rename:", error)
			return null
		}
	}

	/**
	 * 获取悬停信息
	 */
	async getHoverInfo(document: vscode.TextDocument, position: vscode.Position): Promise<vscode.Hover | null> {
		try {
			const hover = await vscode.commands.executeCommand<vscode.Hover[]>(
				"vscode.executeHoverProvider",
				document.uri,
				position,
			)

			return hover && hover.length > 0 ? hover[0] : null
		} catch (error) {
			console.warn("QaxLSPService: Failed to get hover info:", error)
			return null
		}
	}

	/**
	 * 检测符号是否被重命名
	 */
	async detectSymbolRename(
		document: vscode.TextDocument,
		oldPosition: vscode.Position,
		newPosition: vscode.Position,
		oldText: string,
		newText: string,
	): Promise<QaxChangeDetection | null> {
		try {
			// 获取旧位置的符号信息
			const oldReferences = await this.getReferences(document, oldPosition)
			const oldDefinitions = await this.getDefinitions(document, oldPosition)

			// 检查是否是符号重命名
			if (oldReferences.length > 0 || oldDefinitions.length > 0) {
				return {
					type: QaxChangeType.VARIABLE_RENAME,
					filePath: document.uri.fsPath,
					range: new vscode.Range(oldPosition, newPosition),
					oldValue: oldText,
					newValue: newText,
					confidence: 0.9,
					metadata: {
						symbolName: oldText,
						references: oldReferences,
						definitions: oldDefinitions,
					},
				}
			}

			return null
		} catch (error) {
			console.warn("QaxLSPService: Failed to detect symbol rename:", error)
			return null
		}
	}

	/**
	 * 转换 DocumentSymbol 为 QaxSymbolInfo
	 */
	private convertDocumentSymbols(symbols: vscode.DocumentSymbol[], uri: vscode.Uri): QaxSymbolInfo[] {
		const result: QaxSymbolInfo[] = []

		const convertSymbol = (symbol: vscode.DocumentSymbol, containerName?: string): void => {
			const symbolInfo: QaxSymbolInfo = {
				name: symbol.name,
				kind: symbol.kind,
				location: new vscode.Location(uri, symbol.selectionRange),
				containerName,
				detail: symbol.detail,
			}

			result.push(symbolInfo)

			// 递归处理子符号
			if (symbol.children) {
				for (const child of symbol.children) {
					convertSymbol(child, symbol.name)
				}
			}
		}

		for (const symbol of symbols) {
			convertSymbol(symbol)
		}

		return result
	}

	/**
	 * 检查 LSP 是否可用
	 */
	async isLSPAvailable(document: vscode.TextDocument): Promise<boolean> {
		try {
			// 尝试获取文档符号来检查 LSP 是否可用
			const symbols = await vscode.commands.executeCommand<vscode.DocumentSymbol[]>(
				"vscode.executeDocumentSymbolProvider",
				document.uri,
			)
			return symbols !== undefined
		} catch (error) {
			return false
		}
	}

	/**
	 * 清理资源
	 */
	dispose(): void {
		this.disposables.forEach((d) => d.dispose())
		this.disposables = []
	}
}
