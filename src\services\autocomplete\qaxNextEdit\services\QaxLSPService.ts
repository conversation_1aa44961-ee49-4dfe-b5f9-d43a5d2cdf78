import * as vscode from "vscode"
import { QaxSymbolInfo, QaxChangeDetection, QaxChangeType } from "../types/QaxNextEditTypes"

/**
 * LSP 集成服务，用于获取语言服务器提供的符号信息、引用、定义等
 */
export class QaxLSPService {
	private static instance: QaxLSPService | null = null
	private disposables: vscode.Disposable[] = []

	private constructor() {
		this.setupEventHandlers()
	}

	public static getInstance(): QaxLSPService {
		if (!QaxLSPService.instance) {
			QaxLSPService.instance = new QaxLSPService()
		}
		return QaxLSPService.instance
	}

	public static dispose(): void {
		if (QaxLSPService.instance) {
			QaxLSPService.instance.dispose()
			QaxLSPService.instance = null
		}
	}

	private setupEventHandlers(): void {
		// 监听语言服务器状态变化
		this.disposables.push(
			vscode.languages.onDidChangeDiagnostics(() => {
				// 可以在这里处理诊断信息变化
			}),
		)
	}

	/**
	 * 获取文档中的所有符号
	 */
	async getDocumentSymbols(document: vscode.TextDocument): Promise<QaxSymbolInfo[]> {
		try {
			const symbols = await vscode.commands.executeCommand<vscode.DocumentSymbol[]>(
				"vscode.executeDocumentSymbolProvider",
				document.uri,
			)

			if (!symbols) {
				return []
			}

			return this.convertDocumentSymbols(symbols, document.uri)
		} catch (error) {
			console.warn("QaxLSPService: Failed to get document symbols:", error)
			return []
		}
	}

	/**
	 * 获取符号的所有引用
	 */
	async getReferences(document: vscode.TextDocument, position: vscode.Position): Promise<vscode.Location[]> {
		try {
			const references = await vscode.commands.executeCommand<vscode.Location[]>(
				"vscode.executeReferenceProvider",
				document.uri,
				position,
			)

			return references || []
		} catch (error) {
			console.warn("QaxLSPService: Failed to get references:", error)
			return []
		}
	}

	/**
	 * 使用原始内容获取符号的所有引用
	 * 这个方法解决了在修改后的位置查询原符号引用的问题
	 */
	async getReferencesFromOriginalContent(
		originalContent: string,
		filePath: string,
		position: vscode.Position,
		languageId: string
	): Promise<vscode.Location[]> {
		try {
			console.log(`🔍 QaxLSPService: Getting references from original content at ${position.line}:${position.character}`)

			// 创建临时文档来查询原符号的引用
			const tempDocument = await vscode.workspace.openTextDocument({
				content: originalContent,
				language: languageId
			})

			console.log(`🔍 QaxLSPService: Created temp document with ${originalContent.length} characters`)

			// 在临时文档上查询引用
			const references = await vscode.commands.executeCommand<vscode.Location[]>(
				"vscode.executeReferenceProvider",
				tempDocument.uri,
				position,
			)

			if (!references || references.length === 0) {
				console.log(`🔍 QaxLSPService: No references found in original content`)
				return []
			}

			console.log(`🔍 QaxLSPService: Found ${references.length} references in original content`)

			// 将临时文档的引用位置映射回原始文档
			const mappedReferences: vscode.Location[] = []
			const originalUri = vscode.Uri.file(filePath)

			for (const ref of references) {
				if (ref.uri.toString() === tempDocument.uri.toString()) {
					// 这是在临时文档中的引用，映射回原始文档
					const mappedLocation = new vscode.Location(originalUri, ref.range)
					mappedReferences.push(mappedLocation)
					console.log(`  Mapped reference: ${ref.range.start.line}:${ref.range.start.character}`)
				} else {
					// 这是在其他文件中的引用，直接使用
					mappedReferences.push(ref)
					console.log(`  External reference: ${ref.uri.fsPath} ${ref.range.start.line}:${ref.range.start.character}`)
				}
			}

			// 清理临时文档
			try {
				await vscode.commands.executeCommand('workbench.action.closeActiveEditor')
			} catch (cleanupError) {
				console.warn("QaxLSPService: Failed to cleanup temp document:", cleanupError)
			}

			console.log(`🔍 QaxLSPService: Returning ${mappedReferences.length} mapped references`)
			return mappedReferences

		} catch (error) {
			console.warn("QaxLSPService: Failed to get references from original content:", error)
			return []
		}
	}

	/**
	 * 使用原始内容获取符号的定义位置
	 */
	async getDefinitionsFromOriginalContent(
		originalContent: string,
		filePath: string,
		position: vscode.Position,
		languageId: string
	): Promise<vscode.Location[]> {
		try {
			console.log(`🔍 QaxLSPService: Getting definitions from original content at ${position.line}:${position.character}`)

			// 创建临时文档来查询原符号的定义
			const tempDocument = await vscode.workspace.openTextDocument({
				content: originalContent,
				language: languageId
			})

			console.log(`🔍 QaxLSPService: Created temp document for definitions with ${originalContent.length} characters`)

			// 在临时文档上查询定义
			const definitions = await vscode.commands.executeCommand<vscode.Location[]>(
				"vscode.executeDefinitionProvider",
				tempDocument.uri,
				position,
			)

			if (!definitions || definitions.length === 0) {
				console.log(`🔍 QaxLSPService: No definitions found in original content`)
				return []
			}

			console.log(`🔍 QaxLSPService: Found ${definitions.length} definitions in original content`)

			// 将临时文档的定义位置映射回原始文档
			const mappedDefinitions: vscode.Location[] = []
			const originalUri = vscode.Uri.file(filePath)

			for (const def of definitions) {
				if (def.uri.toString() === tempDocument.uri.toString()) {
					// 这是在临时文档中的定义，映射回原始文档
					const mappedLocation = new vscode.Location(originalUri, def.range)
					mappedDefinitions.push(mappedLocation)
					console.log(`  Mapped definition: ${def.range.start.line}:${def.range.start.character}`)
				} else {
					// 这是在其他文件中的定义，直接使用
					mappedDefinitions.push(def)
					console.log(`  External definition: ${def.uri.fsPath} ${def.range.start.line}:${def.range.start.character}`)
				}
			}

			// 清理临时文档
			try {
				await vscode.commands.executeCommand('workbench.action.closeActiveEditor')
			} catch (cleanupError) {
				console.warn("QaxLSPService: Failed to cleanup temp document:", cleanupError)
			}

			console.log(`🔍 QaxLSPService: Returning ${mappedDefinitions.length} mapped definitions`)
			return mappedDefinitions

		} catch (error) {
			console.warn("QaxLSPService: Failed to get definitions from original content:", error)
			return []
		}
	}

	/**
	 * 判断位置是否是符号的定义位置
	 */
	async isDefinitionLocation(
		originalContent: string,
		filePath: string,
		position: vscode.Position,
		languageId: string
	): Promise<boolean> {
		try {
			const definitions = await this.getDefinitionsFromOriginalContent(
				originalContent,
				filePath,
				position,
				languageId
			)

			// 检查当前位置是否与任何定义位置重叠
			for (const def of definitions) {
				if (def.uri.fsPath === filePath) {
					// 检查位置是否在定义范围内
					if (this.isPositionInRange(position, def.range)) {
						console.log(`🔍 QaxLSPService: Position ${position.line}:${position.character} is a definition location`)
						return true
					}
				}
			}

			console.log(`🔍 QaxLSPService: Position ${position.line}:${position.character} is NOT a definition location`)
			return false
		} catch (error) {
			console.warn("QaxLSPService: Failed to check if position is definition:", error)
			return false
		}
	}

	/**
	 * 检查位置是否在范围内
	 */
	private isPositionInRange(position: vscode.Position, range: vscode.Range): boolean {
		if (position.line < range.start.line || position.line > range.end.line) {
			return false
		}

		if (position.line === range.start.line && position.character < range.start.character) {
			return false
		}

		if (position.line === range.end.line && position.character > range.end.character) {
			return false
		}

		return true
	}

	/**
	 * 获取文件扩展名
	 */
	private getFileExtension(languageId: string): string {
		const extensionMap: { [key: string]: string } = {
			'javascript': 'js',
			'typescript': 'ts',
			'python': 'py',
			'java': 'java',
			'csharp': 'cs',
			'cpp': 'cpp',
			'c': 'c',
			'go': 'go',
			'rust': 'rs',
			'php': 'php',
			'ruby': 'rb',
			'swift': 'swift',
			'kotlin': 'kt'
		}
		return extensionMap[languageId] || 'txt'
	}

	/**
	 * 获取符号的定义
	 */
	async getDefinitions(document: vscode.TextDocument, position: vscode.Position): Promise<vscode.Location[]> {
		try {
			const definitions = await vscode.commands.executeCommand<vscode.Location[]>(
				"vscode.executeDefinitionProvider",
				document.uri,
				position,
			)

			return definitions || []
		} catch (error) {
			console.warn("QaxLSPService: Failed to get definitions:", error)
			return []
		}
	}

	/**
	 * 获取符号的类型定义
	 */
	async getTypeDefinitions(document: vscode.TextDocument, position: vscode.Position): Promise<vscode.Location[]> {
		try {
			const typeDefinitions = await vscode.commands.executeCommand<vscode.Location[]>(
				"vscode.executeTypeDefinitionProvider",
				document.uri,
				position,
			)

			return typeDefinitions || []
		} catch (error) {
			console.warn("QaxLSPService: Failed to get type definitions:", error)
			return []
		}
	}

	/**
	 * 获取重命名信息
	 */
	async getRenameInfo(
		document: vscode.TextDocument,
		position: vscode.Position,
	): Promise<{
		range: vscode.Range
		placeholder: string
	} | null> {
		try {
			const renameInfo = await vscode.commands.executeCommand<{
				range: vscode.Range
				placeholder: string
			}>("vscode.prepareRename", document.uri, position)

			return renameInfo || null
		} catch (error) {
			console.warn("QaxLSPService: Failed to get rename info:", error)
			return null
		}
	}

	/**
	 * 执行重命名预览
	 */
	async previewRename(
		document: vscode.TextDocument,
		position: vscode.Position,
		newName: string,
	): Promise<vscode.WorkspaceEdit | null> {
		try {
			const workspaceEdit = await vscode.commands.executeCommand<vscode.WorkspaceEdit>(
				"vscode.executeDocumentRenameProvider",
				document.uri,
				position,
				newName,
			)

			return workspaceEdit || null
		} catch (error) {
			console.warn("QaxLSPService: Failed to preview rename:", error)
			return null
		}
	}

	/**
	 * 获取悬停信息
	 */
	async getHoverInfo(document: vscode.TextDocument, position: vscode.Position): Promise<vscode.Hover | null> {
		try {
			const hover = await vscode.commands.executeCommand<vscode.Hover[]>(
				"vscode.executeHoverProvider",
				document.uri,
				position,
			)

			return hover && hover.length > 0 ? hover[0] : null
		} catch (error) {
			console.warn("QaxLSPService: Failed to get hover info:", error)
			return null
		}
	}

	/**
	 * 检测符号是否被重命名
	 */
	async detectSymbolRename(
		document: vscode.TextDocument,
		oldPosition: vscode.Position,
		newPosition: vscode.Position,
		oldText: string,
		newText: string,
	): Promise<QaxChangeDetection | null> {
		try {
			// 获取旧位置的符号信息
			const oldReferences = await this.getReferences(document, oldPosition)
			const oldDefinitions = await this.getDefinitions(document, oldPosition)

			// 检查是否是符号重命名
			if (oldReferences.length > 0 || oldDefinitions.length > 0) {
				return {
					type: QaxChangeType.VARIABLE_RENAME,
					filePath: document.uri.fsPath,
					range: new vscode.Range(oldPosition, newPosition),
					oldValue: oldText,
					newValue: newText,
					confidence: 0.9,
					metadata: {
						symbolName: oldText,
						references: oldReferences,
						definitions: oldDefinitions,
					},
				}
			}

			return null
		} catch (error) {
			console.warn("QaxLSPService: Failed to detect symbol rename:", error)
			return null
		}
	}

	/**
	 * 转换 DocumentSymbol 为 QaxSymbolInfo
	 */
	private convertDocumentSymbols(symbols: vscode.DocumentSymbol[], uri: vscode.Uri): QaxSymbolInfo[] {
		const result: QaxSymbolInfo[] = []

		const convertSymbol = (symbol: vscode.DocumentSymbol, containerName?: string): void => {
			const symbolInfo: QaxSymbolInfo = {
				name: symbol.name,
				kind: symbol.kind,
				location: new vscode.Location(uri, symbol.selectionRange),
				containerName,
				detail: symbol.detail,
			}

			result.push(symbolInfo)

			// 递归处理子符号
			if (symbol.children) {
				for (const child of symbol.children) {
					convertSymbol(child, symbol.name)
				}
			}
		}

		for (const symbol of symbols) {
			convertSymbol(symbol)
		}

		return result
	}

	/**
	 * 检查 LSP 是否可用
	 */
	async isLSPAvailable(document: vscode.TextDocument): Promise<boolean> {
		try {
			// 尝试获取文档符号来检查 LSP 是否可用
			const symbols = await vscode.commands.executeCommand<vscode.DocumentSymbol[]>(
				"vscode.executeDocumentSymbolProvider",
				document.uri,
			)
			return symbols !== undefined
		} catch (error) {
			return false
		}
	}

	/**
	 * 清理资源
	 */
	dispose(): void {
		this.disposables.forEach((d) => d.dispose())
		this.disposables = []
	}
}
