import * as vscode from "vscode"
import { QaxSymbolInfo, QaxChangeDetection, QaxChangeType } from "../types/QaxNextEditTypes"

/**
 * LSP 集成服务，用于获取语言服务器提供的符号信息、引用、定义等
 */
export class QaxLSPService {
	private static instance: QaxLSPService | null = null
	private disposables: vscode.Disposable[] = []

	private constructor() {
		this.setupEventHandlers()
	}

	public static getInstance(): QaxLSPService {
		if (!QaxLSPService.instance) {
			QaxLSPService.instance = new QaxLSPService()
		}
		return QaxLSPService.instance
	}

	public static dispose(): void {
		if (QaxLSPService.instance) {
			QaxLSPService.instance.dispose()
			QaxLSPService.instance = null
		}
	}

	private setupEventHandlers(): void {
		// 监听语言服务器状态变化
		this.disposables.push(
			vscode.languages.onDidChangeDiagnostics(() => {
				// 可以在这里处理诊断信息变化
			}),
		)
	}

	/**
	 * 获取文档中的所有符号
	 */
	async getDocumentSymbols(document: vscode.TextDocument): Promise<QaxSymbolInfo[]> {
		try {
			const symbols = await vscode.commands.executeCommand<vscode.DocumentSymbol[]>(
				"vscode.executeDocumentSymbolProvider",
				document.uri,
			)

			if (!symbols) {
				return []
			}

			return this.convertDocumentSymbols(symbols, document.uri)
		} catch (error) {
			console.warn("QaxLSPService: Failed to get document symbols:", error)
			return []
		}
	}

	/**
	 * 获取符号的所有引用
	 */
	async getReferences(document: vscode.TextDocument, position: vscode.Position): Promise<vscode.Location[]> {
		try {
			const references = await vscode.commands.executeCommand<vscode.Location[]>(
				"vscode.executeReferenceProvider",
				document.uri,
				position,
			)

			return references || []
		} catch (error) {
			console.warn("QaxLSPService: Failed to get references:", error)
			return []
		}
	}

	/**
	 * 获取符号的定义
	 */
	async getDefinitions(document: vscode.TextDocument, position: vscode.Position): Promise<vscode.Location[]> {
		try {
			const definitions = await vscode.commands.executeCommand<vscode.Location[]>(
				"vscode.executeDefinitionProvider",
				document.uri,
				position,
			)

			return definitions || []
		} catch (error) {
			console.warn("QaxLSPService: Failed to get definitions:", error)
			return []
		}
	}

	/**
	 * 获取符号的类型定义
	 */
	async getTypeDefinitions(document: vscode.TextDocument, position: vscode.Position): Promise<vscode.Location[]> {
		try {
			const typeDefinitions = await vscode.commands.executeCommand<vscode.Location[]>(
				"vscode.executeTypeDefinitionProvider",
				document.uri,
				position,
			)

			return typeDefinitions || []
		} catch (error) {
			console.warn("QaxLSPService: Failed to get type definitions:", error)
			return []
		}
	}

	/**
	 * 获取重命名信息
	 */
	async getRenameInfo(
		document: vscode.TextDocument,
		position: vscode.Position,
	): Promise<{
		range: vscode.Range
		placeholder: string
	} | null> {
		try {
			const renameInfo = await vscode.commands.executeCommand<{
				range: vscode.Range
				placeholder: string
			}>("vscode.prepareRename", document.uri, position)

			return renameInfo || null
		} catch (error) {
			console.warn("QaxLSPService: Failed to get rename info:", error)
			return null
		}
	}

	/**
	 * 执行重命名预览
	 */
	async previewRename(
		document: vscode.TextDocument,
		position: vscode.Position,
		newName: string,
	): Promise<vscode.WorkspaceEdit | null> {
		try {
			const workspaceEdit = await vscode.commands.executeCommand<vscode.WorkspaceEdit>(
				"vscode.executeDocumentRenameProvider",
				document.uri,
				position,
				newName,
			)

			return workspaceEdit || null
		} catch (error) {
			console.warn("QaxLSPService: Failed to preview rename:", error)
			return null
		}
	}

	/**
	 * 获取悬停信息
	 */
	async getHoverInfo(document: vscode.TextDocument, position: vscode.Position): Promise<vscode.Hover | null> {
		try {
			const hover = await vscode.commands.executeCommand<vscode.Hover[]>(
				"vscode.executeHoverProvider",
				document.uri,
				position,
			)

			return hover && hover.length > 0 ? hover[0] : null
		} catch (error) {
			console.warn("QaxLSPService: Failed to get hover info:", error)
			return null
		}
	}

	/**
	 * 检测符号是否被重命名
	 */
	async detectSymbolRename(
		document: vscode.TextDocument,
		oldPosition: vscode.Position,
		newPosition: vscode.Position,
		oldText: string,
		newText: string,
	): Promise<QaxChangeDetection | null> {
		try {
			// 获取旧位置的符号信息
			const oldReferences = await this.getReferences(document, oldPosition)
			const oldDefinitions = await this.getDefinitions(document, oldPosition)

			// 检查是否是符号重命名
			if (oldReferences.length > 0 || oldDefinitions.length > 0) {
				return {
					type: QaxChangeType.VARIABLE_RENAME,
					filePath: document.uri.fsPath,
					range: new vscode.Range(oldPosition, newPosition),
					oldValue: oldText,
					newValue: newText,
					confidence: 0.9,
					metadata: {
						symbolName: oldText,
						references: oldReferences,
						definitions: oldDefinitions,
					},
				}
			}

			return null
		} catch (error) {
			console.warn("QaxLSPService: Failed to detect symbol rename:", error)
			return null
		}
	}

	/**
	 * 转换 DocumentSymbol 为 QaxSymbolInfo
	 */
	private convertDocumentSymbols(symbols: vscode.DocumentSymbol[], uri: vscode.Uri): QaxSymbolInfo[] {
		const result: QaxSymbolInfo[] = []

		const convertSymbol = (symbol: vscode.DocumentSymbol, containerName?: string): void => {
			const symbolInfo: QaxSymbolInfo = {
				name: symbol.name,
				kind: symbol.kind,
				location: new vscode.Location(uri, symbol.selectionRange),
				containerName,
				detail: symbol.detail,
			}

			result.push(symbolInfo)

			// 递归处理子符号
			if (symbol.children) {
				for (const child of symbol.children) {
					convertSymbol(child, symbol.name)
				}
			}
		}

		for (const symbol of symbols) {
			convertSymbol(symbol)
		}

		return result
	}

	/**
	 * 检查 LSP 是否可用
	 */
	async isLSPAvailable(document: vscode.TextDocument): Promise<boolean> {
		try {
			// 尝试获取文档符号来检查 LSP 是否可用
			const symbols = await vscode.commands.executeCommand<vscode.DocumentSymbol[]>(
				"vscode.executeDocumentSymbolProvider",
				document.uri,
			)
			return symbols !== undefined
		} catch (error) {
			return false
		}
	}

	/**
	 * 清理资源
	 */
	dispose(): void {
		this.disposables.forEach((d) => d.dispose())
		this.disposables = []
	}
}
