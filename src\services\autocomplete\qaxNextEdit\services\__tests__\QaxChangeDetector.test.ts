import * as vscode from 'vscode';
import { QaxChangeDetector } from '../QaxChangeDetector';
import { QaxLSPService } from '../QaxLSPService';
import { QaxASTService } from '../QaxASTService';
import { QaxJumpSuggestionEngine } from '../QaxJumpSuggestionEngine';
import { QaxSymbolDetector } from '../QaxSymbolDetector';
import {
    QaxAnalysisContext,
    QaxChangeDetection,
    QaxChangeType,
    QaxNextEditConfig,
    DEFAULT_QAX_NEXT_EDIT_CONFIG,
    QaxSymbolChange,
} from '../../types/QaxNextEditTypes';

// Mock all dependencies
jest.mock('../QaxLSPService');
jest.mock('../QaxASTService');
jest.mock('../QaxJumpSuggestionEngine');
jest.mock('../QaxSymbolDetector');

describe('QaxChangeDetector', () => {
    let detector: QaxChangeDetector;
    let mockConfig: QaxNextEditConfig;
    let mockLSPService: jest.Mocked<QaxLSPService>;
    let mockASTService: jest.Mocked<QaxASTService>;
    let mockJumpSuggestionEngine: jest.Mocked<QaxJumpSuggestionEngine>;
    let mockSymbolDetector: jest.Mocked<QaxSymbolDetector>;
    let mockDocument: vscode.TextDocument;
    let mockContext: QaxAnalysisContext;

    beforeEach(() => {
        // Reset all mocks
        jest.clearAllMocks();

        // Create mock config
        mockConfig = {
            ...DEFAULT_QAX_NEXT_EDIT_CONFIG,
            enableLSPIntegration: true,
            enableASTAnalysis: true,
            confidenceThreshold: 0.4,
        };

        // Setup mock services
        mockLSPService = {
            getReferencesFromOriginalContent: jest.fn(),
            getDefinitionsFromOriginalContent: jest.fn(),
            isDefinitionLocation: jest.fn(),
            detectSymbolRename: jest.fn(),
            getReferences: jest.fn(),
            getDefinitions: jest.fn(),
            isLSPAvailable: jest.fn(),
            getDocumentSymbols: jest.fn(),
        } as any;

        mockASTService = {
            detectVariableRename: jest.fn(),
            detectFunctionParameterChanges: jest.fn(),
            detectFunctionCallDeletions: jest.fn(),
            detectVariableDeletions: jest.fn(),
            detectImportChanges: jest.fn(),
            detectTypeChanges: jest.fn(),
            isASTAvailable: jest.fn(),
            parseDocument: jest.fn(),
        } as any;

        mockJumpSuggestionEngine = {
            generateSuggestions: jest.fn(),
            updateConfig: jest.fn(),
        } as any;

        mockSymbolDetector = {
            detectSymbolChanges: jest.fn(),
        } as any;

        // Setup mock returns
        (QaxLSPService.getInstance as jest.Mock).mockReturnValue(mockLSPService);
        (QaxASTService.getInstance as jest.Mock).mockReturnValue(mockASTService);

        // Create mock document
        mockDocument = {
            uri: vscode.Uri.file('/test/file.js'),
            fileName: '/test/file.js',
            languageId: 'javascript',
            version: 1,
            getText: jest.fn().mockReturnValue('const test = 1;'),
            lineAt: jest.fn(),
            lineCount: 1,
        } as any;

        // Create mock context
        mockContext = {
            filePath: '/test/file.js',
            document: mockDocument,
            changes: [],
            beforeContent: 'const oldTest = 1;',
            afterContent: 'const newTest = 1;',
            languageId: 'javascript',
        };

        // Create detector instance
        detector = new QaxChangeDetector(mockConfig);
    });

    describe('Constructor and Initialization', () => {
        test('should initialize with correct dependencies', () => {
            expect(QaxLSPService.getInstance).toHaveBeenCalled();
            expect(QaxASTService.getInstance).toHaveBeenCalled();
            expect(QaxJumpSuggestionEngine).toHaveBeenCalledWith(mockConfig);
            expect(QaxSymbolDetector).toHaveBeenCalled();
        });

        test('should store config correctly', () => {
            expect((detector as any).config).toEqual(mockConfig);
        });
    });

    describe('analyzeChanges', () => {
        test('should return empty result when no changes detected', async () => {
            // Setup mocks to return no changes
            mockSymbolDetector.detectSymbolChanges.mockResolvedValue([]);
            mockLSPService.isLSPAvailable.mockResolvedValue(false);
            mockASTService.isASTAvailable.mockResolvedValue(false);
            mockJumpSuggestionEngine.generateSuggestions.mockResolvedValue([]);

            const result = await detector.analyzeChanges(mockContext);

            expect(result).toBeDefined();
            expect(result.detectedChanges).toEqual([]);
            expect(result.suggestions).toEqual([]);
            expect(result.confidence).toBe(0);
        });

        test('should detect symbol changes when available', async () => {
            const mockSymbolChanges: QaxSymbolChange[] = [
                {
                    symbolType: 'identifier',
                    oldText: 'oldTest',
                    newText: 'newTest',
                    range: new vscode.Range(0, 6, 0, 13),
                    symbolName: 'oldTest',
                    context: { type: 'variable_declaration' },
                }
            ];

            mockSymbolDetector.detectSymbolChanges.mockResolvedValue(mockSymbolChanges);
            mockLSPService.getReferencesFromOriginalContent.mockResolvedValue([]);
            mockLSPService.getDefinitionsFromOriginalContent.mockResolvedValue([]);
            mockLSPService.isDefinitionLocation.mockResolvedValue(true);
            mockJumpSuggestionEngine.generateSuggestions.mockResolvedValue([]);

            const result = await detector.analyzeChanges(mockContext);

            expect(result.detectedChanges.length).toBeGreaterThan(0);
            expect(mockSymbolDetector.detectSymbolChanges).toHaveBeenCalledWith(
                mockContext.beforeContent,
                mockContext.afterContent,
                mockContext.languageId
            );
        });

        test('should use LSP detection when symbol detection fails', async () => {
            mockSymbolDetector.detectSymbolChanges.mockResolvedValue([]);
            mockLSPService.isLSPAvailable.mockResolvedValue(true);
            mockLSPService.detectSymbolRename.mockResolvedValue({
                type: QaxChangeType.VARIABLE_RENAME,
                filePath: '/test/file.js',
                range: new vscode.Range(0, 0, 0, 10),
                oldValue: 'oldTest',
                newValue: 'newTest',
                confidence: 0.8,
                metadata: { detectionMethod: 'lsp' },
            });
            mockJumpSuggestionEngine.generateSuggestions.mockResolvedValue([]);

            const result = await detector.analyzeChanges(mockContext);

            expect(mockLSPService.detectSymbolRename).toHaveBeenCalled();
            expect(result.detectedChanges.length).toBeGreaterThan(0);
        });

        test('should use AST detection when other methods fail', async () => {
            mockSymbolDetector.detectSymbolChanges.mockResolvedValue([]);
            mockLSPService.isLSPAvailable.mockResolvedValue(false);
            mockASTService.isASTAvailable.mockResolvedValue(true);
            mockASTService.detectVariableRename.mockResolvedValue([{
                type: QaxChangeType.VARIABLE_RENAME,
                filePath: '/test/file.js',
                range: new vscode.Range(0, 0, 0, 10),
                oldValue: 'oldTest',
                newValue: 'newTest',
                confidence: 0.7,
                metadata: { detectionMethod: 'ast' },
            }]);
            mockJumpSuggestionEngine.generateSuggestions.mockResolvedValue([]);

            const result = await detector.analyzeChanges(mockContext);

            expect(mockASTService.detectVariableRename).toHaveBeenCalled();
            expect(result.detectedChanges.length).toBeGreaterThan(0);
        });

        test('should fall back to text diff when all other methods fail', async () => {
            mockSymbolDetector.detectSymbolChanges.mockResolvedValue([]);
            mockLSPService.isLSPAvailable.mockResolvedValue(false);
            mockASTService.isASTAvailable.mockResolvedValue(false);
            mockJumpSuggestionEngine.generateSuggestions.mockResolvedValue([]);

            const result = await detector.analyzeChanges(mockContext);

            // Should still detect changes using text diff
            expect(result.detectedChanges.length).toBeGreaterThanOrEqual(0);
        });

        test('should handle errors gracefully', async () => {
            mockSymbolDetector.detectSymbolChanges.mockRejectedValue(new Error('Symbol detection failed'));
            mockLSPService.isLSPAvailable.mockRejectedValue(new Error('LSP failed'));
            mockASTService.isASTAvailable.mockRejectedValue(new Error('AST failed'));

            const result = await detector.analyzeChanges(mockContext);

            expect(result).toBeDefined();
            expect(result.detectedChanges).toEqual([]);
            expect(result.suggestions).toEqual([]);
        });
    });

    describe('detectSymbolChanges', () => {
        test('should process symbol changes correctly', async () => {
            const mockSymbolChanges: QaxSymbolChange[] = [
                {
                    symbolType: 'identifier',
                    oldText: 'oldVar',
                    newText: 'newVar',
                    range: new vscode.Range(0, 6, 0, 12),
                    symbolName: 'oldVar',
                    context: { type: 'variable_declaration' },
                }
            ];

            mockSymbolDetector.detectSymbolChanges.mockResolvedValue(mockSymbolChanges);
            mockLSPService.getReferencesFromOriginalContent.mockResolvedValue([]);
            mockLSPService.getDefinitionsFromOriginalContent.mockResolvedValue([]);
            mockLSPService.isDefinitionLocation.mockResolvedValue(true);

            const result = await (detector as any).detectSymbolChanges(mockContext);

            expect(result.length).toBe(1);
            expect(result[0].type).toBe(QaxChangeType.VARIABLE_RENAME);
            expect(result[0].oldValue).toBe('oldVar');
            expect(result[0].newValue).toBe('newVar');
        });

        test('should skip changes with no actual difference', async () => {
            const mockSymbolChanges: QaxSymbolChange[] = [
                {
                    symbolType: 'identifier',
                    oldText: 'sameVar',
                    newText: 'sameVar',
                    range: new vscode.Range(0, 6, 0, 13),
                    symbolName: 'sameVar',
                    context: { type: 'variable_declaration' },
                }
            ];

            mockSymbolDetector.detectSymbolChanges.mockResolvedValue(mockSymbolChanges);

            const result = await (detector as any).detectSymbolChanges(mockContext);

            expect(result.length).toBe(0);
        });

        test('should handle unsupported symbol types', async () => {
            const mockSymbolChanges: QaxSymbolChange[] = [
                {
                    symbolType: 'unsupported_type' as any,
                    oldText: 'oldValue',
                    newText: 'newValue',
                    range: new vscode.Range(0, 0, 0, 8),
                    symbolName: 'test',
                    context: {},
                }
            ];

            mockSymbolDetector.detectSymbolChanges.mockResolvedValue(mockSymbolChanges);

            const result = await (detector as any).detectSymbolChanges(mockContext);

            expect(result.length).toBe(0);
        });
    });

    describe('detectLSPChanges', () => {
        test('should detect LSP changes when available', async () => {
            mockLSPService.isLSPAvailable.mockResolvedValue(true);
            mockLSPService.detectSymbolRename.mockResolvedValue({
                type: QaxChangeType.VARIABLE_RENAME,
                filePath: '/test/file.js',
                range: new vscode.Range(0, 0, 0, 10),
                oldValue: 'oldTest',
                newValue: 'newTest',
                confidence: 0.8,
                metadata: { detectionMethod: 'lsp' },
            });

            const result = await (detector as any).detectLSPChanges(mockContext);

            expect(result.length).toBe(1);
            expect(result[0].type).toBe(QaxChangeType.VARIABLE_RENAME);
        });

        test('should return empty array when LSP not available', async () => {
            mockLSPService.isLSPAvailable.mockResolvedValue(false);

            const result = await (detector as any).detectLSPChanges(mockContext);

            expect(result).toEqual([]);
        });

        test('should handle LSP errors gracefully', async () => {
            mockLSPService.isLSPAvailable.mockResolvedValue(true);
            mockLSPService.detectSymbolRename.mockRejectedValue(new Error('LSP error'));

            const result = await (detector as any).detectLSPChanges(mockContext);

            expect(result).toEqual([]);
        });
    });

    describe('detectASTChanges', () => {
        test('should detect AST changes when available', async () => {
            mockASTService.isASTAvailable.mockResolvedValue(true);
            mockASTService.detectVariableRename.mockResolvedValue([{
                type: QaxChangeType.VARIABLE_RENAME,
                filePath: '/test/file.js',
                range: new vscode.Range(0, 0, 0, 10),
                oldValue: 'oldVar',
                newValue: 'newVar',
                confidence: 0.7,
                metadata: { detectionMethod: 'ast' },
            }]);

            const result = await (detector as any).detectASTChanges(mockContext);

            expect(result.length).toBe(1);
            expect(result[0].type).toBe(QaxChangeType.VARIABLE_RENAME);
        });

        test('should return empty array when AST not available', async () => {
            mockASTService.isASTAvailable.mockResolvedValue(false);

            const result = await (detector as any).detectASTChanges(mockContext);

            expect(result).toEqual([]);
        });

        test('should handle AST errors gracefully', async () => {
            mockASTService.isASTAvailable.mockResolvedValue(true);
            mockASTService.detectVariableRename.mockRejectedValue(new Error('AST error'));

            const result = await (detector as any).detectASTChanges(mockContext);

            expect(result).toEqual([]);
        });

        test('should call all AST detection methods', async () => {
            mockASTService.isASTAvailable.mockResolvedValue(true);
            mockASTService.detectVariableRename.mockResolvedValue([]);
            mockASTService.detectFunctionParameterChanges.mockResolvedValue([]);
            mockASTService.detectFunctionCallDeletions.mockResolvedValue([]);
            mockASTService.detectVariableDeletions.mockResolvedValue([]);
            mockASTService.detectImportChanges.mockResolvedValue([]);
            mockASTService.detectTypeChanges.mockResolvedValue([]);

            await (detector as any).detectASTChanges(mockContext);

            expect(mockASTService.detectVariableRename).toHaveBeenCalled();
            expect(mockASTService.detectFunctionParameterChanges).toHaveBeenCalled();
            expect(mockASTService.detectFunctionCallDeletions).toHaveBeenCalled();
            expect(mockASTService.detectVariableDeletions).toHaveBeenCalled();
            expect(mockASTService.detectImportChanges).toHaveBeenCalled();
            expect(mockASTService.detectTypeChanges).toHaveBeenCalled();
        });
    });

    describe('detectTextDiffChanges', () => {
        test('should detect text differences', async () => {
            const context = {
                ...mockContext,
                beforeContent: 'const oldVar = 1;',
                afterContent: 'const newVar = 1;',
            };

            const result = await (detector as any).detectTextDiffChanges(context);

            expect(result.length).toBeGreaterThan(0);
        });

        test('should handle identical content', async () => {
            const context = {
                ...mockContext,
                beforeContent: 'const test = 1;',
                afterContent: 'const test = 1;',
            };

            const result = await (detector as any).detectTextDiffChanges(context);

            expect(result.length).toBe(0);
        });

        test('should handle empty content', async () => {
            const context = {
                ...mockContext,
                beforeContent: '',
                afterContent: '',
            };

            const result = await (detector as any).detectTextDiffChanges(context);

            expect(result.length).toBe(0);
        });
    });

    describe('mergeDetectedChanges', () => {
        test('should merge and deduplicate changes', () => {
            const changes: QaxChangeDetection[] = [
                {
                    type: QaxChangeType.VARIABLE_RENAME,
                    filePath: '/test/file.js',
                    range: new vscode.Range(0, 0, 0, 10),
                    oldValue: 'oldVar',
                    newValue: 'newVar',
                    confidence: 0.8,
                    metadata: { detectionMethod: 'symbol_based' },
                },
                {
                    type: QaxChangeType.VARIABLE_RENAME,
                    filePath: '/test/file.js',
                    range: new vscode.Range(0, 0, 0, 10),
                    oldValue: 'oldVar',
                    newValue: 'newVar',
                    confidence: 0.7,
                    metadata: { detectionMethod: 'lsp' },
                },
            ];

            const result = (detector as any).mergeDetectedChanges(changes);

            expect(result.length).toBe(1);
            expect(result[0].confidence).toBe(0.8); // Should keep higher confidence
        });

        test('should sort by detection method quality', () => {
            const changes: QaxChangeDetection[] = [
                {
                    type: QaxChangeType.VARIABLE_RENAME,
                    filePath: '/test/file.js',
                    range: new vscode.Range(0, 0, 0, 10),
                    oldValue: 'test1',
                    newValue: 'test1New',
                    confidence: 0.6,
                    metadata: { detectionMethod: 'text_diff' },
                },
                {
                    type: QaxChangeType.VARIABLE_RENAME,
                    filePath: '/test/file.js',
                    range: new vscode.Range(1, 0, 1, 10),
                    oldValue: 'test2',
                    newValue: 'test2New',
                    confidence: 0.9,
                    metadata: { detectionMethod: 'symbol_based' },
                },
            ];

            const result = (detector as any).mergeDetectedChanges(changes);

            expect(result[0].metadata.detectionMethod).toBe('symbol_based');
            expect(result[1].metadata.detectionMethod).toBe('text_diff');
        });
    });

    describe('filterByConfidence', () => {
        test('should filter changes by confidence threshold', () => {
            const changes: QaxChangeDetection[] = [
                {
                    type: QaxChangeType.VARIABLE_RENAME,
                    filePath: '/test/file.js',
                    range: new vscode.Range(0, 0, 0, 10),
                    oldValue: 'highConf',
                    newValue: 'highConfNew',
                    confidence: 0.8,
                    metadata: {},
                },
                {
                    type: QaxChangeType.VARIABLE_RENAME,
                    filePath: '/test/file.js',
                    range: new vscode.Range(1, 0, 1, 10),
                    oldValue: 'lowConf',
                    newValue: 'lowConfNew',
                    confidence: 0.2,
                    metadata: {},
                },
            ];

            const result = (detector as any).filterByConfidence(changes);

            expect(result.length).toBe(1);
            expect(result[0].confidence).toBe(0.8);
        });

        test('should return empty array when no changes meet threshold', () => {
            const changes: QaxChangeDetection[] = [
                {
                    type: QaxChangeType.VARIABLE_RENAME,
                    filePath: '/test/file.js',
                    range: new vscode.Range(0, 0, 0, 10),
                    oldValue: 'lowConf',
                    newValue: 'lowConfNew',
                    confidence: 0.1,
                    metadata: {},
                },
            ];

            const result = (detector as any).filterByConfidence(changes);

            expect(result.length).toBe(0);
        });
    });

    describe('Private Helper Methods', () => {
        describe('determineChangeType', () => {
            test('should determine variable rename for identifier', () => {
                const symbolChange: QaxSymbolChange = {
                    symbolType: 'identifier',
                    oldText: 'oldVar',
                    newText: 'newVar',
                    range: new vscode.Range(0, 0, 0, 6),
                    symbolName: 'oldVar',
                    context: {},
                };

                const result = (detector as any).determineChangeType(symbolChange);

                expect(result).toBe(QaxChangeType.VARIABLE_RENAME);
            });

            test('should determine function call rename', () => {
                const symbolChange: QaxSymbolChange = {
                    symbolType: 'function_call',
                    oldText: 'oldFunc',
                    newText: 'newFunc',
                    range: new vscode.Range(0, 0, 0, 7),
                    symbolName: 'oldFunc',
                    context: {},
                };

                const result = (detector as any).determineChangeType(symbolChange);

                expect(result).toBe(QaxChangeType.FUNCTION_CALL_RENAME);
            });

            test('should return null for unsupported symbol type', () => {
                const symbolChange: QaxSymbolChange = {
                    symbolType: 'unsupported' as any,
                    oldText: 'old',
                    newText: 'new',
                    range: new vscode.Range(0, 0, 0, 3),
                    symbolName: 'test',
                    context: {},
                };

                const result = (detector as any).determineChangeType(symbolChange);

                expect(result).toBe(QaxChangeType.UNKNOWN_CHANGE);
            });
        });

        describe('calculateSymbolChangeConfidence', () => {
            test('should calculate high confidence for clear identifier change', () => {
                const symbolChange: QaxSymbolChange = {
                    symbolType: 'identifier',
                    oldText: 'oldVariable',
                    newText: 'newVariable',
                    range: new vscode.Range(0, 0, 0, 11),
                    symbolName: 'oldVariable',
                    context: { type: 'variable_declaration' },
                };

                const confidence = (detector as any).calculateSymbolChangeConfidence(symbolChange);

                expect(confidence).toBeGreaterThan(0.7);
            });

            test('should calculate lower confidence for short changes', () => {
                const symbolChange: QaxSymbolChange = {
                    symbolType: 'identifier',
                    oldText: 'a',
                    newText: 'b',
                    range: new vscode.Range(0, 0, 0, 1),
                    symbolName: 'a',
                    context: {},
                };

                const confidence = (detector as any).calculateSymbolChangeConfidence(symbolChange);

                expect(confidence).toBeLessThan(0.7);
            });
        });

        describe('looksLikeIdentifier', () => {
            test('should return true for valid identifiers', () => {
                expect((detector as any).looksLikeIdentifier('validIdentifier')).toBe(true);
                expect((detector as any).looksLikeIdentifier('_private')).toBe(true);
                expect((detector as any).looksLikeIdentifier('$jquery')).toBe(true);
                expect((detector as any).looksLikeIdentifier('test123')).toBe(true);
            });

            test('should return false for invalid identifiers', () => {
                expect((detector as any).looksLikeIdentifier('123invalid')).toBe(false);
                expect((detector as any).looksLikeIdentifier('invalid-name')).toBe(false);
                expect((detector as any).looksLikeIdentifier('invalid.name')).toBe(false);
                expect((detector as any).looksLikeIdentifier('')).toBe(false);
            });
        });
    });

    describe('updateConfig', () => {
        test('should update configuration', () => {
            const newConfig: QaxNextEditConfig = {
                ...mockConfig,
                confidenceThreshold: 0.8,
            };

            detector.updateConfig(newConfig);

            expect((detector as any).config).toEqual(newConfig);
            expect(mockJumpSuggestionEngine.updateConfig).toHaveBeenCalledWith(newConfig);
        });
    });

    describe('Error Handling and Edge Cases', () => {
        test('should handle null/undefined inputs gracefully', async () => {
            const nullContext = null as any;

            const result = await detector.analyzeChanges(nullContext);

            expect(result.detectedChanges).toEqual([]);
            expect(result.suggestions).toEqual([]);
        });

        test('should handle empty file content', async () => {
            const emptyContext = {
                ...mockContext,
                beforeContent: '',
                afterContent: '',
            };

            mockSymbolDetector.detectSymbolChanges.mockResolvedValue([]);

            const result = await detector.analyzeChanges(emptyContext);

            expect(result.detectedChanges).toEqual([]);
        });

        test('should handle very large files', async () => {
            const largeContent = 'const test = 1;\n'.repeat(10000);
            const largeContext = {
                ...mockContext,
                beforeContent: largeContent,
                afterContent: largeContent.replace('test', 'newTest'),
            };

            mockSymbolDetector.detectSymbolChanges.mockResolvedValue([]);

            const result = await detector.analyzeChanges(largeContext);

            expect(result).toBeDefined();
        });

        test('should handle special characters in content', async () => {
            const specialContext = {
                ...mockContext,
                beforeContent: 'const 测试变量 = 1;',
                afterContent: 'const 新测试变量 = 1;',
            };

            mockSymbolDetector.detectSymbolChanges.mockResolvedValue([]);

            const result = await detector.analyzeChanges(specialContext);

            expect(result).toBeDefined();
        });
    });
});
