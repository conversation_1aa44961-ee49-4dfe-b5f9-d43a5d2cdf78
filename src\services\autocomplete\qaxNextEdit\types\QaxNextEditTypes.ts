import * as vscode from "vscode"

/**
 * 检测到的修改类型
 */
export enum QaxChangeType {
	VARIABLE_RENAME = "variable_rename",
	FUNCTION_PARAMETER_CHANGE = "function_parameter_change",
	FUNCTION_CALL_DELETION = "function_call_deletion",
	VARIABLE_DELETION = "variable_deletion",
	IMPORT_CHANGE = "import_change",
	TYPE_CHANGE = "type_change",
}

/**
 * 修改检测结果
 */
export interface QaxChangeDetection {
	type: QaxChangeType
	filePath: string
	range: vscode.Range
	oldValue: string
	newValue?: string
	confidence: number // 0-1, 检测置信度
	metadata?: {
		symbolName?: string
		symbolType?: string
		references?: vscode.Location[]
		definitions?: vscode.Location[]
		[key: string]: any
	}
}

/**
 * 跳转建议
 */
export interface QaxJumpSuggestion {
	id: string
	filePath: string
	range: vscode.Range
	description: string
	changeType: QaxChangeType
	suggestedEdit?: {
		range: vscode.Range
		newText: string
		description: string
	}
	priority: number // 1-10, 优先级
	relatedChange: QaxChangeDetection
}

/**
 * LSP 符号信息
 */
export interface QaxSymbolInfo {
	name: string
	kind: vscode.SymbolKind
	location: vscode.Location
	containerName?: string
	detail?: string
	references?: vscode.Location[]
	definitions?: vscode.Location[]
}

/**
 * AST 节点信息
 */
export interface QaxASTNode {
	type: string
	text: string
	range: vscode.Range
	children?: QaxASTNode[]
	parent?: QaxASTNode
	metadata?: {
		[key: string]: any
	}
}

/**
 * 符号级别的变更检测单元
 */
export interface QaxSymbolChange {
	symbolType: 'identifier' | 'function_call' | 'string_literal' | 'number_literal' | 'statement' | 'expression'
	range: vscode.Range
	oldText: string
	newText: string
	symbolName?: string // 对于标识符，这是符号名称
	context?: {
		parentSymbol?: string
		scope?: string
		[key: string]: any
	}
}

/**
 * 符号检测器接口
 */
export interface QaxSymbolDetector {
	/**
	 * 检测文档中的符号
	 */
	detectSymbols(document: vscode.TextDocument): QaxSymbolChange[]

	/**
	 * 比较两个文档的符号差异
	 */
	compareSymbols(beforeDoc: vscode.TextDocument, afterDoc: vscode.TextDocument): QaxSymbolChange[]

	/**
	 * 判断位置是否在符号内
	 */
	getSymbolAtPosition(document: vscode.TextDocument, position: vscode.Position): QaxSymbolChange | null
}

/**
 * 代码分析上下文
 */
export interface QaxAnalysisContext {
	filePath: string
	document: vscode.TextDocument
	changes: vscode.TextDocumentContentChangeEvent[]
	beforeContent: string
	afterContent: string
	languageId: string
	symbols?: QaxSymbolInfo[]
	astNodes?: QaxASTNode[]
}

/**
 * QaxNextEdit 配置
 */
export interface QaxNextEditConfig {
	enabled: boolean
	enableLSPIntegration: boolean
	enableASTAnalysis: boolean
	debounceDelayMs: number
	maxSuggestions: number
	confidenceThreshold: number // 最小置信度阈值
	supportedLanguages: string[]
	analysisDepth: "shallow" | "deep" // 分析深度
}

/**
 * QaxNextEdit 事件类型
 */
export enum QaxNextEditEventType {
	CHANGE_DETECTED = "change_detected",
	SUGGESTIONS_GENERATED = "suggestions_generated",
	SUGGESTION_APPLIED = "suggestion_applied",
	SUGGESTION_IGNORED = "suggestion_ignored",
	ANALYSIS_STARTED = "analysis_started",
	ANALYSIS_COMPLETED = "analysis_completed",
	ERROR_OCCURRED = "error_occurred",
}

/**
 * QaxNextEdit 事件
 */
export interface QaxNextEditEvent {
	type: QaxNextEditEventType
	timestamp: Date
	filePath?: string
	data?: any
}

/**
 * 事件回调函数
 */
export type QaxNextEditEventCallback = (event: QaxNextEditEvent) => void

/**
 * 默认配置
 */
export const DEFAULT_QAX_NEXT_EDIT_CONFIG: QaxNextEditConfig = {
	enabled: true,
	enableLSPIntegration: true,
	enableASTAnalysis: true,
	debounceDelayMs: 800, // 减少延迟，提高响应性
	maxSuggestions: 8,
	confidenceThreshold: 0.4,
	supportedLanguages: [
		"typescript",
		"javascript",
		"python",
		"java",
		"csharp",
		"cpp",
		"c",
		"rust",
		"go",
		"php",
		"ruby",
		"swift",
		"kotlin",
	],
	analysisDepth: "deep",
}

/**
 * 分析结果
 */
export interface QaxAnalysisResult {
	detectedChanges: QaxChangeDetection[]
	jumpSuggestions: QaxJumpSuggestion[]
	analysisTime: number
	confidence: number
	metadata?: {
		lspAvailable: boolean
		astParsed: boolean
		symbolsFound: number
		referencesFound: number
		[key: string]: any
	}
}

/**
 * 服务状态
 */
export interface QaxNextEditServiceState {
	isEnabled: boolean
	isAnalyzing: boolean
	lastAnalysisTime?: Date
	activeFile?: string
	pendingChanges: Map<string, QaxAnalysisContext>
	cachedResults: Map<string, QaxAnalysisResult>
}
