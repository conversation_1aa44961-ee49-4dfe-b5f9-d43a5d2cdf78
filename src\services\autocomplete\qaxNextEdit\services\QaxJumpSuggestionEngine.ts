import * as vscode from "vscode"
import * as path from "path"
import {
	QaxChangeDetection,
	QaxJumpSuggestion,
	QaxChangeType,
	QaxAnalysisContext,
	QaxNextEditConfig,
} from "../types/QaxNextEditTypes"
import { QaxLSPService } from "./QaxLSPService"

/**
 * 跳转和建议引擎，提供精确的跳转位置和修改建议
 */
export class QaxJumpSuggestionEngine {
	private lspService: QaxLSPService
	private config: QaxNextEditConfig

	constructor(config: QaxNextEditConfig) {
		this.config = config
		this.lspService = QaxLSPService.getInstance()
	}

	/**
	 * 生成跳转建议
	 */
	async generateJumpSuggestions(changes: QaxChangeDetection[], context: QaxAnalysisContext): Promise<QaxJumpSuggestion[]> {
		const suggestions: QaxJumpSuggestion[] = []

		// 限制处理的变更数量，避免无限循环
		const MAX_CHANGES = 50
		const limitedChanges = changes.slice(0, MAX_CHANGES)

		if (changes.length > MAX_CHANGES) {
			console.warn(`🔍 QaxJumpSuggestionEngine: Too many changes (${changes.length}), processing only first ${MAX_CHANGES}`)
		}

		console.log(`🔍 QaxJumpSuggestionEngine: Generating jump suggestions for ${limitedChanges.length} changes`)

		for (const [index, change] of limitedChanges.entries()) {
			// 跳过无效的变更
			if (!change || !change.type || !change.oldValue || !change.newValue) {
				console.log(`🔍 QaxJumpSuggestionEngine: Skipping invalid change ${index + 1}`)
				continue
			}

			console.log(`🔍 QaxJumpSuggestionEngine: Processing change ${index + 1}: ${change.type}`)
			console.log(`  Old value: "${change.oldValue}"`)
			console.log(`  New value: "${change.newValue}"`)
			console.log(`  Confidence: ${change.confidence}`)
			console.log(`  Metadata:`, change.metadata)

			try {
				let changeSuggestions: QaxJumpSuggestion[] = []

				switch (change.type) {
					case QaxChangeType.VARIABLE_RENAME:
						console.log(`🔍 QaxJumpSuggestionEngine: Generating variable rename suggestions...`)
						changeSuggestions = await this.generateVariableRenameSuggestions(change, context)
						console.log(
							`🔍 QaxJumpSuggestionEngine: Generated ${changeSuggestions.length} variable rename suggestions`,
						)
						break

					case QaxChangeType.FUNCTION_PARAMETER_CHANGE:
						console.log(`🔍 QaxJumpSuggestionEngine: Generating parameter change suggestions...`)
						changeSuggestions = await this.generateParameterChangeSuggestions(change, context)
						console.log(
							`🔍 QaxJumpSuggestionEngine: Generated ${changeSuggestions.length} parameter change suggestions`,
						)
						break

					case QaxChangeType.FUNCTION_CALL_DELETION:
						console.log(`🔍 QaxJumpSuggestionEngine: Generating call deletion suggestions...`)
						changeSuggestions = await this.generateCallDeletionSuggestions(change, context)
						console.log(`🔍 QaxJumpSuggestionEngine: Generated ${changeSuggestions.length} call deletion suggestions`)
						break

					case QaxChangeType.VARIABLE_DELETION:
						console.log(`🔍 QaxJumpSuggestionEngine: Generating variable deletion suggestions...`)
						changeSuggestions = await this.generateVariableDeletionSuggestions(change, context)
						console.log(
							`🔍 QaxJumpSuggestionEngine: Generated ${changeSuggestions.length} variable deletion suggestions`,
						)
						break

					case QaxChangeType.IMPORT_CHANGE:
						console.log(`🔍 QaxJumpSuggestionEngine: Generating import change suggestions...`)
						changeSuggestions = await this.generateImportChangeSuggestions(change, context)
						console.log(`🔍 QaxJumpSuggestionEngine: Generated ${changeSuggestions.length} import change suggestions`)
						break

					case QaxChangeType.TYPE_CHANGE:
						console.log(`🔍 QaxJumpSuggestionEngine: Generating type change suggestions...`)
						changeSuggestions = await this.generateTypeChangeSuggestions(change, context)
						console.log(`🔍 QaxJumpSuggestionEngine: Generated ${changeSuggestions.length} type change suggestions`)
						break

					default:
						console.log(`🔍 QaxJumpSuggestionEngine: Unsupported change type: ${change.type}, skipping`)
						continue // 跳过这个变更，不处理
				}

				suggestions.push(...changeSuggestions)
				console.log(`🔍 QaxJumpSuggestionEngine: Total suggestions so far: ${suggestions.length}`)
			} catch (error) {
				console.error(`🔍 QaxJumpSuggestionEngine: Failed to generate suggestions for ${change.type}:`, error)
			}
		}

		console.log(`🔍 QaxJumpSuggestionEngine: Generated ${suggestions.length} total suggestions before sorting`)

		// 排序和限制数量
		const sortedSuggestions = suggestions.sort((a, b) => b.priority - a.priority).slice(0, this.config.maxSuggestions)

		console.log(`🔍 QaxJumpSuggestionEngine: Final suggestions after sorting and limiting: ${sortedSuggestions.length}`)
		sortedSuggestions.forEach((suggestion, index) => {
			console.log(
				`  Suggestion ${index + 1}: ${suggestion.changeType} - ${suggestion.description} (priority: ${suggestion.priority})`,
			)
		})

		return sortedSuggestions
	}

	/**
	 * 生成变量重命名建议
	 */
	private async generateVariableRenameSuggestions(
		change: QaxChangeDetection,
		context: QaxAnalysisContext,
	): Promise<QaxJumpSuggestion[]> {
		const suggestions: QaxJumpSuggestion[] = []

		console.log(`🔍 QaxJumpSuggestionEngine: Generating variable rename suggestions`)
		console.log(`  Change new value: "${change.newValue}"`)
		console.log(`  Change metadata:`, change.metadata)

		if (!change.newValue) {
			console.log(`🔍 QaxJumpSuggestionEngine: No new value for variable rename, skipping`)
			return suggestions
		}

		// 检查是否有符号名称信息
		const symbolName = change.metadata?.symbolName || change.oldValue
		console.log(`🔍 QaxJumpSuggestionEngine: Symbol name: "${symbolName}"`)

		// 获取引用和定义信息
		const references = (change.metadata?.references as vscode.Location[]) || []
		const definitions = (change.metadata?.definitions as vscode.Location[]) || []
		const isDefinitionLocation = change.metadata?.isDefinitionLocation === true

		console.log(`🔍 QaxJumpSuggestionEngine: Found ${references.length} references in metadata`)
		console.log(`🔍 QaxJumpSuggestionEngine: Found ${definitions.length} definitions in metadata`)
		console.log(`🔍 QaxJumpSuggestionEngine: Current location is ${isDefinitionLocation ? 'definition' : 'reference'}`)

		// 根据修改位置类型决定建议策略
		if (isDefinitionLocation) {
			console.log(`🔍 QaxJumpSuggestionEngine: Definition modified - suggesting all reference updates`)
			return this.generateDefinitionBasedSuggestions(change, context, references, definitions)
		} else {
			console.log(`🔍 QaxJumpSuggestionEngine: Reference modified - suggesting definition update first`)
			return this.generateReferenceBasedSuggestions(change, context, references, definitions)
		}
	}

	/**
	 * 生成函数参数变更建议
	 */
	private async generateParameterChangeSuggestions(
		change: QaxChangeDetection,
		context: QaxAnalysisContext,
	): Promise<QaxJumpSuggestion[]> {
		const suggestions: QaxJumpSuggestion[] = []

		if (!change.metadata?.symbolName) {
			return suggestions
		}

		const functionName = change.metadata.symbolName

		// 查找函数调用位置
		const callSites = await this.findFunctionCallSites(functionName, context)

		for (const callSite of callSites) {
			const suggestion: QaxJumpSuggestion = {
				id: `param-change-${functionName}-${callSite.uri.fsPath}-${callSite.range.start.line}`,
				filePath: callSite.uri.fsPath,
				range: callSite.range,
				description: `Update function call '${functionName}' to match new parameters`,
				changeType: QaxChangeType.FUNCTION_PARAMETER_CHANGE,
				suggestedEdit: {
					range: callSite.range,
					newText: this.generateUpdatedFunctionCall(callSite, change),
					description: `Update parameters for '${functionName}'`,
				},
				priority: this.calculatePriority(callSite, context, change),
				relatedChange: change,
			}

			suggestions.push(suggestion)
		}

		return suggestions
	}

	/**
	 * 生成函数调用删除建议
	 */
	private async generateCallDeletionSuggestions(
		change: QaxChangeDetection,
		context: QaxAnalysisContext,
	): Promise<QaxJumpSuggestion[]> {
		const suggestions: QaxJumpSuggestion[] = []

		if (!change.metadata?.symbolName) {
			return suggestions
		}

		const functionName = change.metadata.symbolName

		// 查找其他相关的函数调用
		const relatedCalls = await this.findFunctionCallSites(functionName, context)

		for (const callSite of relatedCalls) {
			// 跳过已删除的调用
			if (callSite.uri.fsPath === context.filePath && callSite.range.intersection(change.range)) {
				continue
			}

			const suggestion: QaxJumpSuggestion = {
				id: `call-deletion-${functionName}-${callSite.uri.fsPath}-${callSite.range.start.line}`,
				filePath: callSite.uri.fsPath,
				range: callSite.range,
				description: `Consider removing this call to '${functionName}' as well`,
				changeType: QaxChangeType.FUNCTION_CALL_DELETION,
				suggestedEdit: {
					range: callSite.range,
					newText: "",
					description: `Remove call to '${functionName}'`,
				},
				priority: this.calculatePriority(callSite, context, change) - 2, // 降低优先级
				relatedChange: change,
			}

			suggestions.push(suggestion)
		}

		return suggestions
	}

	/**
	 * 生成变量删除建议
	 */
	private async generateVariableDeletionSuggestions(
		change: QaxChangeDetection,
		context: QaxAnalysisContext,
	): Promise<QaxJumpSuggestion[]> {
		const suggestions: QaxJumpSuggestion[] = []

		if (!change.metadata?.symbolName) {
			return suggestions
		}

		// 查找变量的所有使用位置
		const references = (change.metadata.references as vscode.Location[]) || []

		for (const reference of references) {
			// 跳过删除位置
			if (reference.uri.fsPath === context.filePath && reference.range.intersection(change.range)) {
				continue
			}

			const suggestion: QaxJumpSuggestion = {
				id: `var-deletion-${change.metadata.symbolName}-${reference.uri.fsPath}-${reference.range.start.line}`,
				filePath: reference.uri.fsPath,
				range: reference.range,
				description: `Remove usage of deleted variable '${change.metadata.symbolName}'`,
				changeType: QaxChangeType.VARIABLE_DELETION,
				suggestedEdit: {
					range: reference.range,
					newText: "",
					description: `Remove reference to '${change.metadata.symbolName}'`,
				},
				priority: this.calculatePriority(reference, context, change),
				relatedChange: change,
			}

			suggestions.push(suggestion)
		}

		return suggestions
	}

	/**
	 * 生成导入变更建议
	 */
	private async generateImportChangeSuggestions(
		change: QaxChangeDetection,
		context: QaxAnalysisContext,
	): Promise<QaxJumpSuggestion[]> {
		// 暂时返回空数组，可以后续实现
		return []
	}

	/**
	 * 生成类型变更建议
	 */
	private async generateTypeChangeSuggestions(
		change: QaxChangeDetection,
		context: QaxAnalysisContext,
	): Promise<QaxJumpSuggestion[]> {
		// 暂时返回空数组，可以后续实现
		return []
	}

	/**
	 * 查找函数调用位置
	 */
	private async findFunctionCallSites(functionName: string, context: QaxAnalysisContext): Promise<vscode.Location[]> {
		const callSites: vscode.Location[] = []

		try {
			// 简化实现：只在当前文档中查找函数调用
			const document = context.document
			const content = document.getText()
			const lines = content.split("\n")

			const callPattern = new RegExp(`\\b${functionName}\\s*\\(`, "g")

			for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
				const line = lines[lineIndex]
				let match

				while ((match = callPattern.exec(line)) !== null) {
					const startPos = new vscode.Position(lineIndex, match.index)
					const endPos = new vscode.Position(lineIndex, match.index + match[0].length)
					const range = new vscode.Range(startPos, endPos)
					const location = new vscode.Location(document.uri, range)
					callSites.push(location)
				}
			}
		} catch (error) {
			console.warn("Failed to find function call sites:", error)
		}

		return callSites
	}

	/**
	 * 通过文本搜索查找引用
	 */
	private async findTextReferences(symbolName: string, context: QaxAnalysisContext): Promise<vscode.Location[]> {
		const references: vscode.Location[] = []

		try {
			console.log(`🔍 QaxJumpSuggestionEngine: Searching for text references of "${symbolName}"`)

			// 在当前文档的原始内容中搜索（使用 beforeContent）
			const document = context.document
			const content = context.beforeContent // 使用修改前的内容
			const lines = content.split("\n")

			// 创建匹配模式：单词边界确保精确匹配
			const pattern = new RegExp(`\\b${symbolName.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}\\b`, "g")

			for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
				const line = lines[lineIndex]
				let match

				while ((match = pattern.exec(line)) !== null) {
					const startPos = new vscode.Position(lineIndex, match.index)
					const endPos = new vscode.Position(lineIndex, match.index + symbolName.length)
					const range = new vscode.Range(startPos, endPos)
					const location = new vscode.Location(document.uri, range)
					references.push(location)

					console.log(`  Found reference at line ${lineIndex + 1}, column ${match.index + 1}`)
				}
			}

			// 也可以在工作区的其他文件中搜索（简化版本）
			const workspaceFiles = vscode.workspace.textDocuments.filter(
				(doc) => doc.uri.fsPath !== context.filePath && doc.languageId === context.languageId,
			)

			for (const doc of workspaceFiles) {
				const docContent = doc.getText()
				const docLines = docContent.split("\n")

				for (let lineIndex = 0; lineIndex < docLines.length; lineIndex++) {
					const line = docLines[lineIndex]
					let match

					while ((match = pattern.exec(line)) !== null) {
						const startPos = new vscode.Position(lineIndex, match.index)
						const endPos = new vscode.Position(lineIndex, match.index + symbolName.length)
						const range = new vscode.Range(startPos, endPos)
						const location = new vscode.Location(doc.uri, range)
						references.push(location)

						console.log(`  Found reference in ${doc.uri.fsPath} at line ${lineIndex + 1}, column ${match.index + 1}`)
					}
				}
			}
		} catch (error) {
			console.warn("🔍 QaxJumpSuggestionEngine: Failed to find text references:", error)
		}

		console.log(`🔍 QaxJumpSuggestionEngine: Found ${references.length} text references for "${symbolName}"`)
		return references
	}

	/**
	 * 生成更新后的函数调用
	 */
	private generateUpdatedFunctionCall(callSite: vscode.Location, change: QaxChangeDetection): string {
		// 这里需要根据具体的参数变更生成新的函数调用
		// 暂时返回原始文本，实际实现需要解析函数调用并更新参数
		return "/* TODO: Update function call parameters */"
	}

	/**
	 * 计算建议的优先级
	 */
	private calculatePriority(location: vscode.Location, context: QaxAnalysisContext, change: QaxChangeDetection): number {
		let priority = 5 // 基础优先级

		// 同一文件的建议优先级更高
		if (location.uri.fsPath === context.filePath) {
			priority += 3
		}

		// 距离变更位置越近，优先级越高
		if (location.uri.fsPath === context.filePath) {
			const lineDiff = Math.abs(location.range.start.line - change.range.start.line)
			if (lineDiff <= 10) {
				priority += 2
			} else if (lineDiff <= 50) {
				priority += 1
			}
		}

		// 根据置信度调整优先级
		priority += Math.floor(change.confidence * 3)

		// 根据文件类型调整优先级
		const ext = path.extname(location.uri.fsPath).toLowerCase()
		if ([".ts", ".tsx", ".js", ".jsx"].includes(ext)) {
			priority += 1 // TypeScript/JavaScript 文件优先级稍高
		}

		return Math.max(1, Math.min(10, priority)) // 限制在 1-10 范围内
	}

	/**
	 * 检查引用是否是当前修改位置
	 */
	private isCurrentChangeLocation(reference: vscode.Location, change: QaxChangeDetection, context: QaxAnalysisContext): boolean {
		if (reference.uri.fsPath !== context.filePath) {
			return false
		}

		// 检查是否与变更范围重叠
		const refRange = reference.range
		const changeRange = change.range

		// 如果在同一行且有重叠，则认为是当前修改位置
		if (refRange.start.line === changeRange.start.line) {
			const refStart = refRange.start.character
			const refEnd = refRange.end.character
			const changeStart = changeRange.start.character
			const changeEnd = changeRange.end.character

			// 检查是否有重叠
			return !(refEnd <= changeStart || changeEnd <= refStart)
		}

		return false
	}

	/**
	 * 验证引用是否有效
	 */
	private isValidReference(reference: vscode.Location, symbolName: string, context: QaxAnalysisContext): boolean {
		try {
			// 获取引用位置的文本内容
			const document = reference.uri.fsPath === context.filePath ?
				context.document :
				vscode.workspace.textDocuments.find(doc => doc.uri.fsPath === reference.uri.fsPath)

			if (!document) {
				console.log(`    Cannot validate reference: document not found for ${reference.uri.fsPath}`)
				return true // 如果找不到文档，假设引用有效
			}

			const line = document.lineAt(reference.range.start.line)
			const referenceText = line.text.substring(
				reference.range.start.character,
				reference.range.end.character
			)

			// 检查引用文本是否匹配符号名称
			const isMatch = referenceText === symbolName
			if (!isMatch) {
				console.log(`    Reference text "${referenceText}" doesn't match symbol "${symbolName}"`)
			}

			return isMatch
		} catch (error) {
			console.warn(`    Failed to validate reference:`, error)
			return true // 验证失败时假设引用有效
		}
	}

	/**
	 * 获取用于搜索引用的位置列表
	 */
	private getSearchPositions(change: QaxChangeDetection, symbolName: string, context: QaxAnalysisContext): vscode.Position[] {
		const positions: vscode.Position[] = []

		// 1. 使用变更的起始位置
		positions.push(change.range.start)

		// 2. 使用变更的中间位置
		if (change.range.start.line === change.range.end.line) {
			const midChar = Math.floor((change.range.start.character + change.range.end.character) / 2)
			positions.push(new vscode.Position(change.range.start.line, midChar))
		}

		// 3. 使用变更的结束位置
		if (!change.range.start.isEqual(change.range.end)) {
			positions.push(change.range.end)
		}

		return positions
	}

	/**
	 * 改进的文本搜索，只查找完整的标识符匹配
	 */
	private async findTextReferencesImproved(symbolName: string, context: QaxAnalysisContext): Promise<vscode.Location[]> {
		const references: vscode.Location[] = []

		if (!symbolName || symbolName.length === 0) {
			return references
		}

		try {
			console.log(`🔍 QaxJumpSuggestionEngine: Improved text search for "${symbolName}"`)

			// 创建正则表达式，只匹配完整的标识符
			const pattern = new RegExp(`\\b${symbolName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'g')

			// 在当前文档的原始内容中搜索（使用 beforeContent）
			const content = context.beforeContent
			const lines = content.split("\n")

			for (const [lineIndex, line] of lines.entries()) {
				let match: RegExpExecArray | null
				while ((match = pattern.exec(line)) !== null) {
					const startPos = new vscode.Position(lineIndex, match.index)
					const endPos = new vscode.Position(lineIndex, match.index + symbolName.length)
					const range = new vscode.Range(startPos, endPos)
					const location = new vscode.Location(context.document.uri, range)
					references.push(location)

					console.log(`  Found reference at line ${lineIndex + 1}, column ${match.index + 1}`)
				}
			}

			console.log(`🔍 QaxJumpSuggestionEngine: Improved text search found ${references.length} references for "${symbolName}"`)
		} catch (error) {
			console.warn("🔍 QaxJumpSuggestionEngine: Failed improved text search:", error)
		}

		return references
	}

	/**
	 * 生成基于定义修改的建议（定义被修改，建议更新所有引用）
	 */
	private async generateDefinitionBasedSuggestions(
		change: QaxChangeDetection,
		context: QaxAnalysisContext,
		references: vscode.Location[],
		definitions: vscode.Location[]
	): Promise<QaxJumpSuggestion[]> {
		const suggestions: QaxJumpSuggestion[] = []
		const symbolName = change.metadata?.symbolName || change.oldValue

		console.log(`🔍 QaxJumpSuggestionEngine: Generating definition-based suggestions`)
		console.log(`  Will suggest updating ${references.length} references`)

		// 为每个引用位置生成建议
		for (const [index, reference] of references.entries()) {
			console.log(`🔍 QaxJumpSuggestionEngine: Processing reference ${index + 1}:`)
			console.log(`  File: ${reference.uri.fsPath}`)
			console.log(`  Range: ${reference.range.start.line}:${reference.range.start.character} - ${reference.range.end.line}:${reference.range.end.character}`)

			// 跳过当前修改位置（定义位置）
			if (this.isCurrentChangeLocation(reference, change, context)) {
				console.log(`  ❌ Skipping current change location (definition)`)
				continue
			}

			// 验证引用有效性
			if (!this.isValidReference(reference, symbolName, context)) {
				console.log(`  ❌ Skipping invalid reference`)
				continue
			}

			// 计算优先级（引用的优先级相对较低）
			const priority = this.calculatePriority(reference, context, change)

			const suggestion: QaxJumpSuggestion = {
				id: `def-to-ref-${symbolName}-${reference.uri.fsPath}-${reference.range.start.line}`,
				filePath: reference.uri.fsPath,
				range: reference.range,
				description: `Update reference to '${change.newValue}' in ${this.getRelativeFilePath(reference.uri.fsPath, context)}`,
				changeType: change.type,
				suggestedEdit: {
					range: reference.range,
					newText: change.newValue || "",
					description: `Update reference from '${symbolName}' to '${change.newValue}'`,
				},
				priority,
				relatedChange: change,
			}

			suggestions.push(suggestion)
			console.log(`  ✅ Added reference update suggestion: ${suggestion.description}`)
		}

		console.log(`🔍 QaxJumpSuggestionEngine: Generated ${suggestions.length} definition-based suggestions`)
		return suggestions
	}

	/**
	 * 生成基于引用修改的建议（引用被修改，优先建议更新定义）
	 */
	private async generateReferenceBasedSuggestions(
		change: QaxChangeDetection,
		context: QaxAnalysisContext,
		references: vscode.Location[],
		definitions: vscode.Location[]
	): Promise<QaxJumpSuggestion[]> {
		const suggestions: QaxJumpSuggestion[] = []
		const symbolName = change.metadata?.symbolName || change.oldValue

		console.log(`🔍 QaxJumpSuggestionEngine: Generating reference-based suggestions`)
		console.log(`  Will prioritize ${definitions.length} definitions, then ${references.length} other references`)

		// 1. 首先建议更新定义位置（高优先级）
		for (const [index, definition] of definitions.entries()) {
			console.log(`🔍 QaxJumpSuggestionEngine: Processing definition ${index + 1}:`)
			console.log(`  File: ${definition.uri.fsPath}`)
			console.log(`  Range: ${definition.range.start.line}:${definition.range.start.character} - ${definition.range.end.line}:${definition.range.end.character}`)

			// 跳过当前修改位置
			if (this.isCurrentChangeLocation(definition, change, context)) {
				console.log(`  ❌ Skipping current change location`)
				continue
			}

			// 定义的优先级最高
			const priority = 10

			const suggestion: QaxJumpSuggestion = {
				id: `ref-to-def-${symbolName}-${definition.uri.fsPath}-${definition.range.start.line}`,
				filePath: definition.uri.fsPath,
				range: definition.range,
				description: `Update definition to '${change.newValue}' in ${this.getRelativeFilePath(definition.uri.fsPath, context)}`,
				changeType: change.type,
				suggestedEdit: {
					range: definition.range,
					newText: change.newValue || "",
					description: `Update definition from '${symbolName}' to '${change.newValue}'`,
				},
				priority,
				relatedChange: change,
			}

			suggestions.push(suggestion)
			console.log(`  ✅ Added definition update suggestion (priority ${priority}): ${suggestion.description}`)
		}

		// 2. 然后建议更新其他引用位置（较低优先级）
		for (const [index, reference] of references.entries()) {
			console.log(`🔍 QaxJumpSuggestionEngine: Processing other reference ${index + 1}:`)
			console.log(`  File: ${reference.uri.fsPath}`)
			console.log(`  Range: ${reference.range.start.line}:${reference.range.start.character} - ${reference.range.end.line}:${reference.range.end.character}`)

			// 跳过当前修改位置
			if (this.isCurrentChangeLocation(reference, change, context)) {
				console.log(`  ❌ Skipping current change location`)
				continue
			}

			// 验证引用有效性
			if (!this.isValidReference(reference, symbolName, context)) {
				console.log(`  ❌ Skipping invalid reference`)
				continue
			}

			// 其他引用的优先级较低
			const priority = this.calculatePriority(reference, context, change)

			const suggestion: QaxJumpSuggestion = {
				id: `ref-to-ref-${symbolName}-${reference.uri.fsPath}-${reference.range.start.line}`,
				filePath: reference.uri.fsPath,
				range: reference.range,
				description: `Update reference to '${change.newValue}' in ${this.getRelativeFilePath(reference.uri.fsPath, context)}`,
				changeType: change.type,
				suggestedEdit: {
					range: reference.range,
					newText: change.newValue || "",
					description: `Update reference from '${symbolName}' to '${change.newValue}'`,
				},
				priority,
				relatedChange: change,
			}

			suggestions.push(suggestion)
			console.log(`  ✅ Added reference update suggestion (priority ${priority}): ${suggestion.description}`)
		}

		console.log(`🔍 QaxJumpSuggestionEngine: Generated ${suggestions.length} reference-based suggestions`)
		return suggestions
	}

	/**
	 * 获取相对文件路径
	 */
	private getRelativeFilePath(filePath: string, context: QaxAnalysisContext): string {
		if (filePath === context.filePath) {
			return "this file"
		}

		// 简单的相对路径计算
		const contextDir = context.filePath.split('/').slice(0, -1).join('/')
		if (filePath.startsWith(contextDir)) {
			return filePath.substring(contextDir.length + 1)
		}

		// 返回文件名
		return filePath.split('/').pop() || filePath
	}

	/**
	 * 更新配置
	 */
	updateConfig(config: QaxNextEditConfig): void {
		this.config = config
	}
}
