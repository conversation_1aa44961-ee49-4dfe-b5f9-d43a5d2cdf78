import * as vscode from "vscode"
import * as path from "path"
import {
	QaxChangeDetection,
	QaxJumpSuggestion,
	QaxChangeType,
	QaxAnalysisContext,
	QaxNextEditConfig,
} from "../types/QaxNextEditTypes"
import { QaxLSPService } from "./QaxLSPService"

/**
 * 跳转和建议引擎，提供精确的跳转位置和修改建议
 */
export class QaxJumpSuggestionEngine {
	private lspService: QaxLSPService
	private config: QaxNextEditConfig

	constructor(config: QaxNextEditConfig) {
		this.config = config
		this.lspService = QaxLSPService.getInstance()
	}

	/**
	 * 生成跳转建议
	 */
	async generateJumpSuggestions(changes: QaxChangeDetection[], context: QaxAnalysisContext): Promise<QaxJumpSuggestion[]> {
		const suggestions: QaxJumpSuggestion[] = []

		console.log(`🔍 QaxJumpSuggestionEngine: Generating jump suggestions for ${changes.length} changes`)

		for (const [index, change] of changes.entries()) {
			console.log(`🔍 QaxJumpSuggestionEngine: Processing change ${index + 1}: ${change.type}`)
			console.log(`  Old value: "${change.oldValue}"`)
			console.log(`  New value: "${change.newValue}"`)
			console.log(`  Confidence: ${change.confidence}`)
			console.log(`  Metadata:`, change.metadata)

			try {
				let changeSuggestions: QaxJumpSuggestion[] = []

				switch (change.type) {
					case QaxChangeType.VARIABLE_RENAME:
						console.log(`🔍 QaxJumpSuggestionEngine: Generating variable rename suggestions...`)
						changeSuggestions = await this.generateVariableRenameSuggestions(change, context)
						console.log(
							`🔍 QaxJumpSuggestionEngine: Generated ${changeSuggestions.length} variable rename suggestions`,
						)
						break

					case QaxChangeType.FUNCTION_PARAMETER_CHANGE:
						console.log(`🔍 QaxJumpSuggestionEngine: Generating parameter change suggestions...`)
						changeSuggestions = await this.generateParameterChangeSuggestions(change, context)
						console.log(
							`🔍 QaxJumpSuggestionEngine: Generated ${changeSuggestions.length} parameter change suggestions`,
						)
						break

					case QaxChangeType.FUNCTION_CALL_DELETION:
						console.log(`🔍 QaxJumpSuggestionEngine: Generating call deletion suggestions...`)
						changeSuggestions = await this.generateCallDeletionSuggestions(change, context)
						console.log(`🔍 QaxJumpSuggestionEngine: Generated ${changeSuggestions.length} call deletion suggestions`)
						break

					case QaxChangeType.VARIABLE_DELETION:
						console.log(`🔍 QaxJumpSuggestionEngine: Generating variable deletion suggestions...`)
						changeSuggestions = await this.generateVariableDeletionSuggestions(change, context)
						console.log(
							`🔍 QaxJumpSuggestionEngine: Generated ${changeSuggestions.length} variable deletion suggestions`,
						)
						break

					case QaxChangeType.IMPORT_CHANGE:
						console.log(`🔍 QaxJumpSuggestionEngine: Generating import change suggestions...`)
						changeSuggestions = await this.generateImportChangeSuggestions(change, context)
						console.log(`🔍 QaxJumpSuggestionEngine: Generated ${changeSuggestions.length} import change suggestions`)
						break

					case QaxChangeType.TYPE_CHANGE:
						console.log(`🔍 QaxJumpSuggestionEngine: Generating type change suggestions...`)
						changeSuggestions = await this.generateTypeChangeSuggestions(change, context)
						console.log(`🔍 QaxJumpSuggestionEngine: Generated ${changeSuggestions.length} type change suggestions`)
						break

					default:
						console.log(`🔍 QaxJumpSuggestionEngine: Unknown change type: ${change.type}`)
						break
				}

				suggestions.push(...changeSuggestions)
				console.log(`🔍 QaxJumpSuggestionEngine: Total suggestions so far: ${suggestions.length}`)
			} catch (error) {
				console.error(`🔍 QaxJumpSuggestionEngine: Failed to generate suggestions for ${change.type}:`, error)
			}
		}

		console.log(`🔍 QaxJumpSuggestionEngine: Generated ${suggestions.length} total suggestions before sorting`)

		// 排序和限制数量
		const sortedSuggestions = suggestions.sort((a, b) => b.priority - a.priority).slice(0, this.config.maxSuggestions)

		console.log(`🔍 QaxJumpSuggestionEngine: Final suggestions after sorting and limiting: ${sortedSuggestions.length}`)
		sortedSuggestions.forEach((suggestion, index) => {
			console.log(
				`  Suggestion ${index + 1}: ${suggestion.changeType} - ${suggestion.description} (priority: ${suggestion.priority})`,
			)
		})

		return sortedSuggestions
	}

	/**
	 * 生成变量重命名建议
	 */
	private async generateVariableRenameSuggestions(
		change: QaxChangeDetection,
		context: QaxAnalysisContext,
	): Promise<QaxJumpSuggestion[]> {
		const suggestions: QaxJumpSuggestion[] = []

		console.log(`🔍 QaxJumpSuggestionEngine: Generating variable rename suggestions`)
		console.log(`  Change new value: "${change.newValue}"`)
		console.log(`  Change metadata:`, change.metadata)

		if (!change.newValue) {
			console.log(`🔍 QaxJumpSuggestionEngine: No new value for variable rename, skipping`)
			return suggestions
		}

		// 检查是否有符号名称信息
		const symbolName = change.metadata?.symbolName || change.oldValue
		console.log(`🔍 QaxJumpSuggestionEngine: Symbol name: "${symbolName}"`)

		// 获取所有引用位置
		let references = (change.metadata?.references as vscode.Location[]) || []
		console.log(`🔍 QaxJumpSuggestionEngine: Found ${references.length} references in metadata`)

		// 如果没有 LSP 引用信息，尝试通过 LSP 获取
		if (references.length === 0) {
			console.log(`🔍 QaxJumpSuggestionEngine: No references in metadata, trying LSP...`)
			try {
				const position = change.range.start
				console.log(
					`🔍 QaxJumpSuggestionEngine: Getting LSP references at position ${position.line}:${position.character}`,
				)
				const lspReferences = await this.lspService.getReferences(context.document, position)
				console.log(`🔍 QaxJumpSuggestionEngine: LSP returned ${lspReferences.length} references`)
				references.push(...lspReferences)
			} catch (error) {
				console.warn("🔍 QaxJumpSuggestionEngine: Failed to get LSP references:", error)
			}
		}

		// 如果仍然没有引用，尝试简单的文本搜索
		if (references.length === 0 && symbolName) {
			console.log(`🔍 QaxJumpSuggestionEngine: No LSP references, trying text search for "${symbolName}"`)
			references = await this.findTextReferences(symbolName, context)
			console.log(`🔍 QaxJumpSuggestionEngine: Text search found ${references.length} references`)
		}

		console.log(`🔍 QaxJumpSuggestionEngine: Total references to process: ${references.length}`)

		// 为每个引用位置生成建议
		for (const [index, reference] of references.entries()) {
			console.log(`🔍 QaxJumpSuggestionEngine: Processing reference ${index + 1}:`)
			console.log(`  File: ${reference.uri.fsPath}`)
			console.log(
				`  Range: ${reference.range.start.line}:${reference.range.start.character} - ${reference.range.end.line}:${reference.range.end.character}`,
			)

			// 跳过当前修改位置（使用更宽松的比较）
			if (reference.uri.fsPath === context.filePath &&
				reference.range.start.line === change.range.start.line &&
				Math.abs(reference.range.start.character - change.range.start.character) <= 2) {
				console.log(`  Skipping current change location (line ${reference.range.start.line}, char ${reference.range.start.character})`)
				continue
			}

			const priority = this.calculatePriority(reference, context, change)
			console.log(`  Calculated priority: ${priority}`)

			const suggestion: QaxJumpSuggestion = {
				id: `rename-${symbolName}-${reference.uri.fsPath}-${reference.range.start.line}`,
				filePath: reference.uri.fsPath,
				range: reference.range,
				description: `Update variable name from '${change.oldValue}' to '${change.newValue}'`,
				changeType: QaxChangeType.VARIABLE_RENAME,
				suggestedEdit: {
					range: reference.range,
					newText: change.newValue,
					description: `Rename '${change.oldValue}' to '${change.newValue}'`,
				},
				priority,
				relatedChange: change,
			}

			suggestions.push(suggestion)
			console.log(`  Added suggestion: ${suggestion.description}`)
		}

		console.log(`🔍 QaxJumpSuggestionEngine: Generated ${suggestions.length} variable rename suggestions`)
		return suggestions
	}

	/**
	 * 生成函数参数变更建议
	 */
	private async generateParameterChangeSuggestions(
		change: QaxChangeDetection,
		context: QaxAnalysisContext,
	): Promise<QaxJumpSuggestion[]> {
		const suggestions: QaxJumpSuggestion[] = []

		if (!change.metadata?.symbolName) {
			return suggestions
		}

		const functionName = change.metadata.symbolName

		// 查找函数调用位置
		const callSites = await this.findFunctionCallSites(functionName, context)

		for (const callSite of callSites) {
			const suggestion: QaxJumpSuggestion = {
				id: `param-change-${functionName}-${callSite.uri.fsPath}-${callSite.range.start.line}`,
				filePath: callSite.uri.fsPath,
				range: callSite.range,
				description: `Update function call '${functionName}' to match new parameters`,
				changeType: QaxChangeType.FUNCTION_PARAMETER_CHANGE,
				suggestedEdit: {
					range: callSite.range,
					newText: this.generateUpdatedFunctionCall(callSite, change),
					description: `Update parameters for '${functionName}'`,
				},
				priority: this.calculatePriority(callSite, context, change),
				relatedChange: change,
			}

			suggestions.push(suggestion)
		}

		return suggestions
	}

	/**
	 * 生成函数调用删除建议
	 */
	private async generateCallDeletionSuggestions(
		change: QaxChangeDetection,
		context: QaxAnalysisContext,
	): Promise<QaxJumpSuggestion[]> {
		const suggestions: QaxJumpSuggestion[] = []

		if (!change.metadata?.symbolName) {
			return suggestions
		}

		const functionName = change.metadata.symbolName

		// 查找其他相关的函数调用
		const relatedCalls = await this.findFunctionCallSites(functionName, context)

		for (const callSite of relatedCalls) {
			// 跳过已删除的调用
			if (callSite.uri.fsPath === context.filePath && callSite.range.intersection(change.range)) {
				continue
			}

			const suggestion: QaxJumpSuggestion = {
				id: `call-deletion-${functionName}-${callSite.uri.fsPath}-${callSite.range.start.line}`,
				filePath: callSite.uri.fsPath,
				range: callSite.range,
				description: `Consider removing this call to '${functionName}' as well`,
				changeType: QaxChangeType.FUNCTION_CALL_DELETION,
				suggestedEdit: {
					range: callSite.range,
					newText: "",
					description: `Remove call to '${functionName}'`,
				},
				priority: this.calculatePriority(callSite, context, change) - 2, // 降低优先级
				relatedChange: change,
			}

			suggestions.push(suggestion)
		}

		return suggestions
	}

	/**
	 * 生成变量删除建议
	 */
	private async generateVariableDeletionSuggestions(
		change: QaxChangeDetection,
		context: QaxAnalysisContext,
	): Promise<QaxJumpSuggestion[]> {
		const suggestions: QaxJumpSuggestion[] = []

		if (!change.metadata?.symbolName) {
			return suggestions
		}

		// 查找变量的所有使用位置
		const references = (change.metadata.references as vscode.Location[]) || []

		for (const reference of references) {
			// 跳过删除位置
			if (reference.uri.fsPath === context.filePath && reference.range.intersection(change.range)) {
				continue
			}

			const suggestion: QaxJumpSuggestion = {
				id: `var-deletion-${change.metadata.symbolName}-${reference.uri.fsPath}-${reference.range.start.line}`,
				filePath: reference.uri.fsPath,
				range: reference.range,
				description: `Remove usage of deleted variable '${change.metadata.symbolName}'`,
				changeType: QaxChangeType.VARIABLE_DELETION,
				suggestedEdit: {
					range: reference.range,
					newText: "",
					description: `Remove reference to '${change.metadata.symbolName}'`,
				},
				priority: this.calculatePriority(reference, context, change),
				relatedChange: change,
			}

			suggestions.push(suggestion)
		}

		return suggestions
	}

	/**
	 * 生成导入变更建议
	 */
	private async generateImportChangeSuggestions(
		change: QaxChangeDetection,
		context: QaxAnalysisContext,
	): Promise<QaxJumpSuggestion[]> {
		// 暂时返回空数组，可以后续实现
		return []
	}

	/**
	 * 生成类型变更建议
	 */
	private async generateTypeChangeSuggestions(
		change: QaxChangeDetection,
		context: QaxAnalysisContext,
	): Promise<QaxJumpSuggestion[]> {
		// 暂时返回空数组，可以后续实现
		return []
	}

	/**
	 * 查找函数调用位置
	 */
	private async findFunctionCallSites(functionName: string, context: QaxAnalysisContext): Promise<vscode.Location[]> {
		const callSites: vscode.Location[] = []

		try {
			// 简化实现：只在当前文档中查找函数调用
			const document = context.document
			const content = document.getText()
			const lines = content.split("\n")

			const callPattern = new RegExp(`\\b${functionName}\\s*\\(`, "g")

			for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
				const line = lines[lineIndex]
				let match

				while ((match = callPattern.exec(line)) !== null) {
					const startPos = new vscode.Position(lineIndex, match.index)
					const endPos = new vscode.Position(lineIndex, match.index + match[0].length)
					const range = new vscode.Range(startPos, endPos)
					const location = new vscode.Location(document.uri, range)
					callSites.push(location)
				}
			}
		} catch (error) {
			console.warn("Failed to find function call sites:", error)
		}

		return callSites
	}

	/**
	 * 通过文本搜索查找引用
	 */
	private async findTextReferences(symbolName: string, context: QaxAnalysisContext): Promise<vscode.Location[]> {
		const references: vscode.Location[] = []

		try {
			console.log(`🔍 QaxJumpSuggestionEngine: Searching for text references of "${symbolName}"`)

			// 在当前文档的原始内容中搜索（使用 beforeContent）
			const document = context.document
			const content = context.beforeContent // 使用修改前的内容
			const lines = content.split("\n")

			// 创建匹配模式：单词边界确保精确匹配
			const pattern = new RegExp(`\\b${symbolName.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}\\b`, "g")

			for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
				const line = lines[lineIndex]
				let match

				while ((match = pattern.exec(line)) !== null) {
					const startPos = new vscode.Position(lineIndex, match.index)
					const endPos = new vscode.Position(lineIndex, match.index + symbolName.length)
					const range = new vscode.Range(startPos, endPos)
					const location = new vscode.Location(document.uri, range)
					references.push(location)

					console.log(`  Found reference at line ${lineIndex + 1}, column ${match.index + 1}`)
				}
			}

			// 也可以在工作区的其他文件中搜索（简化版本）
			const workspaceFiles = vscode.workspace.textDocuments.filter(
				(doc) => doc.uri.fsPath !== context.filePath && doc.languageId === context.languageId,
			)

			for (const doc of workspaceFiles) {
				const docContent = doc.getText()
				const docLines = docContent.split("\n")

				for (let lineIndex = 0; lineIndex < docLines.length; lineIndex++) {
					const line = docLines[lineIndex]
					let match

					while ((match = pattern.exec(line)) !== null) {
						const startPos = new vscode.Position(lineIndex, match.index)
						const endPos = new vscode.Position(lineIndex, match.index + symbolName.length)
						const range = new vscode.Range(startPos, endPos)
						const location = new vscode.Location(doc.uri, range)
						references.push(location)

						console.log(`  Found reference in ${doc.uri.fsPath} at line ${lineIndex + 1}, column ${match.index + 1}`)
					}
				}
			}
		} catch (error) {
			console.warn("🔍 QaxJumpSuggestionEngine: Failed to find text references:", error)
		}

		console.log(`🔍 QaxJumpSuggestionEngine: Found ${references.length} text references for "${symbolName}"`)
		return references
	}

	/**
	 * 生成更新后的函数调用
	 */
	private generateUpdatedFunctionCall(callSite: vscode.Location, change: QaxChangeDetection): string {
		// 这里需要根据具体的参数变更生成新的函数调用
		// 暂时返回原始文本，实际实现需要解析函数调用并更新参数
		return "/* TODO: Update function call parameters */"
	}

	/**
	 * 计算建议的优先级
	 */
	private calculatePriority(location: vscode.Location, context: QaxAnalysisContext, change: QaxChangeDetection): number {
		let priority = 5 // 基础优先级

		// 同一文件的建议优先级更高
		if (location.uri.fsPath === context.filePath) {
			priority += 3
		}

		// 距离变更位置越近，优先级越高
		if (location.uri.fsPath === context.filePath) {
			const lineDiff = Math.abs(location.range.start.line - change.range.start.line)
			if (lineDiff <= 10) {
				priority += 2
			} else if (lineDiff <= 50) {
				priority += 1
			}
		}

		// 根据置信度调整优先级
		priority += Math.floor(change.confidence * 3)

		// 根据文件类型调整优先级
		const ext = path.extname(location.uri.fsPath).toLowerCase()
		if ([".ts", ".tsx", ".js", ".jsx"].includes(ext)) {
			priority += 1 // TypeScript/JavaScript 文件优先级稍高
		}

		return Math.max(1, Math.min(10, priority)) // 限制在 1-10 范围内
	}

	/**
	 * 更新配置
	 */
	updateConfig(config: QaxNextEditConfig): void {
		this.config = config
	}
}
