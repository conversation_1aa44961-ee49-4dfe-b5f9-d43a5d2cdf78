import * as vscode from "vscode"
import {
	QaxAnalysisContext,
	QaxChangeDetection,
	QaxChangeType,
	QaxAnalysisResult,
	QaxNextEditConfig,
	QaxSymbolChange,
} from "../types/QaxNextEditTypes"
import { QaxLSPService } from "./QaxLSPService"
import { QaxASTService } from "./QaxASTService"
import { QaxJumpSuggestionEngine } from "./QaxJumpSuggestionEngine"
import { QaxSymbolDetector } from "./QaxSymbolDetector"

/**
 * 文本差异项
 */
interface TextDiffItem {
	type: 'insert' | 'delete' | 'replace' | 'no-change'
	oldValue: string
	newValue: string
	line: number
	character: number
}

/**
 * 修改检测器，分析代码变更并识别需要同步修改的位置
 * 重新设计为以符号和语句为基本检测单元
 */
export class QaxChangeDetector {
	private lspService: QaxLSPService
	private astService: QaxASTService
	private jumpSuggestionEngine: QaxJumpSuggestionEngine
	private symbolDetector: QaxSymbolDetector
	private config: QaxNextEditConfig
	private recentChanges: Map<string, { timestamp: number; changes: string[] }> = new Map()
	private readonly CHANGE_HISTORY_TIMEOUT = 5000 // 5秒内的变更被认为是连续的

	constructor(config: QaxNextEditConfig) {
		this.config = config
		this.lspService = QaxLSPService.getInstance()
		this.astService = QaxASTService.getInstance()
		this.jumpSuggestionEngine = new QaxJumpSuggestionEngine(config)
		this.symbolDetector = new QaxSymbolDetector()
	}

	/**
	 * 分析代码变更
	 */
	async analyzeChanges(context: QaxAnalysisContext): Promise<QaxAnalysisResult> {
		const startTime = Date.now()
		const detectedChanges: QaxChangeDetection[] = []

		console.log(`🔍 QaxChangeDetector: Starting analysis for ${context.filePath}`)
		console.log(`🔍 QaxChangeDetector: Language: ${context.languageId}`)
		console.log(`🔍 QaxChangeDetector: Changes count: ${context.changes.length}`)
		console.log(`🔍 QaxChangeDetector: Before content length: ${context.beforeContent.length}`)
		console.log(`🔍 QaxChangeDetector: After content length: ${context.afterContent.length}`)

		// 检查是否为重复的微小变更
		if (this.isRepeatedMinorChange(context)) {
			console.log(`🔍 QaxChangeDetector: Detected repeated minor changes, skipping analysis`)
			return {
				detectedChanges: [],
				jumpSuggestions: [],
				analysisTime: Date.now() - startTime,
				confidence: 0,
				metadata: {
					lspAvailable: false,
					astParsed: false,
					symbolsFound: 0,
					referencesFound: 0,
					skippedReason: "repeated_minor_changes",
				},
			}
		}

		// 详细记录每个变更
		context.changes.forEach((change, index) => {
			console.log(`🔍 QaxChangeDetector: Change ${index + 1}:`)
			console.log(
				`  Range: ${change.range.start.line}:${change.range.start.character} - ${change.range.end.line}:${change.range.end.character}`,
			)
			console.log(`  Range length: ${change.rangeLength}`)
			console.log(`  New text: "${change.text}"`)

			// 获取被替换的旧文本
			if (change.rangeLength > 0) {
				const oldText = context.beforeContent.substring(
					context.document.offsetAt(change.range.start),
					context.document.offsetAt(change.range.start) + change.rangeLength,
				)
				console.log(`  Old text: "${oldText}"`)
			}
		})

		try {
			// 检查语言是否支持
			if (!this.config.supportedLanguages.includes(context.languageId)) {
				console.log(`🔍 QaxChangeDetector: Unsupported language: ${context.languageId}`)
				console.log(`🔍 QaxChangeDetector: Supported languages: ${this.config.supportedLanguages.join(", ")}`)
				return {
					detectedChanges: [],
					jumpSuggestions: [],
					analysisTime: Date.now() - startTime,
					confidence: 0,
					metadata: {
						lspAvailable: false,
						astParsed: false,
						symbolsFound: 0,
						referencesFound: 0,
						unsupportedLanguage: true,
					},
				}
			}

			console.log(`🔍 QaxChangeDetector: Language ${context.languageId} is supported`)

			// 1. 基于文本差异的快速检测
			console.log(`🔍 QaxChangeDetector: Starting text-based change detection...`)
			const textChanges = await this.detectTextChanges(context)
			console.log(`🔍 QaxChangeDetector: Text-based detection found ${textChanges.length} changes`)
			textChanges.forEach((change, index) => {
				console.log(
					`  Text change ${index + 1}: ${change.type} - "${change.oldValue}" → "${change.newValue}" (confidence: ${change.confidence})`,
				)
			})
			detectedChanges.push(...textChanges)

			// 2. LSP 集成检测（如果启用）
			let lspAvailable = false
			if (this.config.enableLSPIntegration) {
				console.log(`🔍 QaxChangeDetector: Checking LSP availability...`)
				lspAvailable = await this.lspService.isLSPAvailable(context.document)
				console.log(`🔍 QaxChangeDetector: LSP available: ${lspAvailable}`)
				if (lspAvailable) {
					console.log(`🔍 QaxChangeDetector: Starting LSP-based change detection...`)
					const lspChanges = await this.detectLSPChanges(context)
					console.log(`🔍 QaxChangeDetector: LSP-based detection found ${lspChanges.length} changes`)
					lspChanges.forEach((change, index) => {
						console.log(
							`  LSP change ${index + 1}: ${change.type} - "${change.oldValue}" → "${change.newValue}" (confidence: ${change.confidence})`,
						)
					})
					detectedChanges.push(...lspChanges)
				}
			} else {
				console.log(`🔍 QaxChangeDetector: LSP integration disabled`)
			}

			// 3. AST 分析检测（如果启用）
			let astParsed = false
			if (this.config.enableASTAnalysis) {
				console.log(`🔍 QaxChangeDetector: Starting AST-based change detection...`)
				const astChanges = await this.detectASTChanges(context)
				console.log(`🔍 QaxChangeDetector: AST-based detection found ${astChanges.length} changes`)
				if (astChanges.length > 0) {
					astParsed = true
					astChanges.forEach((change, index) => {
						console.log(
							`  AST change ${index + 1}: ${change.type} - "${change.oldValue}" → "${change.newValue}" (confidence: ${change.confidence})`,
						)
					})
					detectedChanges.push(...astChanges)
				}
			} else {
				console.log(`🔍 QaxChangeDetector: AST analysis disabled`)
			}

			console.log(`🔍 QaxChangeDetector: Total detected changes before merging: ${detectedChanges.length}`)

			// 4. 合并和去重检测结果
			const mergedChanges = this.mergeDetectedChanges(detectedChanges)
			console.log(`🔍 QaxChangeDetector: Merged changes: ${mergedChanges.length}`)

			// 5. 过滤低置信度的检测结果
			const filteredChanges = mergedChanges.filter((change) => change.confidence >= this.config.confidenceThreshold)
			console.log(
				`🔍 QaxChangeDetector: Filtered changes (confidence >= ${this.config.confidenceThreshold}): ${filteredChanges.length}`,
			)
			filteredChanges.forEach((change, index) => {
				console.log(
					`  Filtered change ${index + 1}: ${change.type} - "${change.oldValue}" → "${change.newValue}" (confidence: ${change.confidence})`,
				)
			})

			// 6. 生成跳转建议
			console.log(`🔍 QaxChangeDetector: Generating jump suggestions...`)
			const jumpSuggestions = await this.generateJumpSuggestions(filteredChanges, context)
			console.log(`🔍 QaxChangeDetector: Generated ${jumpSuggestions.length} jump suggestions`)
			jumpSuggestions.forEach((suggestion, index) => {
				console.log(`  Jump suggestion ${index + 1}: ${suggestion.changeType} - ${suggestion.description}`)
				console.log(`    File: ${suggestion.filePath}`)
				console.log(
					`    Range: ${suggestion.range.start.line}:${suggestion.range.start.character} - ${suggestion.range.end.line}:${suggestion.range.end.character}`,
				)
			})

			const analysisTime = Date.now() - startTime
			const confidence = this.calculateOverallConfidence(filteredChanges)

			console.log(`🔍 QaxChangeDetector: Analysis completed in ${analysisTime}ms with confidence ${confidence}`)

			return {
				detectedChanges: filteredChanges,
				jumpSuggestions,
				analysisTime,
				confidence,
				metadata: {
					lspAvailable,
					astParsed,
					symbolsFound: context.symbols?.length || 0,
					referencesFound: filteredChanges.reduce((sum, change) => sum + (change.metadata?.references?.length || 0), 0),
					originalChangesCount: detectedChanges.length,
					filteredChangesCount: filteredChanges.length,
				},
			}
		} catch (error) {
			console.error("🔍 QaxChangeDetector: Analysis failed:", error)
			return {
				detectedChanges: [],
				jumpSuggestions: [],
				analysisTime: Date.now() - startTime,
				confidence: 0,
				metadata: {
					lspAvailable: false,
					astParsed: false,
					symbolsFound: 0,
					referencesFound: 0,
					error: error instanceof Error ? error.message : "Unknown error",
				},
			}
		}
	}

	/**
	 * 基于符号级别的快速检测
	 * 重新设计为以符号为基本单元进行检测
	 */
	private async detectTextChanges(context: QaxAnalysisContext): Promise<QaxChangeDetection[]> {
		const changes: QaxChangeDetection[] = []

		console.log(`🔍 QaxChangeDetector: Starting symbol-based change detection...`)

		try {
			// 创建临时文档用于符号检测
			const beforeDocument = await vscode.workspace.openTextDocument({
				content: context.beforeContent,
				language: context.languageId,
			})

			// 使用符号检测器比较两个文档
			const symbolChanges = this.symbolDetector.compareSymbols(beforeDocument, context.document)
			console.log(`🔍 QaxChangeDetector: Found ${symbolChanges.length} symbol changes`)

			// 将符号变化转换为变更检测结果
			for (const [index, symbolChange] of symbolChanges.entries()) {
				console.log(`🔍 QaxChangeDetector: Processing symbol change ${index + 1}:`)
				console.log(`  Symbol type: ${symbolChange.symbolType}`)
				console.log(`  Range: ${symbolChange.range.start.line}:${symbolChange.range.start.character} - ${symbolChange.range.end.line}:${symbolChange.range.end.character}`)
				console.log(`  Old text: "${symbolChange.oldText}"`)
				console.log(`  New text: "${symbolChange.newText}"`)
				console.log(`  Symbol name: ${symbolChange.symbolName || 'N/A'}`)
				console.log(`  Context: ${JSON.stringify(symbolChange.context || {})}`)

				// 跳过没有实际变化的符号
				if (symbolChange.oldText === symbolChange.newText) {
					console.log(`  ❌ Skipping: no actual change`)
					continue
				}

				// 根据符号类型确定变更类型
				const changeType = this.determineChangeType(symbolChange)
				if (!changeType) {
					console.log(`  ❌ Skipping: unsupported symbol type '${symbolChange.symbolType}' - no corresponding change type`)
					continue
				}

				console.log(`  ✅ Detected change type: ${changeType}`)
				console.log(`  ✅ Change: "${symbolChange.oldText}" → "${symbolChange.newText}"`)

				// 计算置信度
				const confidence = this.calculateSymbolChangeConfidence(symbolChange)
				console.log(`  ✅ Confidence: ${confidence.toFixed(4)}`)

				changes.push({
					type: changeType,
					filePath: context.filePath,
					range: symbolChange.range,
					oldValue: symbolChange.oldText,
					newValue: symbolChange.newText,
					confidence,
					metadata: {
						detectionMethod: "symbol_based",
						symbolName: symbolChange.symbolName,
						symbolType: symbolChange.symbolType,
						context: symbolChange.context,
					},
				})
			}

		} catch (error) {
			console.warn(`🔍 QaxChangeDetector: Symbol-based detection failed, falling back to text diff:`, error)

			// 如果符号检测失败，回退到基本的文本差异检测
			return this.detectTextChangesLegacy(context)
		}

		console.log(`🔍 QaxChangeDetector: Symbol-based detection completed with ${changes.length} changes`)
		return changes
	}

	/**
	 * 传统的基于文本差异的检测（作为备用方案）
	 */
	private async detectTextChangesLegacy(context: QaxAnalysisContext): Promise<QaxChangeDetection[]> {
		const changes: QaxChangeDetection[] = []

		console.log(`🔍 QaxChangeDetector: Analyzing ${context.changes.length} text changes...`)

		// 使用更简单直接的方法：基于字符串差异分析
		const diff = this.calculateTextDiff(context.beforeContent, context.afterContent)
		console.log(`🔍 QaxChangeDetector: Calculated diff:`, diff)

		// 分析每个差异
		for (const [index, diffItem] of diff.entries()) {
			console.log(`🔍 QaxChangeDetector: Processing diff ${index + 1}:`)
			console.log(`  Type: ${diffItem.type}`)
			console.log(`  Old value: "${diffItem.oldValue}"`)
			console.log(`  New value: "${diffItem.newValue}"`)
			console.log(`  Position: line ${diffItem.line}, char ${diffItem.character}`)

			// 早期过滤：如果没有实际变化，跳过
			if (diffItem.type === 'no-change' || diffItem.oldValue === diffItem.newValue) {
				console.log(`  ❌ Skipping: no actual change`)
				continue
			}

			// 对于单字符替换，尝试扩展到完整的标识符
			if (diffItem.type === 'replace' && diffItem.oldValue.length === 1 && diffItem.newValue.length === 1) {
				const expandedDiff = this.expandToFullIdentifier(diffItem, context.beforeContent, context.afterContent)
				if (expandedDiff) {
					console.log(`  🔍 Expanded to full identifier: "${expandedDiff.oldValue}" → "${expandedDiff.newValue}"`)
					diffItem.oldValue = expandedDiff.oldValue
					diffItem.newValue = expandedDiff.newValue
					diffItem.character = expandedDiff.character
				}
			}

			// 检测变量重命名模式
			const isVariableRename = this.isVariableRenamePattern(diffItem.newValue, diffItem.oldValue.length)
			console.log(`  Is variable rename pattern: ${isVariableRename}`)

			if (isVariableRename && diffItem.type === 'replace') {
				console.log(`  ✅ Detected variable rename: "${diffItem.oldValue}" → "${diffItem.newValue}"`)

				// 创建范围对象
				const range = new vscode.Range(
					new vscode.Position(diffItem.line, diffItem.character),
					new vscode.Position(diffItem.line, diffItem.character + diffItem.oldValue.length)
				)

				changes.push({
					type: QaxChangeType.VARIABLE_RENAME,
					filePath: context.filePath,
					range,
					oldValue: diffItem.oldValue,
					newValue: diffItem.newValue,
					confidence: 0.7, // 基于改进的文本差异检测，置信度稍高
					metadata: {
						detectionMethod: "text_diff_v2",
						symbolName: diffItem.oldValue, // 添加符号名称用于后续引用查找
					},
				})
			}

			// 检测函数调用删除模式
			const isFunctionCallDeletion = this.isFunctionCallDeletionPattern("", diffItem.oldValue.length)
			console.log(`  Is function call deletion pattern: ${isFunctionCallDeletion}`)
			if (isFunctionCallDeletion && diffItem.type === 'delete') {
				console.log(`  ✅ Detected function call deletion: "${diffItem.oldValue}"`)

				// 创建范围对象
				const range = new vscode.Range(
					new vscode.Position(diffItem.line, diffItem.character),
					new vscode.Position(diffItem.line, diffItem.character + diffItem.oldValue.length)
				)

				changes.push({
					type: QaxChangeType.FUNCTION_CALL_DELETION,
					filePath: context.filePath,
					range,
					oldValue: diffItem.oldValue,
					confidence: 0.6,
					metadata: {
						detectionMethod: "text_diff_v2",
					},
				})
			}
		}

		console.log(`🔍 QaxChangeDetector: Text-based detection completed, found ${changes.length} changes`)
		return changes
	}

	/**
	 * 基于 LSP 的检测
	 */
	private async detectLSPChanges(context: QaxAnalysisContext): Promise<QaxChangeDetection[]> {
		const changes: QaxChangeDetection[] = []

		// 获取文档符号
		const symbols = await this.lspService.getDocumentSymbols(context.document)
		context.symbols = symbols

		// 分析每个变更位置
		for (const change of context.changes) {
			const position = change.range.start

			// 获取该位置的引用和定义
			const references = await this.lspService.getReferences(context.document, position)
			const definitions = await this.lspService.getDefinitions(context.document, position)

			if (references.length > 0 || definitions.length > 0) {
				// 检测符号重命名
				const renameDetection = await this.lspService.detectSymbolRename(
					context.document,
					position,
					change.range.end,
					context.beforeContent.substring(
						context.document.offsetAt(change.range.start),
						context.document.offsetAt(change.range.start) + change.rangeLength,
					),
					change.text,
				)

				if (renameDetection) {
					renameDetection.filePath = context.filePath
					renameDetection.metadata = {
						...renameDetection.metadata,
						detectionMethod: "lsp",
						references,
						definitions,
					}
					changes.push(renameDetection)
				}
			}
		}

		return changes
	}

	/**
	 * 基于 AST 的检测
	 */
	private async detectASTChanges(context: QaxAnalysisContext): Promise<QaxChangeDetection[]> {
		const changes: QaxChangeDetection[] = []

		try {
			// 直接使用内容进行 AST 解析，避免创建临时文档
			const afterAST = await this.astService.parseDocument(context.document)

			// 为 beforeContent 创建一个虚拟的 AST，不使用 vscode.workspace.openTextDocument
			const beforeAST = await this.astService.parseContentDirectly(
				context.beforeContent,
				context.filePath,
				context.languageId
			)

			if (!beforeAST || !afterAST) {
				return changes
			}

			// 检测变量重命名
			const variableRenames = this.astService.detectVariableRename(beforeAST, afterAST)
			variableRenames.forEach((change) => {
				change.filePath = context.filePath
				change.metadata = {
					...change.metadata,
					detectionMethod: "ast",
				}
			})
			changes.push(...variableRenames)

			// 检测函数参数变更
			const parameterChanges = this.astService.detectFunctionParameterChanges(beforeAST, afterAST)
			parameterChanges.forEach((change) => {
				change.filePath = context.filePath
				change.metadata = {
					...change.metadata,
					detectionMethod: "ast",
				}
			})
			changes.push(...parameterChanges)

			// 检测函数调用删除
			const callDeletions = this.astService.detectFunctionCallDeletions(beforeAST, afterAST)
			callDeletions.forEach((change) => {
				change.filePath = context.filePath
				change.metadata = {
					...change.metadata,
					detectionMethod: "ast",
				}
			})
			changes.push(...callDeletions)
		} catch (error) {
			console.warn("QaxChangeDetector: AST analysis failed:", error)
		}

		return changes
	}

	/**
	 * 合并和去重检测结果
	 * 优先使用符号级别的检测结果，过滤掉字符级别的冗余检测
	 */
	private mergeDetectedChanges(changes: QaxChangeDetection[]): QaxChangeDetection[] {
		const merged: QaxChangeDetection[] = []

		// 按检测方法质量和置信度排序
		const sorted = changes.sort((a, b) => {
			// 首先按检测方法质量排序
			const aMethodScore = this.getDetectionMethodScore(a)
			const bMethodScore = this.getDetectionMethodScore(b)

			if (aMethodScore !== bMethodScore) {
				return bMethodScore - aMethodScore // 高质量方法优先
			}

			// 相同质量的方法按置信度排序
			return b.confidence - a.confidence
		})

		console.log(`🔍 QaxChangeDetector: Sorting ${changes.length} changes by method quality and confidence`)
		sorted.forEach((change, index) => {
			const methodScore = this.getDetectionMethodScore(change)
			console.log(`  Change ${index + 1}: ${change.metadata?.detectionMethod || 'unknown'} (score: ${methodScore}, confidence: ${change.confidence}) - "${change.oldValue}" → "${change.newValue}"`)
		})

		// 使用位置重叠检测来过滤冗余的字符级别检测
		for (const change of sorted) {
			const shouldInclude = this.shouldIncludeChange(change, merged)

			if (shouldInclude) {
				merged.push(change)
				console.log(`  ✅ Including: ${change.metadata?.detectionMethod || 'unknown'} - "${change.oldValue}" → "${change.newValue}"`)
			} else {
				console.log(`  ❌ Filtering out: ${change.metadata?.detectionMethod || 'unknown'} - "${change.oldValue}" → "${change.newValue}" (redundant with higher quality detection)`)
			}
		}

		return merged
	}

	/**
	 * 获取检测方法的质量分数
	 */
	private getDetectionMethodScore(change: QaxChangeDetection): number {
		const method = change.metadata?.detectionMethod as string

		switch (method) {
			case 'symbol_based':
				return 100 // 最高优先级：符号级别检测
			case 'ast':
				return 90  // AST检测
			case 'text_diff_v2':
				return 80  // 改进的文本差异检测
			case 'lsp':
				return 70  // LSP检测（可能是字符级别）
			case 'text_diff':
				return 60  // 基础文本差异检测
			default:
				return 50  // 未知方法
		}
	}

	/**
	 * 判断是否应该包含某个变更检测结果
	 */
	private shouldIncludeChange(change: QaxChangeDetection, existingChanges: QaxChangeDetection[]): boolean {
		// 检查是否与已有的高质量检测结果重叠
		for (const existing of existingChanges) {
			if (this.areChangesOverlapping(change, existing)) {
				const changeScore = this.getDetectionMethodScore(change)
				const existingScore = this.getDetectionMethodScore(existing)

				// 如果当前变更的质量分数低于已存在的变更，则过滤掉
				if (changeScore < existingScore) {
					return false
				}

				// 如果质量分数相同，检查是否是字符级别的冗余检测
				if (changeScore === existingScore && this.isCharacterLevelRedundancy(change, existing)) {
					return false
				}
			}
		}

		return true
	}

	/**
	 * 检查两个变更是否重叠
	 */
	private areChangesOverlapping(change1: QaxChangeDetection, change2: QaxChangeDetection): boolean {
		// 检查范围是否重叠
		const range1 = change1.range
		const range2 = change2.range

		// 如果在同一行
		if (range1.start.line === range2.start.line) {
			const start1 = range1.start.character
			const end1 = range1.end.character
			const start2 = range2.start.character
			const end2 = range2.end.character

			// 检查是否有重叠或包含关系
			const hasOverlap = !(end1 <= start2 || end2 <= start1)
			const range1ContainsRange2 = start1 <= start2 && end1 >= end2
			const range2ContainsRange1 = start2 <= start1 && end2 >= end1

			return hasOverlap || range1ContainsRange2 || range2ContainsRange1
		}

		return false
	}

	/**
	 * 检查是否是字符级别的冗余检测
	 */
	private isCharacterLevelRedundancy(change1: QaxChangeDetection, change2: QaxChangeDetection): boolean {
		// 安全获取新值，如果为undefined则使用空字符串
		const newValue1 = change1.newValue || ''
		const newValue2 = change2.newValue || ''

		// 如果一个是单字符变更，另一个是多字符变更，且多字符变更包含单字符变更
		const isChange1SingleChar = change1.oldValue.length <= 1 && newValue1.length <= 1
		const isChange2SingleChar = change2.oldValue.length <= 1 && newValue2.length <= 1

		// 特殊情况：如果一个变更的oldValue为空且newValue是单字符，另一个是完整的符号变更
		// 这通常表示字符级别的插入 vs 符号级别的重命名
		if (isChange1SingleChar && !isChange2SingleChar) {
			// change1是单字符，change2是多字符
			if (change1.oldValue === '' && newValue1.length === 1) {
				// change1是单字符插入，检查是否是change2符号变更的一部分
				return newValue2.includes(newValue1) || change2.oldValue.includes(newValue1)
			}
			return change2.oldValue.includes(change1.oldValue) || newValue2.includes(newValue1)
		}

		if (!isChange1SingleChar && isChange2SingleChar) {
			// change2是单字符，change1是多字符
			if (change2.oldValue === '' && newValue2.length === 1) {
				// change2是单字符插入，检查是否是change1符号变更的一部分
				return newValue1.includes(newValue2) || change1.oldValue.includes(newValue2)
			}
			return change1.oldValue.includes(change2.oldValue) || newValue1.includes(newValue2)
		}

		return false
	}

	/**
	 * 生成跳转建议
	 */
	private async generateJumpSuggestions(changes: QaxChangeDetection[], context: QaxAnalysisContext): Promise<any[]> {
		return await this.jumpSuggestionEngine.generateJumpSuggestions(changes, context)
	}

	/**
	 * 计算整体置信度
	 */
	private calculateOverallConfidence(changes: QaxChangeDetection[]): number {
		if (changes.length === 0) return 0

		const totalConfidence = changes.reduce((sum, change) => sum + change.confidence, 0)
		return totalConfidence / changes.length
	}

	/**
	 * 检测是否为变量重命名模式
	 */
	private isVariableRenamePattern(changeText: string, rangeLength: number): boolean {
		console.log(`🔍 QaxChangeDetector: Checking variable rename pattern:`)
		console.log(`  Change text: "${changeText}"`)
		console.log(`  Range length: ${rangeLength}`)

		const hasContentReplaced = rangeLength > 0
		const hasNewContent = changeText.length > 0
		const isValidIdentifier = /^[a-zA-Z_$][a-zA-Z0-9_$]*$/.test(changeText)
		const isNotAllLowercase = changeText !== changeText.toLowerCase()

		// 添加更严格的过滤条件
		const isMinimumLength = changeText.length >= 2 // 至少2个字符
		const isSignificantChange = rangeLength >= 2 || changeText.length >= 2 // 变更要有意义
		const isNotSingleChar = !(changeText.length === 1 && rangeLength === 1) // 不是单字符替换

		console.log(`  Has content replaced: ${hasContentReplaced}`)
		console.log(`  Has new content: ${hasNewContent}`)
		console.log(`  Is valid identifier: ${isValidIdentifier}`)
		console.log(`  Is not all lowercase: ${isNotAllLowercase}`)
		console.log(`  Is minimum length: ${isMinimumLength}`)
		console.log(`  Is significant change: ${isSignificantChange}`)
		console.log(`  Is not single char: ${isNotSingleChar}`)

		// 更严格的启发式规则
		const result = hasContentReplaced && hasNewContent && isValidIdentifier &&
					   isNotAllLowercase && isMinimumLength && isSignificantChange && isNotSingleChar
		console.log(`  Variable rename pattern result: ${result}`)
		return result
	}

	/**
	 * 检测是否为函数调用删除模式
	 */
	private isFunctionCallDeletionPattern(changeText: string, rangeLength: number): boolean {
		console.log(`🔍 QaxChangeDetector: Checking function call deletion pattern:`)
		console.log(`  Change text: "${changeText}"`)
		console.log(`  Range length: ${rangeLength}`)

		const hasContentDeleted = rangeLength > 0
		const hasNoNewContent = changeText === ""

		// 添加更严格的过滤条件
		const isSignificantDeletion = rangeLength >= 2 // 至少删除2个字符
		const isNotSingleCharDeletion = rangeLength !== 1 // 不是单字符删除

		console.log(`  Has content deleted: ${hasContentDeleted}`)
		console.log(`  Has no new content: ${hasNoNewContent}`)
		console.log(`  Is significant deletion: ${isSignificantDeletion}`)
		console.log(`  Is not single char deletion: ${isNotSingleCharDeletion}`)

		// 更严格的启发式规则
		const result = hasContentDeleted && hasNoNewContent && isSignificantDeletion && isNotSingleCharDeletion
		console.log(`  Function call deletion pattern result: ${result}`)
		return result
	}

	/**
	 * 检查是否为重复的微小变更
	 */
	private isRepeatedMinorChange(context: QaxAnalysisContext): boolean {
		const filePath = context.filePath
		const currentTime = Date.now()

		// 清理过期的变更历史
		for (const [path, history] of this.recentChanges.entries()) {
			if (currentTime - history.timestamp > this.CHANGE_HISTORY_TIMEOUT) {
				this.recentChanges.delete(path)
			}
		}

		// 获取当前变更的特征
		const changeSignatures = context.changes.map(change =>
			`${change.text}:${change.rangeLength}:${change.range.start.line}:${change.range.start.character}`
		)

		// 检查是否有最近的变更历史
		const recentHistory = this.recentChanges.get(filePath)
		if (recentHistory) {
			// 检查是否为相似的微小变更
			const hasMinorChanges = changeSignatures.every(sig => {
				const [text, rangeLength] = sig.split(':')
				return text.length <= 1 && parseInt(rangeLength) <= 1
			})

			if (hasMinorChanges && recentHistory.changes.some(prevSig =>
				changeSignatures.some(currSig => this.areSimilarChanges(prevSig, currSig))
			)) {
				return true
			}
		}

		// 更新变更历史
		this.recentChanges.set(filePath, {
			timestamp: currentTime,
			changes: changeSignatures
		})

		return false
	}

	/**
	 * 检查两个变更是否相似
	 */
	private areSimilarChanges(sig1: string, sig2: string): boolean {
		const [text1, rangeLength1, line1, char1] = sig1.split(':')
		const [text2, rangeLength2, line2, char2] = sig2.split(':')

		// 相同位置的单字符变更被认为是相似的
		return line1 === line2 &&
			   Math.abs(parseInt(char1) - parseInt(char2)) <= 2 &&
			   text1.length <= 1 && text2.length <= 1
	}

	/**
	 * 计算文本差异
	 */
	private calculateTextDiff(beforeContent: string, afterContent: string): TextDiffItem[] {
		const diffs: TextDiffItem[] = []

		// 简单的逐行比较方法
		const beforeLines = beforeContent.split('\n')
		const afterLines = afterContent.split('\n')

		const maxLines = Math.max(beforeLines.length, afterLines.length)

		for (let i = 0; i < maxLines; i++) {
			const beforeLine = beforeLines[i] || ''
			const afterLine = afterLines[i] || ''

			if (beforeLine !== afterLine) {
				// 找到行内的具体差异
				const lineDiffs = this.calculateLineDiff(beforeLine, afterLine, i)
				diffs.push(...lineDiffs)
			}
		}

		return diffs
	}

	/**
	 * 计算单行内的差异
	 */
	private calculateLineDiff(beforeLine: string, afterLine: string, lineNumber: number): TextDiffItem[] {
		const diffs: TextDiffItem[] = []

		// 使用简单的最长公共子序列算法找到差异
		const commonParts = this.findCommonParts(beforeLine, afterLine)

		let beforePos = 0
		let afterPos = 0

		for (const common of commonParts) {
			// 处理公共部分之前的差异
			if (beforePos < common.beforeStart || afterPos < common.afterStart) {
				const oldValue = beforeLine.substring(beforePos, common.beforeStart)
				const newValue = afterLine.substring(afterPos, common.afterStart)

				if (oldValue || newValue) {
					diffs.push({
						type: oldValue && newValue ? 'replace' : oldValue ? 'delete' : 'insert',
						oldValue,
						newValue,
						line: lineNumber,
						character: beforePos,
					})
				}
			}

			beforePos = common.beforeEnd
			afterPos = common.afterEnd
		}

		// 处理末尾的差异
		if (beforePos < beforeLine.length || afterPos < afterLine.length) {
			const oldValue = beforeLine.substring(beforePos)
			const newValue = afterLine.substring(afterPos)

			if (oldValue || newValue) {
				diffs.push({
					type: oldValue && newValue ? 'replace' : oldValue ? 'delete' : 'insert',
					oldValue,
					newValue,
					line: lineNumber,
					character: beforePos,
				})
			}
		}

		return diffs
	}

	/**
	 * 找到两个字符串的公共部分
	 */
	private findCommonParts(str1: string, str2: string): Array<{beforeStart: number, beforeEnd: number, afterStart: number, afterEnd: number}> {
		const common: Array<{beforeStart: number, beforeEnd: number, afterStart: number, afterEnd: number}> = []

		// 使用更简单但更准确的方法：逐字符比较找到公共前缀和后缀
		let prefixLength = 0
		while (prefixLength < str1.length && prefixLength < str2.length &&
			   str1[prefixLength] === str2[prefixLength]) {
			prefixLength++
		}

		// 从末尾开始比较，找到公共后缀
		let suffixLength = 0
		const maxSuffixLength = Math.min(str1.length - prefixLength, str2.length - prefixLength)
		while (suffixLength < maxSuffixLength &&
			   str1[str1.length - 1 - suffixLength] === str2[str2.length - 1 - suffixLength]) {
			suffixLength++
		}

		// 添加公共前缀
		if (prefixLength > 0) {
			common.push({
				beforeStart: 0,
				beforeEnd: prefixLength,
				afterStart: 0,
				afterEnd: prefixLength
			})
		}

		// 添加公共后缀（只有在不与前缀重叠时）
		if (suffixLength > 0 && prefixLength + suffixLength <= Math.min(str1.length, str2.length)) {
			common.push({
				beforeStart: str1.length - suffixLength,
				beforeEnd: str1.length,
				afterStart: str2.length - suffixLength,
				afterEnd: str2.length
			})
		}

		return common
	}

	/**
	 * 扩展单字符差异到完整的标识符
	 */
	private expandToFullIdentifier(diffItem: TextDiffItem, beforeContent: string, afterContent: string): {oldValue: string, newValue: string, character: number} | null {
		const beforeLines = beforeContent.split('\n')
		const afterLines = afterContent.split('\n')

		if (diffItem.line >= beforeLines.length || diffItem.line >= afterLines.length) {
			return null
		}

		const beforeLine = beforeLines[diffItem.line]
		const afterLine = afterLines[diffItem.line]

		// 找到标识符的边界
		const beforeStart = this.findIdentifierStart(beforeLine, diffItem.character)
		const beforeEnd = this.findIdentifierEnd(beforeLine, diffItem.character)
		const afterStart = this.findIdentifierStart(afterLine, diffItem.character)
		const afterEnd = this.findIdentifierEnd(afterLine, diffItem.character)

		// 检查是否找到了有效的标识符
		if (beforeStart === -1 || beforeEnd === -1 || afterStart === -1 || afterEnd === -1) {
			return null
		}

		const oldValue = beforeLine.substring(beforeStart, beforeEnd + 1)
		const newValue = afterLine.substring(afterStart, afterEnd + 1)

		// 确保这是一个有意义的标识符变更
		if (oldValue.length < 2 || newValue.length < 2 || oldValue === newValue) {
			return null
		}

		// 检查是否是有效的标识符
		if (!/^[a-zA-Z_$][a-zA-Z0-9_$]*$/.test(oldValue) || !/^[a-zA-Z_$][a-zA-Z0-9_$]*$/.test(newValue)) {
			return null
		}

		return {
			oldValue,
			newValue,
			character: beforeStart
		}
	}

	/**
	 * 找到标识符的开始位置
	 */
	private findIdentifierStart(line: string, position: number): number {
		let start = position
		while (start > 0 && /[a-zA-Z0-9_$]/.test(line[start - 1])) {
			start--
		}
		// 确保开始字符是有效的标识符开始字符
		if (start < line.length && /[a-zA-Z_$]/.test(line[start])) {
			return start
		}
		return -1
	}

	/**
	 * 找到标识符的结束位置
	 */
	private findIdentifierEnd(line: string, position: number): number {
		let end = position
		while (end < line.length && /[a-zA-Z0-9_$]/.test(line[end])) {
			end++
		}
		return end > position ? end - 1 : -1
	}

	/**
	 * 根据符号变化确定变更类型
	 */
	private determineChangeType(symbolChange: QaxSymbolChange): QaxChangeType | null {
		const hasOldText = symbolChange.oldText && symbolChange.oldText.length > 0
		const hasNewText = symbolChange.newText && symbolChange.newText.length > 0

		switch (symbolChange.symbolType) {
			case 'identifier':
				if (hasOldText && hasNewText) {
					return QaxChangeType.VARIABLE_RENAME
				} else if (hasOldText && !hasNewText) {
					return QaxChangeType.VARIABLE_DELETION
				} else if (!hasOldText && hasNewText) {
					return QaxChangeType.VARIABLE_ADDITION
				}
				break

			case 'function_call':
				if (hasOldText && hasNewText) {
					return QaxChangeType.FUNCTION_CALL_RENAME
				} else if (hasOldText && !hasNewText) {
					return QaxChangeType.FUNCTION_CALL_DELETION
				} else if (!hasOldText && hasNewText) {
					return QaxChangeType.FUNCTION_CALL_ADDITION
				}
				break

			case 'string_literal':
				if (hasOldText && hasNewText) {
					return QaxChangeType.STRING_LITERAL_CHANGE
				}
				break

			case 'number_literal':
				if (hasOldText && hasNewText) {
					return QaxChangeType.NUMBER_LITERAL_CHANGE
				}
				break

			case 'statement':
			case 'expression':
				// 对于语句和表达式，尝试推断具体类型
				if (hasOldText && hasNewText) {
					// 简单的启发式判断
					if (this.looksLikeIdentifier(symbolChange.oldText) && this.looksLikeIdentifier(symbolChange.newText)) {
						return QaxChangeType.VARIABLE_RENAME
					}
					return QaxChangeType.UNKNOWN_CHANGE
				}
				break

			default:
				console.log(`🔍 QaxChangeDetector: Unknown symbol type: ${symbolChange.symbolType}`)
				return QaxChangeType.UNKNOWN_CHANGE
		}

		return null
	}

	/**
	 * 判断文本是否看起来像标识符
	 */
	private looksLikeIdentifier(text: string): boolean {
		return /^[a-zA-Z_$][a-zA-Z0-9_$]*$/.test(text)
	}

	/**
	 * 计算符号变化的置信度
	 */
	private calculateSymbolChangeConfidence(symbolChange: QaxSymbolChange): number {
		let confidence = 0.8 // 符号级别检测的基础置信度较高

		// 根据符号类型调整置信度
		switch (symbolChange.symbolType) {
			case 'identifier':
				confidence = 0.9
				break
			case 'function_call':
				confidence = 0.85
				break
			case 'string_literal':
			case 'number_literal':
				confidence = 0.6
				break
		}

		// 根据变化的复杂度调整置信度
		if (symbolChange.oldText && symbolChange.newText) {
			const similarity = this.calculateStringSimilarity(symbolChange.oldText, symbolChange.newText)
			confidence *= (0.5 + similarity * 0.5) // 相似度越高，置信度越高
		}

		return Math.min(confidence, 1.0)
	}

	/**
	 * 计算字符串相似度
	 */
	private calculateStringSimilarity(str1: string, str2: string): number {
		if (str1 === str2) return 1.0
		if (str1.length === 0 || str2.length === 0) return 0.0

		// 使用简单的编辑距离算法
		const matrix: number[][] = []
		const len1 = str1.length
		const len2 = str2.length

		for (let i = 0; i <= len1; i++) {
			matrix[i] = [i]
		}

		for (let j = 0; j <= len2; j++) {
			matrix[0][j] = j
		}

		for (let i = 1; i <= len1; i++) {
			for (let j = 1; j <= len2; j++) {
				if (str1[i - 1] === str2[j - 1]) {
					matrix[i][j] = matrix[i - 1][j - 1]
				} else {
					matrix[i][j] = Math.min(
						matrix[i - 1][j] + 1,    // deletion
						matrix[i][j - 1] + 1,    // insertion
						matrix[i - 1][j - 1] + 1 // substitution
					)
				}
			}
		}

		const maxLen = Math.max(len1, len2)
		return (maxLen - matrix[len1][len2]) / maxLen
	}

	/**
	 * 更新配置
	 */
	updateConfig(config: QaxNextEditConfig): void {
		this.config = config
		this.jumpSuggestionEngine.updateConfig(config)
	}
}
