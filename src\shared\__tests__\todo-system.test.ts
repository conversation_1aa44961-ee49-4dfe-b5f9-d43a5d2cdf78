import { describe, it, expect, beforeEach } from "vitest"
import {
	TodoItem,
	TodoStatus,
	parseMarkdownChecklist,
	todoListToMarkdown,
	validateTodos,
	normalizeStatus,
	addTodoToTask,
	updateTodoStatusForTask,
	removeTodoFromTask,
	getTodoListForTask,
	setTodoListForTask,
} from "../../core/tools/updateTodoListTool"
import { getLatestTodo } from "../todo"
import { formatReminderSection } from "../../core/environment/reminder"

describe("Todo System Tests", () => {
	describe("Markdown Parsing", () => {
		it("should parse markdown checklist correctly", () => {
			const markdown = `
[ ] Task 1
[x] Task 2 completed
[-] Task 3 in progress
[~] Task 4 also in progress
			`.trim()

			const todos = parseMarkdownChecklist(markdown)
			expect(todos).toHaveLength(4)
			expect(todos[0].content).toBe("Task 1")
			expect(todos[0].status).toBe("pending")
			expect(todos[1].content).toBe("Task 2 completed")
			expect(todos[1].status).toBe("completed")
			expect(todos[2].content).toBe("Task 3 in progress")
			expect(todos[2].status).toBe("in_progress")
			expect(todos[3].content).toBe("Task 4 also in progress")
			expect(todos[3].status).toBe("in_progress")
		})

		it("should handle empty markdown", () => {
			const todos = parseMarkdownChecklist("")
			expect(todos).toHaveLength(0)
		})

		it("should ignore invalid lines", () => {
			const markdown = `
- [ ] Valid task
This is not a checklist item
- [x] Another valid task
			`.trim()

			const todos = parseMarkdownChecklist(markdown)
			expect(todos).toHaveLength(2)
		})
	})

	describe("Markdown Generation", () => {
		it("should convert todos to markdown correctly", () => {
			const todos: TodoItem[] = [
				{ id: "1", content: "Task 1", status: "pending" },
				{ id: "2", content: "Task 2", status: "completed" },
				{ id: "3", content: "Task 3", status: "in_progress" },
			]

			const markdown = todoListToMarkdown(todos)
			const expected = `[ ] Task 1
[x] Task 2
[-] Task 3`
			expect(markdown).toBe(expected)
		})

		it("should handle empty todo list", () => {
			const markdown = todoListToMarkdown([])
			expect(markdown).toBe("")
		})
	})

	describe("Todo Validation", () => {
		it("should validate correct todos", () => {
			const todos: TodoItem[] = [
				{ id: "1", content: "Task 1", status: "pending" },
				{ id: "2", content: "Task 2", status: "completed" },
			]

			const result = validateTodos(todos)
			expect(result.valid).toBe(true)
		})

		it("should reject invalid todos", () => {
			const invalidTodos = [{ id: "1", content: "Task 1", status: "invalid_status" as TodoStatus }]

			const result = validateTodos(invalidTodos)
			expect(result.valid).toBe(false)
			expect(result.error).toContain("Invalid todo status")
		})

		it("should reject todos missing required fields", () => {
			const invalidTodos = [{ content: "Task 1", status: "pending" }] as TodoItem[]

			const result = validateTodos(invalidTodos)
			expect(result.valid).toBe(false)
			expect(result.error).toContain("must have id, content, and status")
		})
	})

	describe("Status Normalization", () => {
		it("should normalize valid statuses", () => {
			expect(normalizeStatus("completed")).toBe("completed")
			expect(normalizeStatus("in_progress")).toBe("in_progress")
			expect(normalizeStatus("pending")).toBe("pending")
		})

		it("should default invalid statuses to pending", () => {
			expect(normalizeStatus("invalid")).toBe("pending")
			expect(normalizeStatus("")).toBe("pending")
		})
	})

	describe("Task Integration", () => {
		let mockTask: any

		beforeEach(() => {
			mockTask = { todoList: [] }
		})

		it("should add todo to task", () => {
			const todo = addTodoToTask(mockTask, "New task", "pending", "test-id")
			expect(mockTask.todoList).toHaveLength(1)
			expect(todo.content).toBe("New task")
			expect(todo.status).toBe("pending")
			expect(todo.id).toBe("test-id")
		})

		it("should update todo status", () => {
			addTodoToTask(mockTask, "Test task", "pending", "test-id")
			const updated = updateTodoStatusForTask(mockTask, "test-id", "in_progress")
			expect(updated).toBe(true)
			expect(mockTask.todoList[0].status).toBe("in_progress")
		})

		it("should remove todo from task", () => {
			addTodoToTask(mockTask, "Test task", "pending", "test-id")
			const removed = removeTodoFromTask(mockTask, "test-id")
			expect(removed).toBe(true)
			expect(mockTask.todoList).toHaveLength(0)
		})

		it("should get todo list copy", () => {
			addTodoToTask(mockTask, "Test task", "pending", "test-id")
			const todos = getTodoListForTask(mockTask)
			expect(todos).toHaveLength(1)
			expect(todos).not.toBe(mockTask.todoList) // Should be a copy
		})

		it("should set todo list for task", async () => {
			const todos: TodoItem[] = [{ id: "1", content: "Task 1", status: "pending" }]
			await setTodoListForTask(mockTask, todos)
			expect(mockTask.todoList).toEqual(todos)
		})
	})

	describe("Message Extraction", () => {
		it("should extract latest todo from messages", () => {
			const messages = [
				{
					type: "ask",
					ask: "tool",
					text: JSON.stringify({
						tool: "updateTodoList",
						todos: [{ id: "1", content: "Task 1", status: "pending" }],
					}),
				},
				{
					type: "ask",
					ask: "tool",
					text: JSON.stringify({
						tool: "updateTodoList",
						todos: [
							{ id: "1", content: "Task 1", status: "completed" },
							{ id: "2", content: "Task 2", status: "pending" },
						],
					}),
				},
			] as any[]

			const todos = getLatestTodo(messages)
			expect(todos).toHaveLength(2)
			expect(todos[0].status).toBe("completed")
			expect(todos[1].content).toBe("Task 2")
		})

		it("should return empty array when no todos found", () => {
			const messages = [
				{
					type: "say",
					say: "text",
					text: "Regular message",
				},
			] as any[]

			const todos = getLatestTodo(messages)
			expect(todos).toEqual([])
		})
	})

	describe("Reminder Formatting", () => {
		it("should format reminder section with todos", () => {
			const todos: TodoItem[] = [
				{ id: "1", content: "Task 1", status: "pending" },
				{ id: "2", content: "Task 2", status: "completed" },
				{ id: "3", content: "Task 3", status: "in_progress" },
			]

			const reminder = formatReminderSection(todos)
			expect(reminder).toContain("REMINDERS")
			expect(reminder).toContain("Task 1")
			expect(reminder).toContain("Task 2")
			expect(reminder).toContain("Task 3")
			expect(reminder).toContain("Pending")
			expect(reminder).toContain("Completed")
			expect(reminder).toContain("In Progress")
		})

		it("should show message when no todos exist", () => {
			const reminder = formatReminderSection([])
			expect(reminder).toContain("You have not created a todo list yet")
			expect(reminder).toContain("update_todo_list")
		})

		it("should handle undefined todos", () => {
			const reminder = formatReminderSection(undefined)
			expect(reminder).toContain("You have not created a todo list yet")
		})
	})

	describe("Round-trip Conversion", () => {
		it("should maintain data integrity through markdown conversion", () => {
			const originalTodos: TodoItem[] = [
				{ id: "1", content: "Task 1", status: "pending" },
				{ id: "2", content: "Task 2 with | pipe", status: "completed" },
				{ id: "3", content: "Task 3", status: "in_progress" },
			]

			const markdown = todoListToMarkdown(originalTodos)
			const parsedTodos = parseMarkdownChecklist(markdown)

			expect(parsedTodos).toHaveLength(originalTodos.length)
			parsedTodos.forEach((todo, index) => {
				expect(todo.content).toBe(originalTodos[index].content)
				expect(todo.status).toBe(originalTodos[index].status)
			})
		})
	})
})
