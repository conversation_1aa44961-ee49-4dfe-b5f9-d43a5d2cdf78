import * as vscode from 'vscode';
import { QaxJumpSuggestionEngine } from '../QaxJumpSuggestionEngine';
import { QaxLSPService } from '../QaxLSPService';
import {
    QaxChangeDetection,
    QaxJumpSuggestion,
    QaxChangeType,
    QaxAnalysisContext,
    QaxNextEditConfig,
    DEFAULT_QAX_NEXT_EDIT_CONFIG,
} from '../../types/QaxNextEditTypes';

// Mock dependencies
jest.mock('../QaxLSPService');

describe('QaxJumpSuggestionEngine', () => {
    let engine: QaxJumpSuggestionEngine;
    let mockConfig: QaxNextEditConfig;
    let mockLSPService: jest.Mocked<QaxLSPService>;
    let mockDocument: vscode.TextDocument;
    let mockContext: QaxAnalysisContext;

    beforeEach(() => {
        jest.clearAllMocks();

        // Create mock config
        mockConfig = {
            ...DEFAULT_QAX_NEXT_EDIT_CONFIG,
            enableLSPIntegration: true,
            maxSuggestions: 10,
            confidenceThreshold: 0.4,
        };

        // Setup mock LSP service
        mockLSPService = {
            getReferences: jest.fn(),
            getDefinitions: jest.fn(),
            getReferencesFromOriginalContent: jest.fn(),
            getDefinitionsFromOriginalContent: jest.fn(),
            isLSPAvailable: jest.fn(),
        } as any;

        (QaxLSPService.getInstance as jest.Mock).mockReturnValue(mockLSPService);

        // Create mock document
        mockDocument = {
            uri: vscode.Uri.file('/test/file.js'),
            fileName: '/test/file.js',
            languageId: 'javascript',
            version: 1,
            getText: jest.fn().mockReturnValue('const test = 1;'),
            lineAt: jest.fn(),
            lineCount: 10,
        } as any;

        // Create mock context
        mockContext = {
            filePath: '/test/file.js',
            document: mockDocument,
            changes: [],
            beforeContent: 'const oldVar = 1;\nconsole.log(oldVar);',
            afterContent: 'const newVar = 1;\nconsole.log(newVar);',
            languageId: 'javascript',
        };

        // Create engine instance
        engine = new QaxJumpSuggestionEngine(mockConfig);
    });

    describe('Constructor and Initialization', () => {
        test('should initialize with correct config and LSP service', () => {
            expect(QaxLSPService.getInstance).toHaveBeenCalled();
            expect((engine as any).config).toEqual(mockConfig);
        });
    });

    describe('generateJumpSuggestions', () => {
        test('should generate suggestions for variable rename', async () => {
            const changes: QaxChangeDetection[] = [
                {
                    type: QaxChangeType.VARIABLE_RENAME,
                    filePath: '/test/file.js',
                    range: new vscode.Range(0, 6, 0, 12),
                    oldValue: 'oldVar',
                    newValue: 'newVar',
                    confidence: 0.8,
                    metadata: {},
                }
            ];

            const mockReferences = [
                new vscode.Location(vscode.Uri.file('/test/file.js'), new vscode.Range(1, 12, 1, 18))
            ];

            mockLSPService.getReferencesFromOriginalContent.mockResolvedValue(mockReferences);

            const result = await engine.generateJumpSuggestions(changes, mockContext);

            expect(result.length).toBeGreaterThan(0);
            expect(result[0].type).toBe(QaxChangeType.VARIABLE_RENAME);
            expect(result[0].targetLocation).toBeDefined();
        });

        test('should generate suggestions for function rename', async () => {
            const changes: QaxChangeDetection[] = [
                {
                    type: QaxChangeType.FUNCTION_RENAME,
                    filePath: '/test/file.js',
                    range: new vscode.Range(0, 9, 0, 16),
                    oldValue: 'oldFunc',
                    newValue: 'newFunc',
                    confidence: 0.9,
                    metadata: {},
                }
            ];

            const mockReferences = [
                new vscode.Location(vscode.Uri.file('/test/file.js'), new vscode.Range(2, 0, 2, 7))
            ];

            mockLSPService.getReferencesFromOriginalContent.mockResolvedValue(mockReferences);

            const result = await engine.generateJumpSuggestions(changes, mockContext);

            expect(result.length).toBeGreaterThan(0);
            expect(result[0].type).toBe(QaxChangeType.FUNCTION_RENAME);
        });

        test('should generate suggestions for function call rename', async () => {
            const changes: QaxChangeDetection[] = [
                {
                    type: QaxChangeType.FUNCTION_CALL_RENAME,
                    filePath: '/test/file.js',
                    range: new vscode.Range(1, 0, 1, 7),
                    oldValue: 'oldCall',
                    newValue: 'newCall',
                    confidence: 0.7,
                    metadata: {},
                }
            ];

            const mockReferences = [
                new vscode.Location(vscode.Uri.file('/test/file.js'), new vscode.Range(3, 5, 3, 12))
            ];

            mockLSPService.getReferencesFromOriginalContent.mockResolvedValue(mockReferences);

            const result = await engine.generateJumpSuggestions(changes, mockContext);

            expect(result.length).toBeGreaterThan(0);
            expect(result[0].type).toBe(QaxChangeType.FUNCTION_CALL_RENAME);
        });

        test('should handle property rename', async () => {
            const changes: QaxChangeDetection[] = [
                {
                    type: QaxChangeType.PROPERTY_RENAME,
                    filePath: '/test/file.js',
                    range: new vscode.Range(0, 10, 0, 18),
                    oldValue: 'oldProp',
                    newValue: 'newProp',
                    confidence: 0.6,
                    metadata: {},
                }
            ];

            const result = await engine.generateJumpSuggestions(changes, mockContext);

            expect(result.length).toBeGreaterThanOrEqual(0);
        });

        test('should handle unknown change types', async () => {
            const changes: QaxChangeDetection[] = [
                {
                    type: QaxChangeType.UNKNOWN_CHANGE,
                    filePath: '/test/file.js',
                    range: new vscode.Range(0, 0, 0, 5),
                    oldValue: 'old',
                    newValue: 'new',
                    confidence: 0.5,
                    metadata: {},
                }
            ];

            const result = await engine.generateJumpSuggestions(changes, mockContext);

            expect(result).toEqual([]);
        });

        test('should handle errors gracefully', async () => {
            const changes: QaxChangeDetection[] = [
                {
                    type: QaxChangeType.VARIABLE_RENAME,
                    filePath: '/test/file.js',
                    range: new vscode.Range(0, 0, 0, 5),
                    oldValue: 'test',
                    newValue: 'newTest',
                    confidence: 0.8,
                    metadata: {},
                }
            ];

            mockLSPService.getReferencesFromOriginalContent.mockRejectedValue(new Error('LSP error'));

            const result = await engine.generateJumpSuggestions(changes, mockContext);

            expect(result).toEqual([]);
        });

        test('should limit suggestions by maxSuggestions config', async () => {
            const limitedConfig = { ...mockConfig, maxSuggestions: 2 };
            const limitedEngine = new QaxJumpSuggestionEngine(limitedConfig);

            const changes: QaxChangeDetection[] = [
                {
                    type: QaxChangeType.VARIABLE_RENAME,
                    filePath: '/test/file.js',
                    range: new vscode.Range(0, 0, 0, 5),
                    oldValue: 'test',
                    newValue: 'newTest',
                    confidence: 0.8,
                    metadata: {},
                }
            ];

            const mockReferences = Array.from({ length: 5 }, (_, i) => 
                new vscode.Location(vscode.Uri.file('/test/file.js'), new vscode.Range(i + 1, 0, i + 1, 4))
            );

            mockLSPService.getReferencesFromOriginalContent.mockResolvedValue(mockReferences);

            const result = await limitedEngine.generateJumpSuggestions(changes, mockContext);

            expect(result.length).toBeLessThanOrEqual(2);
        });
    });

    describe('generateVariableRenameSuggestions', () => {
        test('should generate suggestions for variable references', async () => {
            const change: QaxChangeDetection = {
                type: QaxChangeType.VARIABLE_RENAME,
                filePath: '/test/file.js',
                range: new vscode.Range(0, 6, 0, 12),
                oldValue: 'oldVar',
                newValue: 'newVar',
                confidence: 0.8,
                metadata: {},
            };

            const mockReferences = [
                new vscode.Location(vscode.Uri.file('/test/file.js'), new vscode.Range(1, 12, 1, 18)),
                new vscode.Location(vscode.Uri.file('/test/file.js'), new vscode.Range(2, 8, 2, 14)),
            ];

            mockLSPService.getReferencesFromOriginalContent.mockResolvedValue(mockReferences);

            const result = await (engine as any).generateVariableRenameSuggestions(change, mockContext);

            expect(result.length).toBe(2);
            expect(result[0].description).toContain('oldVar');
            expect(result[0].description).toContain('newVar');
        });

        test('should handle empty references', async () => {
            const change: QaxChangeDetection = {
                type: QaxChangeType.VARIABLE_RENAME,
                filePath: '/test/file.js',
                range: new vscode.Range(0, 6, 0, 12),
                oldValue: 'oldVar',
                newValue: 'newVar',
                confidence: 0.8,
                metadata: {},
            };

            mockLSPService.getReferencesFromOriginalContent.mockResolvedValue([]);

            const result = await (engine as any).generateVariableRenameSuggestions(change, mockContext);

            expect(result).toEqual([]);
        });
    });

    describe('generateFunctionRenameSuggestions', () => {
        test('should generate suggestions for function references', async () => {
            const change: QaxChangeDetection = {
                type: QaxChangeType.FUNCTION_RENAME,
                filePath: '/test/file.js',
                range: new vscode.Range(0, 9, 0, 16),
                oldValue: 'oldFunc',
                newValue: 'newFunc',
                confidence: 0.9,
                metadata: {},
            };

            const mockReferences = [
                new vscode.Location(vscode.Uri.file('/test/file.js'), new vscode.Range(3, 0, 3, 7)),
            ];

            mockLSPService.getReferencesFromOriginalContent.mockResolvedValue(mockReferences);

            const result = await (engine as any).generateFunctionRenameSuggestions(change, mockContext);

            expect(result.length).toBe(1);
            expect(result[0].type).toBe(QaxChangeType.FUNCTION_RENAME);
        });
    });

    describe('generateFunctionCallRenameSuggestions', () => {
        test('should generate suggestions for function call references', async () => {
            const change: QaxChangeDetection = {
                type: QaxChangeType.FUNCTION_CALL_RENAME,
                filePath: '/test/file.js',
                range: new vscode.Range(1, 0, 1, 7),
                oldValue: 'oldCall',
                newValue: 'newCall',
                confidence: 0.7,
                metadata: {},
            };

            const mockReferences = [
                new vscode.Location(vscode.Uri.file('/test/file.js'), new vscode.Range(4, 5, 4, 12)),
            ];

            mockLSPService.getReferencesFromOriginalContent.mockResolvedValue(mockReferences);

            const result = await (engine as any).generateFunctionCallRenameSuggestions(change, mockContext);

            expect(result.length).toBe(1);
            expect(result[0].type).toBe(QaxChangeType.FUNCTION_CALL_RENAME);
        });
    });

    describe('generatePropertyRenameSuggestions', () => {
        test('should generate suggestions for property access', async () => {
            const change: QaxChangeDetection = {
                type: QaxChangeType.PROPERTY_RENAME,
                filePath: '/test/file.js',
                range: new vscode.Range(0, 10, 0, 18),
                oldValue: 'oldProp',
                newValue: 'newProp',
                confidence: 0.6,
                metadata: {},
            };

            const result = await (engine as any).generatePropertyRenameSuggestions(change, mockContext);

            expect(Array.isArray(result)).toBe(true);
        });
    });

    describe('Helper Methods', () => {
        describe('createJumpSuggestion', () => {
            test('should create jump suggestion with correct properties', () => {
                const change: QaxChangeDetection = {
                    type: QaxChangeType.VARIABLE_RENAME,
                    filePath: '/test/file.js',
                    range: new vscode.Range(0, 0, 0, 5),
                    oldValue: 'old',
                    newValue: 'new',
                    confidence: 0.8,
                    metadata: {},
                };

                const location = new vscode.Location(
                    vscode.Uri.file('/test/file.js'),
                    new vscode.Range(1, 0, 1, 3)
                );

                const result = (engine as any).createJumpSuggestion(change, location, 'Test description');

                expect(result.type).toBe(QaxChangeType.VARIABLE_RENAME);
                expect(result.targetLocation).toBe(location);
                expect(result.description).toBe('Test description');
                expect(result.confidence).toBe(0.8);
                expect(result.suggestedEdit).toBeDefined();
            });
        });

        describe('calculateSuggestionConfidence', () => {
            test('should calculate confidence based on change confidence and context', () => {
                const change: QaxChangeDetection = {
                    type: QaxChangeType.VARIABLE_RENAME,
                    filePath: '/test/file.js',
                    range: new vscode.Range(0, 0, 0, 5),
                    oldValue: 'longVariableName',
                    newValue: 'anotherLongName',
                    confidence: 0.8,
                    metadata: {},
                };

                const confidence = (engine as any).calculateSuggestionConfidence(change, mockContext);

                expect(confidence).toBeGreaterThan(0);
                expect(confidence).toBeLessThanOrEqual(1);
            });

            test('should return lower confidence for short names', () => {
                const change: QaxChangeDetection = {
                    type: QaxChangeType.VARIABLE_RENAME,
                    filePath: '/test/file.js',
                    range: new vscode.Range(0, 0, 0, 1),
                    oldValue: 'a',
                    newValue: 'b',
                    confidence: 0.8,
                    metadata: {},
                };

                const confidence = (engine as any).calculateSuggestionConfidence(change, mockContext);

                expect(confidence).toBeLessThan(0.8);
            });
        });

        describe('formatSuggestionDescription', () => {
            test('should format description correctly', () => {
                const change: QaxChangeDetection = {
                    type: QaxChangeType.VARIABLE_RENAME,
                    filePath: '/test/file.js',
                    range: new vscode.Range(0, 0, 0, 5),
                    oldValue: 'oldVar',
                    newValue: 'newVar',
                    confidence: 0.8,
                    metadata: {},
                };

                const location = new vscode.Location(
                    vscode.Uri.file('/test/file.js'),
                    new vscode.Range(1, 0, 1, 5)
                );

                const description = (engine as any).formatSuggestionDescription(change, location);

                expect(description).toContain('oldVar');
                expect(description).toContain('newVar');
                expect(description).toContain('line 2');
            });
        });

        describe('isValidSuggestion', () => {
            test('should validate suggestion correctly', () => {
                const validSuggestion: QaxJumpSuggestion = {
                    type: QaxChangeType.VARIABLE_RENAME,
                    targetLocation: new vscode.Location(
                        vscode.Uri.file('/test/file.js'),
                        new vscode.Range(1, 0, 1, 5)
                    ),
                    description: 'Valid suggestion',
                    confidence: 0.8,
                    suggestedEdit: {
                        range: new vscode.Range(1, 0, 1, 5),
                        newText: 'newVar',
                    },
                    metadata: {},
                };

                const isValid = (engine as any).isValidSuggestion(validSuggestion);

                expect(isValid).toBe(true);
            });

            test('should reject invalid suggestions', () => {
                const invalidSuggestion: QaxJumpSuggestion = {
                    type: QaxChangeType.VARIABLE_RENAME,
                    targetLocation: new vscode.Location(
                        vscode.Uri.file('/test/file.js'),
                        new vscode.Range(1, 0, 1, 5)
                    ),
                    description: '',
                    confidence: 0.1, // Below threshold
                    suggestedEdit: {
                        range: new vscode.Range(1, 0, 1, 5),
                        newText: '',
                    },
                    metadata: {},
                };

                const isValid = (engine as any).isValidSuggestion(invalidSuggestion);

                expect(isValid).toBe(false);
            });
        });
    });

    describe('updateConfig', () => {
        test('should update configuration', () => {
            const newConfig: QaxNextEditConfig = {
                ...mockConfig,
                maxSuggestions: 20,
                confidenceThreshold: 0.6,
            };

            engine.updateConfig(newConfig);

            expect((engine as any).config).toEqual(newConfig);
        });
    });

    describe('Error Handling and Edge Cases', () => {
        test('should handle null/undefined inputs gracefully', async () => {
            const result = await engine.generateJumpSuggestions(null as any, null as any);
            
            expect(result).toEqual([]);
        });

        test('should handle empty changes array', async () => {
            const result = await engine.generateJumpSuggestions([], mockContext);
            
            expect(result).toEqual([]);
        });

        test('should handle LSP service unavailable', async () => {
            mockLSPService.getReferencesFromOriginalContent.mockRejectedValue(new Error('LSP unavailable'));

            const changes: QaxChangeDetection[] = [
                {
                    type: QaxChangeType.VARIABLE_RENAME,
                    filePath: '/test/file.js',
                    range: new vscode.Range(0, 0, 0, 5),
                    oldValue: 'test',
                    newValue: 'newTest',
                    confidence: 0.8,
                    metadata: {},
                }
            ];

            const result = await engine.generateJumpSuggestions(changes, mockContext);

            expect(result).toEqual([]);
        });

        test('should handle invalid file paths', async () => {
            const changes: QaxChangeDetection[] = [
                {
                    type: QaxChangeType.VARIABLE_RENAME,
                    filePath: '',
                    range: new vscode.Range(0, 0, 0, 5),
                    oldValue: 'test',
                    newValue: 'newTest',
                    confidence: 0.8,
                    metadata: {},
                }
            ];

            const result = await engine.generateJumpSuggestions(changes, mockContext);

            expect(result).toEqual([]);
        });
    });
});
