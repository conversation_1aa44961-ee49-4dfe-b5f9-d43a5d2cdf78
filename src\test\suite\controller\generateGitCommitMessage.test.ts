import * as assert from "assert"
import * as vscode from "vscode"
import * as sinon from "sinon"
import { Controller } from "../../../core/controller"
import * as gitUtils from "../../../utils/git"
import * as pathUtils from "../../../utils/path"
import { buildApiHandler } from "../../../api"
import * as commitMessageGenerator from "../../../integrations/git/commit-message-generator"

suite("Controller - generateGitCommitMessage", () => {
	let controller: Controller
	let context: vscode.ExtensionContext
	let outputChannel: vscode.OutputChannel
	let postMessageStub: sinon.SinonStub
	let sandbox: sinon.SinonSandbox

	setup(() => {
		sandbox = sinon.createSandbox()

		// Mock VSCode context
		context = {
			globalStorageUri: { fsPath: "/test/storage" },
			extension: { packageJSON: { version: "1.0.0" } },
		} as any

		// Mock output channel
		outputChannel = {
			appendLine: sandbox.stub(),
			dispose: sandbox.stub(),
		} as any

		// Mock post message function
		postMessageStub = sandbox.stub().resolves(true)

		// Create controller instance
		controller = new Controller(context, outputChannel, postMessageStub, "test-id")
	})

	teardown(() => {
		sandbox.restore()
	})

	suite("正常情况下的提交信息生成", () => {
		test("应该成功生成并显示提交信息选项", async () => {
			// Arrange
			const mockGitDiff = `diff --git a/test.js b/test.js
index 1234567..abcdefg 100644
--- a/test.js
+++ b/test.js
@@ -1,3 +1,4 @@
 function test() {
+  console.log('test');
   return true;
 }`

			const mockCommitMessage = "feat: add console logging to test function"
			const mockApiResponse = `\`\`\`
${mockCommitMessage}
\`\`\``

			// Mock dependencies
			sandbox.stub(pathUtils, "getCwd").resolves("/test/workspace")
			sandbox.stub(gitUtils, "getWorkingState").resolves(mockGitDiff)

			// Mock getAllExtensionState
			sandbox.stub(controller as any, "getAllExtensionState").resolves({
				apiConfiguration: {
					apiProvider: "anthropic",
					apiKey: "test-key",
				},
			})

			// Mock buildApiHandler
			const mockApiHandler = {
				createMessage: sandbox.stub().returns(
					(async function* () {
						yield { type: "text", text: mockApiResponse }
					})(),
				),
				getModel: sandbox.stub().returns({
					id: "test-model",
					info: { maxTokens: 4096 },
				}),
			}
			sandbox.stub({ buildApiHandler }, "buildApiHandler").returns(mockApiHandler)

			// Mock commit message generator functions
			const formatGitDiffPromptStub = sandbox
				.stub(commitMessageGenerator, "formatGitDiffPrompt")
				.returns("formatted prompt")
			const extractCommitMessageStub = sandbox
				.stub(commitMessageGenerator, "extractCommitMessage")
				.returns(mockCommitMessage)
			const showCommitMessageOptionsStub = sandbox.stub(commitMessageGenerator, "showCommitMessageOptions").resolves()

			// Mock vscode.window.withProgress
			const withProgressStub = sandbox.stub(vscode.window, "withProgress").callsFake(async (options, callback) => {
				return await callback(
					{ report: sandbox.stub() },
					{ isCancellationRequested: false, onCancellationRequested: sandbox.stub() },
				)
			})

			// Act
			await controller.generateGitCommitMessage()

			// Assert
			assert.ok(formatGitDiffPromptStub.calledOnce, "formatGitDiffPrompt should be called once")
			assert.ok(formatGitDiffPromptStub.calledWith(mockGitDiff), "formatGitDiffPrompt should be called with git diff")
			assert.ok(extractCommitMessageStub.calledOnce, "extractCommitMessage should be called once")
			assert.ok(
				extractCommitMessageStub.calledWith(mockApiResponse),
				"extractCommitMessage should be called with API response",
			)
			assert.ok(showCommitMessageOptionsStub.calledOnce, "showCommitMessageOptions should be called once")
			assert.ok(
				showCommitMessageOptionsStub.calledWith(mockCommitMessage),
				"showCommitMessageOptions should be called with commit message",
			)
		})
	})

	suite("边界情况处理", () => {
		test("应该处理空的工作目录", async () => {
			// Arrange
			sandbox.stub(pathUtils, "getCwd").resolves(undefined)
			const showErrorMessageStub = sandbox.stub(vscode.window, "showErrorMessage")

			// Act
			await controller.generateGitCommitMessage()

			// Assert
			assert.ok(showErrorMessageStub.calledOnce, "Should show error message")
			assert.ok(showErrorMessageStub.calledWith("No workspace folder open"), "Should show correct error message")
		})

		test("应该处理没有变更的情况", async () => {
			// Arrange
			sandbox.stub(pathUtils, "getCwd").resolves("/test/workspace")
			sandbox.stub(gitUtils, "getWorkingState").resolves("No changes in working directory")
			const showInformationMessageStub = sandbox.stub(vscode.window, "showInformationMessage")

			// Act
			await controller.generateGitCommitMessage()

			// Assert
			assert.ok(showInformationMessageStub.calledOnce, "Should show information message")
			assert.ok(
				showInformationMessageStub.calledWith("No changes in workspace for commit message"),
				"Should show correct information message",
			)
		})

		test("应该处理空的提交信息", async () => {
			// Arrange
			const mockGitDiff = "diff --git a/test.js b/test.js"

			sandbox.stub(pathUtils, "getCwd").resolves("/test/workspace")
			sandbox.stub(gitUtils, "getWorkingState").resolves(mockGitDiff)

			sandbox.stub(controller as any, "getAllExtensionState").resolves({
				apiConfiguration: { apiProvider: "anthropic" },
			})

			const mockApiHandler = {
				createMessage: sandbox.stub().returns(
					(async function* () {
						yield { type: "text", text: "" }
					})(),
				),
				getModel: sandbox.stub().returns({
					id: "test-model",
					info: { maxTokens: 4096 },
				}),
			}
			sandbox.stub({ buildApiHandler }, "buildApiHandler").returns(mockApiHandler)

			sandbox.stub(commitMessageGenerator, "formatGitDiffPrompt").returns("prompt")
			sandbox.stub(commitMessageGenerator, "extractCommitMessage").returns("")

			const showErrorMessageStub = sandbox.stub(vscode.window, "showErrorMessage")
			sandbox.stub(vscode.window, "withProgress").callsFake(async (options, callback) => {
				return await callback(
					{ report: sandbox.stub() },
					{ isCancellationRequested: false, onCancellationRequested: sandbox.stub() },
				)
			})

			// Act
			await controller.generateGitCommitMessage()

			// Assert
			assert.ok(
				showErrorMessageStub.calledWith("Failed to generate commit message"),
				"Should show error for empty commit message",
			)
		})

		test("应该处理特殊字符", async () => {
			// Arrange
			const mockGitDiff = `diff --git a/test.js b/test.js
+++ b/test.js
@@ -1 +1 @@
-console.log("hello");
+console.log("hello 世界! @#$%^&*()");`

			const mockCommitMessage = "feat: update console message with special characters"

			sandbox.stub(pathUtils, "getCwd").resolves("/test/workspace")
			sandbox.stub(gitUtils, "getWorkingState").resolves(mockGitDiff)

			sandbox.stub(controller as any, "getAllExtensionState").resolves({
				apiConfiguration: { apiProvider: "anthropic" },
			})

			const mockApiHandler = {
				createMessage: sandbox.stub().returns(
					(async function* () {
						yield { type: "text", text: mockCommitMessage }
					})(),
				),
				getModel: sandbox.stub().returns({
					id: "test-model",
					info: { maxTokens: 4096 },
				}),
			}
			sandbox.stub({ buildApiHandler }, "buildApiHandler").returns(mockApiHandler)

			sandbox.stub(commitMessageGenerator, "formatGitDiffPrompt").returns("prompt")
			sandbox.stub(commitMessageGenerator, "extractCommitMessage").returns(mockCommitMessage)
			const showCommitMessageOptionsStub = sandbox.stub(commitMessageGenerator, "showCommitMessageOptions").resolves()

			sandbox.stub(vscode.window, "withProgress").callsFake(async (options, callback) => {
				return await callback(
					{ report: sandbox.stub() },
					{ isCancellationRequested: false, onCancellationRequested: sandbox.stub() },
				)
			})

			// Act
			await controller.generateGitCommitMessage()

			// Assert
			assert.ok(showCommitMessageOptionsStub.calledWith(mockCommitMessage), "Should handle special characters correctly")
		})
	})

	suite("错误处理机制", () => {
		test("应该处理 API 调用错误", async () => {
			// Arrange
			const mockGitDiff = "diff --git a/test.js b/test.js"

			sandbox.stub(pathUtils, "getCwd").resolves("/test/workspace")
			sandbox.stub(gitUtils, "getWorkingState").resolves(mockGitDiff)

			sandbox.stub(controller as any, "getAllExtensionState").resolves({
				apiConfiguration: { apiProvider: "anthropic" },
			})

			const mockApiHandler = {
				createMessage: sandbox.stub().throws(new Error("API Error")),
				getModel: sandbox.stub().returns({
					id: "test-model",
					info: { maxTokens: 4096 },
				}),
			}
			sandbox.stub({ buildApiHandler }, "buildApiHandler").returns(mockApiHandler)

			sandbox.stub(commitMessageGenerator, "formatGitDiffPrompt").returns("prompt")

			const showErrorMessageStub = sandbox.stub(vscode.window, "showErrorMessage")
			sandbox.stub(vscode.window, "withProgress").callsFake(async (options, callback) => {
				return await callback(
					{ report: sandbox.stub() },
					{ isCancellationRequested: false, onCancellationRequested: sandbox.stub() },
				)
			})

			// Act
			await controller.generateGitCommitMessage()

			// Assert
			assert.ok(
				showErrorMessageStub.calledWith("Failed to generate commit message: API Error"),
				"Should show API error message",
			)
		})

		test("应该处理 git diff 获取错误", async () => {
			// Arrange
			sandbox.stub(pathUtils, "getCwd").resolves("/test/workspace")
			sandbox.stub(gitUtils, "getWorkingState").throws(new Error("Git Error"))

			const showErrorMessageStub = sandbox.stub(vscode.window, "showErrorMessage")

			// Act
			await controller.generateGitCommitMessage()

			// Assert
			assert.ok(
				showErrorMessageStub.calledWith("Failed to generate commit message: Git Error"),
				"Should show git error message",
			)
		})

		test("应该处理导入错误", async () => {
			// Arrange
			const mockGitDiff = "diff --git a/test.js b/test.js"

			sandbox.stub(pathUtils, "getCwd").resolves("/test/workspace")
			sandbox.stub(gitUtils, "getWorkingState").resolves(mockGitDiff)

			// Mock import to throw error
			const originalImport = (global as any).__importStar
			;(global as any).__importStar = sandbox.stub().throws(new Error("Import Error"))

			const showErrorMessageStub = sandbox.stub(vscode.window, "showErrorMessage")
			sandbox.stub(vscode.window, "withProgress").callsFake(async (options, callback) => {
				return await callback(
					{ report: sandbox.stub() },
					{ isCancellationRequested: false, onCancellationRequested: sandbox.stub() },
				)
			})

			// Act
			await controller.generateGitCommitMessage()

			// Assert
			assert.ok(showErrorMessageStub.called, "Should show error message for import failure")

			// Restore
			;(global as any).__importStar = originalImport
		})
	})

	suite("集成测试", () => {
		test("应该完整地执行提交信息生成流程", async () => {
			// Arrange
			const mockGitDiff = `diff --git a/src/test.ts b/src/test.ts
index 1234567..abcdefg 100644
--- a/src/test.ts
+++ b/src/test.ts
@@ -1,5 +1,6 @@
 export function calculateSum(a: number, b: number): number {
+  // Add input validation
+  if (typeof a !== 'number' || typeof b !== 'number') throw new Error('Invalid input');
   return a + b;
 }`

			const mockApiResponse =
				"feat: add input validation to calculateSum function\n\n- Add type checking for function parameters\n- Throw error for invalid input types"
			const expectedCommitMessage =
				"feat: add input validation to calculateSum function\n\n- Add type checking for function parameters\n- Throw error for invalid input types"

			// Mock all dependencies
			sandbox.stub(pathUtils, "getCwd").resolves("/test/workspace")
			sandbox.stub(gitUtils, "getWorkingState").resolves(mockGitDiff)

			sandbox.stub(controller as any, "getAllExtensionState").resolves({
				apiConfiguration: {
					apiProvider: "anthropic",
					apiKey: "test-key",
				},
			})

			const mockApiHandler = {
				createMessage: sandbox.stub().returns(
					(async function* () {
						yield { type: "text", text: mockApiResponse }
					})(),
				),
				getModel: sandbox.stub().returns({
					id: "test-model",
					info: { maxTokens: 4096 },
				}),
			}
			sandbox.stub({ buildApiHandler }, "buildApiHandler").returns(mockApiHandler)

			// Use real functions from commit-message-generator
			const showCommitMessageOptionsStub = sandbox.stub(commitMessageGenerator, "showCommitMessageOptions").resolves()

			sandbox.stub(vscode.window, "withProgress").callsFake(async (options, callback) => {
				return await callback(
					{ report: sandbox.stub() },
					{ isCancellationRequested: false, onCancellationRequested: sandbox.stub() },
				)
			})

			// Act
			await controller.generateGitCommitMessage()

			// Assert
			assert.ok(showCommitMessageOptionsStub.calledOnce, "Should call showCommitMessageOptions")
			const actualCommitMessage = showCommitMessageOptionsStub.getCall(0).args[0]
			assert.strictEqual(actualCommitMessage, expectedCommitMessage, "Should generate correct commit message")
		})
	})
})
