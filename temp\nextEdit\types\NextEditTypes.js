"use strict"
Object.defineProperty(exports, "__esModule", { value: true })
exports.DEFAULT_NEXT_EDIT_CONFIG =
	exports.NextEditEventType =
	exports.NextEditCategory =
	exports.NextEditType =
	exports.NextEditPriority =
		void 0
/**
 * Priority levels for Next Edit suggestions
 */
var NextEditPriority
;(function (NextEditPriority) {
	NextEditPriority["HIGH"] = "high"
	NextEditPriority["MEDIUM"] = "medium"
	NextEditPriority["LOW"] = "low"
})(NextEditPriority || (exports.NextEditPriority = NextEditPriority = {}))
/**
 * Types of Next Edit operations
 */
var NextEditType
;(function (NextEditType) {
	NextEditType["ADD"] = "add"
	NextEditType["MODIFY"] = "modify"
	NextEditType["DELETE"] = "delete"
})(NextEditType || (exports.NextEditType = NextEditType = {}))
/**
 * Categories of Next Edit suggestions
 */
var NextEditCategory
;(function (NextEditCategory) {
	NextEditCategory["COMPLETION"] = "completion"
	NextEditCategory["ERROR_HANDLING"] = "error_handling"
	NextEditCategory["TYPE_SAFETY"] = "type_safety"
	NextEditCategory["TESTING"] = "testing"
	NextEditCategory["DOCUMENTATION"] = "documentation"
	NextEditCategory["BUG_FIX"] = "bug_fix"
})(NextEditCategory || (exports.NextEditCategory = NextEditCategory = {}))
/**
 * Event types for Next Edit service
 */
var NextEditEventType
;(function (NextEditEventType) {
	NextEditEventType["SUGGESTION_APPLIED"] = "suggestion_applied"
	NextEditEventType["SUGGESTION_IGNORED"] = "suggestion_ignored"
	NextEditEventType["SUGGESTIONS_GENERATED"] = "suggestions_generated"
	NextEditEventType["SUGGESTIONS_CLEARED"] = "suggestions_cleared"
	NextEditEventType["SERVICE_ENABLED"] = "service_enabled"
	NextEditEventType["SERVICE_DISABLED"] = "service_disabled"
})(NextEditEventType || (exports.NextEditEventType = NextEditEventType = {}))
/**
 * Default configuration for Next Edit service
 */
exports.DEFAULT_NEXT_EDIT_CONFIG = {
	enabled: true,
	debounceDelayMs: 2000,
	maxSuggestions: 5,
	contextWindowSize: 200,
	allowedTaskTypes: [
		NextEditCategory.COMPLETION,
		NextEditCategory.ERROR_HANDLING,
		NextEditCategory.TYPE_SAFETY,
		NextEditCategory.TESTING,
		NextEditCategory.DOCUMENTATION,
		NextEditCategory.BUG_FIX,
	],
}
