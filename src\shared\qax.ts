/**
 * Qax 共享配置
 * 前后端共享的 Qax 配置类型、常量和函数
 * <AUTHOR>
 */

// Qax 环境类型
export type QaxEnvironment = "dev" | "test" | "prod"

// Qax 默认模型配置
export const qaxDefaultModels = ["DeepSeek-V3", "DeepSeek-R1"] as const
export const qaxDefaultModelId = "DeepSeek-V3" as const

// Qax 扩展标识符和回调配置
export const QAX_EXTENSION_ID = "qi-anxin-group.qax-codegen" as const
export const QAX_AUTH_CALLBACK_PATH = "/qax-codegen-auth" as const

/**
 * Qax 配置接口
 */
export interface QaxConfig {
	isQaxMode: boolean
	environment: QaxEnvironment
	qaxCodegenDomain: string
	qaxAIPlatformDomain: string
	enableAutocomplete: boolean
	defaultModel: string
	defaultModels: readonly string[]
}

/**
 * 根据环境获取 Qax Codegen 域名
 */
function getCodegenDomain(env: QaxEnvironment): string {
	switch (env) {
		case "dev":
			return "https://codegen-dev.qianxin-inc.cn"
		case "test":
			return "https://codegen-test.qianxin-inc.cn"
		case "prod":
			return "https://codegen.qianxin-inc.cn"
		default:
			return "https://codegen.qianxin-inc.cn"
	}
}

/**
 * 获取 Qax 环境类型
 */
function getEnvironment(): QaxEnvironment {
	const env = process.env.VSCODE_QAX_ENV?.toLowerCase()
	if (env === "dev" || env === "test" || env === "prod") {
		return env
	}
	return "prod" // 默认生产环境
}

/**
 * 获取Qax配置
 */
export function getQaxConfig(): QaxConfig {
	// 从环境变量读取配置
	const isQaxMode = process.env.VSCODE_QAX_MODE !== "false" // 默认为 true，只有明确设置为 false 才禁用
	const environment = getEnvironment()
	const qaxCodegenDomain = process.env.VSCODE_QAX_CODEGEN_DOMAIN || getCodegenDomain(environment)
	const qaxAIPlatformDomain = process.env.VSCODE_QAX_AI_DOMAIN || "https://aip.b.qianxin-inc.cn"
	const enableAutocomplete = process.env.VSCODE_QAX_AUTOCOMPLETE !== "false" // 默认启用

	return {
		isQaxMode,
		environment,
		qaxCodegenDomain,
		qaxAIPlatformDomain,
		enableAutocomplete,
		defaultModel: qaxDefaultModelId,
		defaultModels: qaxDefaultModels,
	}
}

/**
 * 获取 Qax Codegen 域名
 */
export function getQaxCodegenDomain(): string {
	return getQaxConfig().qaxCodegenDomain
}

/**
 * 获取当前使用的扩展 ID
 * 开发时默认使用 Cline 的扩展 ID，打包时使用 QAX 的扩展 ID
 */
export function getCurrentExtensionId(): string {
	// 如果环境变量明确设置了扩展 ID，使用环境变量的值
	if (process.env.VSCODE_EXTENSION_ID) {
		return process.env.VSCODE_EXTENSION_ID
	}

	// 默认使用 Cline 的扩展 ID（开发时）
	// 打包时会通过构建脚本设置 VSCODE_EXTENSION_ID 环境变量
	return "saoudrizwan.claude-dev"
}
export function getMemoryConfigKey(): string {
	return getCurrentExtensionId() === QAX_EXTENSION_ID ? "qax-codegen.memory" : "cline.memory"
}
export function getAutocompleConfigKey(): string {
	return getCurrentExtensionId() === QAX_EXTENSION_ID ? "qax-codegen.autocomplete" : "cline.autocomplete"
}

/**
 * 获取 Qax 认证回调 URL
 * @param uriScheme VS Code URI scheme (通常是 "vscode")
 * @returns 完整的回调 URL
 */
export function getQaxAuthCallbackUrl(uriScheme?: string): string {
	const scheme = uriScheme || "vscode"
	const extensionId = getCurrentExtensionId()
	return `${scheme}://${extensionId}${QAX_AUTH_CALLBACK_PATH}`
}

/**
 * 获取 Qax Codegen 登录地址
 * Following Codegen 3.0.0 URL construction pattern
 */
export function getQaxCodegenLoginUrl(callbackUrl?: string, state?: string): string {
	const domain = getQaxCodegenDomain()

	// Use URL object for more graceful query construction (following Codegen 3.0.0 pattern)
	const authUrl = new URL(`${domain}/api/v1/auth/sso/login`)

	if (callbackUrl) {
		authUrl.searchParams.set("ref", callbackUrl)
	}

	if (state) {
		authUrl.searchParams.set("state", state)
	}

	return authUrl.toString()
}

/**
 * 获取 Qax Codegen Base URL (API 基础地址)
 */
export function getQaxCodegenBaseUrl(): string {
	const domain = getQaxCodegenDomain()
	return `${domain}/api/v1`
}

/**
 * 获取 Qax 大模型平台域名
 */
export function getQaxAIPlatformDomain(): string {
	return getQaxConfig().qaxAIPlatformDomain
}

/**
 * 获取 Qax 大模型平台 Base URL (API 基础地址)
 */
export function getQaxAIPlatformBaseUrl(): string {
	const domain = getQaxAIPlatformDomain()
	return `${domain}/v2`
}

/**
 * 获取默认模型
 */
export function getQaxDefaultModel(): string {
	return getQaxConfig().defaultModel
}

/**
 * 获取默认模型列表
 */
export function getQaxDefaultModels(): readonly string[] {
	return getQaxConfig().defaultModels
}

/**
 * 获取当前环境
 */
export function getQaxCurrentEnvironment(): QaxEnvironment {
	return getQaxConfig().environment
}

/**
 * 检查是否为 Qax 模式
 */
export function isQaxMode(): boolean {
	return getQaxConfig().isQaxMode
}

/**
 * 获取 Qax Codegen 模型列表接口 URL
 */
export function getQaxCodegenModelsUrl(): string {
	const baseUrl = getQaxCodegenBaseUrl()
	return `${baseUrl}/chat/models`
}

/**
 * 获取 Qax 大模型平台模型列表接口 URL
 */
export function getQaxAIPlatformModelsUrl(): string {
	const domain = getQaxAIPlatformDomain()
	return `${domain}/v1/chat/models`
}
