module.exports = {
    preset: 'ts-jest',
    testEnvironment: 'node',
    roots: ['<rootDir>/src'],
    testMatch: [
        '**/__tests__/**/*.test.ts',
        '**/?(*.)+(spec|test).ts'
    ],
    transform: {
        '^.+\\.ts$': 'ts-jest',
    },
    collectCoverageFrom: [
        'src/**/*.ts',
        '!src/**/*.d.ts',
        '!src/**/__tests__/**',
        '!src/**/node_modules/**',
    ],
    coverageDirectory: 'coverage',
    coverageReporters: [
        'text',
        'lcov',
        'html'
    ],
    setupFilesAfterEnv: ['<rootDir>/src/services/autocomplete/qaxNextEdit/services/__tests__/setup.ts'],
    moduleNameMapping: {
        '^vscode$': '<rootDir>/src/services/autocomplete/qaxNextEdit/services/__tests__/__mocks__/vscode.ts'
    },
    testPathIgnorePatterns: ['/node_modules/', '/dist/', '/out/'],
    testTimeout: 10000,
    verbose: true,
    collectCoverage: true,
    coverageThreshold: {
        global: {
            branches: 95,
            functions: 95,
            lines: 95,
            statements: 95
        }
    }
};
