import { qaxModelInfoDefaults } from "@shared/api"
import { getAsVar, VSC_DESCRIPTION_FOREGROUND } from "@/utils/vscStyles"
import { VSCodeCheckbox } from "@vscode/webview-ui-toolkit/react"
import { useState } from "react"
import { DebouncedTextField } from "@/components/settings/common/DebouncedTextField"
import { ModelInfoView } from "@/components/settings/common/ModelInfoView"
import { normalizeApiConfiguration } from "@/components/settings/utils/providerUtils"
import { useExtensionState } from "@/context/ExtensionStateContext"
import { useApiConfigurationHandlers } from "@/components/settings/utils/useApiConfigurationHandlers"
import { ClineAccountInfoCard } from "@/components/settings/ClineAccountInfoCard"
import QaxCodegenModelPicker from "../QaxCodegenModelPicker"

/**
 * QAX Codegen 提供器配置组件属性
 * <AUTHOR>
 */
interface QaxCodegenProviderProps {
	showModelOptions: boolean
	isPopup?: boolean
}

/**
 * QAX Codegen 提供器配置组件
 * 提供 QAX Codegen 服务的完整配置界面，包括账户信息、模型选择和参数配置
 * <AUTHOR>
 */
export const QaxCodegenProvider = ({ showModelOptions, isPopup }: QaxCodegenProviderProps) => {
	const { apiConfiguration } = useExtensionState()
	const { handleFieldChange } = useApiConfigurationHandlers()
	const [modelConfigurationSelected, setModelConfigurationSelected] = useState(false)
	const { selectedModelId, selectedModelInfo } = normalizeApiConfiguration(apiConfiguration)

	return (
		<div>
			<ClineAccountInfoCard />

			<div style={{ marginTop: 15 }}>
				<QaxCodegenModelPicker isPopup={isPopup} />
			</div>

			<div
				style={{
					color: getAsVar(VSC_DESCRIPTION_FOREGROUND),
					display: "flex",
					margin: "15px 0 10px 0",
					cursor: "pointer",
					alignItems: "center",
				}}
				onClick={() => setModelConfigurationSelected((val) => !val)}>
				<span
					className={`codicon ${modelConfigurationSelected ? "codicon-chevron-down" : "codicon-chevron-right"}`}
					style={{ marginRight: "4px" }}
				/>
				<span style={{ fontWeight: 500, textTransform: "uppercase" }}>模型配置</span>
			</div>

			{modelConfigurationSelected && (
				<>
					<VSCodeCheckbox
						checked={!!apiConfiguration?.qaxCodegenModelInfo?.supportsImages}
						onChange={(e: any) => {
							const isChecked = e.target.checked === true
							const modelInfo = apiConfiguration?.qaxCodegenModelInfo || { ...qaxModelInfoDefaults }
							modelInfo.supportsImages = isChecked
							handleFieldChange("qaxCodegenModelInfo", modelInfo)
						}}>
						支持图像
					</VSCodeCheckbox>

					<VSCodeCheckbox
						checked={!!apiConfiguration?.qaxCodegenModelInfo?.isR1FormatRequired}
						onChange={(e: any) => {
							const isChecked = e.target.checked === true
							const modelInfo = apiConfiguration?.qaxCodegenModelInfo || { ...qaxModelInfoDefaults }
							handleFieldChange("qaxCodegenModelInfo", { ...modelInfo, isR1FormatRequired: isChecked })
						}}>
						启用 R1 消息格式
					</VSCodeCheckbox>

					<div style={{ display: "flex", gap: 10, marginTop: "5px" }}>
						<DebouncedTextField
							initialValue={
								apiConfiguration?.qaxCodegenModelInfo?.contextWindow?.toString() ||
								qaxModelInfoDefaults.contextWindow?.toString() ||
								""
							}
							style={{ flex: 1 }}
							onChange={(value) => {
								const modelInfo = apiConfiguration?.qaxCodegenModelInfo || { ...qaxModelInfoDefaults }
								modelInfo.contextWindow = Number(value)
								handleFieldChange("qaxCodegenModelInfo", modelInfo)
							}}>
							<span style={{ fontWeight: 500 }}>上下文窗口大小</span>
						</DebouncedTextField>

						<DebouncedTextField
							initialValue={
								apiConfiguration?.qaxCodegenModelInfo?.maxTokens?.toString() ||
								qaxModelInfoDefaults.maxTokens?.toString() ||
								""
							}
							style={{ flex: 1 }}
							onChange={(value) => {
								const modelInfo = apiConfiguration?.qaxCodegenModelInfo || { ...qaxModelInfoDefaults }
								modelInfo.maxTokens = Number(value)
								handleFieldChange("qaxCodegenModelInfo", modelInfo)
							}}>
							<span style={{ fontWeight: 500 }}>最大输出令牌数</span>
						</DebouncedTextField>
					</div>

					<div style={{ display: "flex", gap: 10, marginTop: "5px" }}>
						<DebouncedTextField
							initialValue={
								apiConfiguration?.qaxCodegenModelInfo?.inputPrice?.toString() ||
								qaxModelInfoDefaults.inputPrice?.toString() ||
								""
							}
							style={{ flex: 1 }}
							onChange={(value) => {
								const modelInfo = apiConfiguration?.qaxCodegenModelInfo || { ...qaxModelInfoDefaults }
								modelInfo.inputPrice = Number(value)
								handleFieldChange("qaxCodegenModelInfo", modelInfo)
							}}>
							<span style={{ fontWeight: 500 }}>输入价格 / 100万令牌</span>
						</DebouncedTextField>

						<DebouncedTextField
							initialValue={
								apiConfiguration?.qaxCodegenModelInfo?.outputPrice?.toString() ||
								qaxModelInfoDefaults.outputPrice?.toString() ||
								""
							}
							style={{ flex: 1 }}
							onChange={(value) => {
								const modelInfo = apiConfiguration?.qaxCodegenModelInfo || { ...qaxModelInfoDefaults }
								modelInfo.outputPrice = Number(value)
								handleFieldChange("qaxCodegenModelInfo", modelInfo)
							}}>
							<span style={{ fontWeight: 500 }}>输出价格 / 100万令牌</span>
						</DebouncedTextField>
					</div>

					<div style={{ display: "flex", gap: 10, marginTop: "5px" }}>
						<DebouncedTextField
							initialValue={
								apiConfiguration?.qaxCodegenModelInfo?.temperature
									? apiConfiguration.qaxCodegenModelInfo.temperature.toString()
									: (qaxModelInfoDefaults.temperature?.toString() ?? "")
							}
							onChange={(value) => {
								const modelInfo = apiConfiguration?.qaxCodegenModelInfo
									? apiConfiguration.qaxCodegenModelInfo
									: { ...qaxModelInfoDefaults }

								const shouldPreserveFormat = value.endsWith(".") || (value.includes(".") && value.endsWith("0"))

								modelInfo.temperature =
									value === ""
										? qaxModelInfoDefaults.temperature
										: shouldPreserveFormat
											? (value as any)
											: parseFloat(value)

								handleFieldChange("qaxCodegenModelInfo", modelInfo)
							}}>
							<span style={{ fontWeight: 500 }}>Temperature</span>
						</DebouncedTextField>
					</div>
				</>
			)}

			<p
				style={{
					fontSize: "12px",
					marginTop: 10,
					color: "var(--vscode-descriptionForeground)",
				}}>
				QAX Codegen 使用 JWT 认证，请确保已登录 QAX 账户。
			</p>

			{showModelOptions && (
				<ModelInfoView selectedModelId={selectedModelId} modelInfo={selectedModelInfo} isPopup={isPopup} />
			)}
		</div>
	)
}
