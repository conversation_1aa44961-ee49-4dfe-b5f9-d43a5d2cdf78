#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🧪 Running QaxLSPService Tests with 95% Coverage Target\n');

// Check if Jest is installed
const jestPath = path.join(__dirname, 'node_modules', '.bin', 'jest');
const jestExists = fs.existsSync(jestPath) || fs.existsSync(jestPath + '.cmd');

if (!jestExists) {
    console.log('📦 Installing Jest and dependencies...\n');
    
    const installProcess = spawn('npm', ['install', '--save-dev', 
        'jest', 
        '@types/jest', 
        'ts-jest', 
        'typescript'
    ], {
        stdio: 'inherit',
        shell: true,
        cwd: __dirname
    });

    installProcess.on('close', (code) => {
        if (code === 0) {
            console.log('\n✅ Dependencies installed successfully!\n');
            runTests();
        } else {
            console.error('\n❌ Failed to install dependencies');
            process.exit(1);
        }
    });
} else {
    runTests();
}

function runTests() {
    console.log('🚀 Starting test execution...\n');
    
    const testArgs = [
        '--config=jest.config.js',
        '--testPathPattern=QaxLSPService.test.ts',
        '--coverage',
        '--verbose',
        '--detectOpenHandles',
        '--forceExit'
    ];

    const testProcess = spawn('npx', ['jest', ...testArgs], {
        stdio: 'inherit',
        shell: true,
        cwd: __dirname
    });

    testProcess.on('close', (code) => {
        console.log('\n' + '='.repeat(80));
        
        if (code === 0) {
            console.log('✅ ALL TESTS PASSED WITH 95%+ COVERAGE!');
            console.log('🎉 QaxLSPService is fully tested and ready for production!');
            
            // Check if coverage report exists
            const coverageDir = path.join(__dirname, 'coverage');
            if (fs.existsSync(coverageDir)) {
                console.log('\n📊 Coverage report generated in: ./coverage/');
                console.log('📄 Open ./coverage/lcov-report/index.html to view detailed coverage');
            }
        } else {
            console.log('❌ TESTS FAILED OR COVERAGE BELOW 95%');
            console.log('🔧 Please review the test results and fix any issues');
            
            if (code === 1) {
                console.log('\n💡 Common issues:');
                console.log('   - Test failures: Check test logic and mock implementations');
                console.log('   - Coverage below 95%: Add more test cases for uncovered code paths');
                console.log('   - TypeScript errors: Ensure all types are properly mocked');
            }
        }
        
        console.log('='.repeat(80));
        process.exit(code);
    });

    testProcess.on('error', (error) => {
        console.error('\n❌ Error running tests:', error.message);
        process.exit(1);
    });
}

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n\n⚠️  Test execution interrupted by user');
    process.exit(130);
});

process.on('SIGTERM', () => {
    console.log('\n\n⚠️  Test execution terminated');
    process.exit(143);
});
