#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🧪 Running QaxLSPService Tests\n');

// Create a minimal Jest config for this specific test
const jestConfig = {
    preset: 'ts-jest',
    testEnvironment: 'node',
    roots: ['<rootDir>/src'],
    testMatch: ['**/QaxLSPService.test.ts'],
    transform: {
        '^.+\\.ts$': 'ts-jest',
    },
    collectCoverageFrom: [
        'src/services/autocomplete/qaxNextEdit/services/QaxLSPService.ts'
    ],
    coverageDirectory: 'coverage',
    coverageReporters: ['text', 'lcov', 'html'],
    testTimeout: 10000,
    verbose: true,
    collectCoverage: true,
    coverageThreshold: {
        global: {
            branches: 90,
            functions: 90,
            lines: 90,
            statements: 90
        }
    },
    moduleNameMapping: {
        '^vscode$': '<rootDir>/src/services/autocomplete/qaxNextEdit/services/__tests__/__mocks__/vscode.ts'
    },
    setupFilesAfterEnv: ['<rootDir>/src/services/autocomplete/qaxNextEdit/services/__tests__/setup.ts']
};

// Write the config to a temporary file
fs.writeFileSync('jest.qax.config.js', `module.exports = ${JSON.stringify(jestConfig, null, 2)};`);

console.log('📋 Test Configuration:');
console.log('   Target file: QaxLSPService.ts');
console.log('   Test file: QaxLSPService.test.ts');
console.log('   Coverage threshold: 90%');
console.log('   Timeout: 10 seconds\n');

// Run Jest with the specific config
const jestProcess = spawn('npx', [
    'jest',
    '--config=jest.qax.config.js',
    '--coverage',
    '--verbose'
], {
    stdio: 'inherit',
    shell: true,
    cwd: __dirname
});

jestProcess.on('close', (code) => {
    // Clean up temporary config file
    try {
        fs.unlinkSync('jest.qax.config.js');
    } catch (error) {
        // Ignore cleanup errors
    }

    console.log('\n' + '='.repeat(60));
    
    if (code === 0) {
        console.log('✅ ALL TESTS PASSED!');
        console.log('🎉 QaxLSPService test suite completed successfully!');
        
        // Check if coverage report exists
        if (fs.existsSync('coverage/lcov-report/index.html')) {
            console.log('\n📊 Coverage Report Generated:');
            console.log('   Open: coverage/lcov-report/index.html');
        }
        
        console.log('\n🚀 Ready for production!');
    } else {
        console.log('❌ TESTS FAILED OR COVERAGE BELOW THRESHOLD');
        console.log('🔧 Please review the output above and fix any issues');
        
        console.log('\n💡 Common solutions:');
        console.log('   - Check TypeScript compilation errors');
        console.log('   - Verify mock implementations');
        console.log('   - Add more test cases for uncovered code');
        console.log('   - Fix failing assertions');
    }
    
    console.log('='.repeat(60));
    process.exit(code);
});

jestProcess.on('error', (error) => {
    console.error('\n❌ Failed to run tests:', error.message);
    
    if (error.message.includes('jest')) {
        console.log('\n💡 Try installing Jest:');
        console.log('   npm install --save-dev jest @types/jest ts-jest typescript');
    }
    
    process.exit(1);
});

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n\n⚠️  Test execution interrupted');
    process.exit(130);
});

process.on('SIGTERM', () => {
    console.log('\n\n⚠️  Test execution terminated');
    process.exit(143);
});
