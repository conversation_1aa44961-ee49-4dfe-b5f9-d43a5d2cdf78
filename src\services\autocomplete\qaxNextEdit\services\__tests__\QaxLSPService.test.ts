import * as vscode from 'vscode';
import { QaxLSPService } from '../QaxLSPService';
import { QaxChangeType } from '../../types/QaxNextEditTypes';

// Mock VSCode API
jest.mock('vscode', () => {
    const mockExecuteCommand = jest.fn();
    const mockOnDidChangeDiagnostics = jest.fn();

    return {
        commands: {
            executeCommand: mockExecuteCommand,
        },
        languages: {
            onDidChangeDiagnostics: mockOnDidChangeDiagnostics,
        },
    Uri: {
        file: (path: string) => ({ fsPath: path, toString: () => `file://${path}` }),
    },
    Position: jest.fn().mockImplementation((line: number, character: number) => ({
        line,
        character,
        isAfter: jest.fn(),
        isBefore: jest.fn(),
        isBeforeOrEqual: jest.fn(),
        isAfterOrEqual: jest.fn(),
        isEqual: jest.fn(),
        compareTo: jest.fn(),
        translate: jest.fn(),
        with: jest.fn(),
    })),
    Range: jest.fn().mockImplementation((start: any, end: any) => ({
        start,
        end,
        isEmpty: false,
        isSingleLine: true,
        contains: jest.fn(),
        isEqual: jest.fn(),
        intersection: jest.fn(),
        union: jest.fn(),
        with: jest.fn(),
    })),
    Location: jest.fn().mockImplementation((uri: any, range: any) => ({
        uri,
        range,
    })),
    SymbolKind: {
        Function: 12,
        Variable: 13,
        Class: 5,
        Method: 6,
    },
        Disposable: {
            from: jest.fn().mockReturnValue({ dispose: jest.fn() }),
        },
    };
});

// Get the mocked functions
const mockExecuteCommand = require('vscode').commands.executeCommand;
const mockOnDidChangeDiagnostics = require('vscode').languages.onDidChangeDiagnostics;

describe('QaxLSPService', () => {
    let service: QaxLSPService;
    let mockDocument: vscode.TextDocument;
    let mockPosition: vscode.Position;
    let mockRange: vscode.Range;
    let mockLocation: vscode.Location;

    beforeEach(() => {
        // Reset all mocks
        jest.clearAllMocks();
        
        // Reset singleton instance
        (QaxLSPService as any).instance = null;
        
        // Setup mock returns
        mockOnDidChangeDiagnostics.mockReturnValue({ dispose: jest.fn() });
        
        // Create mock objects
        mockPosition = new vscode.Position(0, 0);
        mockRange = new vscode.Range(mockPosition, mockPosition);
        mockLocation = new vscode.Location(vscode.Uri.file('/test/file.js'), mockRange);
        
        mockDocument = {
            uri: vscode.Uri.file('/test/file.js'),
            fileName: '/test/file.js',
            isUntitled: false,
            languageId: 'javascript',
            version: 1,
            isDirty: false,
            isClosed: false,
            save: jest.fn(),
            eol: 1,
            lineCount: 10,
            lineAt: jest.fn(),
            offsetAt: jest.fn(),
            positionAt: jest.fn(),
            getText: jest.fn(),
            getWordRangeAtPosition: jest.fn(),
            validateRange: jest.fn(),
            validatePosition: jest.fn(),
        } as any;

        service = QaxLSPService.getInstance();
    });

    afterEach(() => {
        QaxLSPService.dispose();
    });

    describe('Singleton Pattern', () => {
        test('should return the same instance', () => {
            const instance1 = QaxLSPService.getInstance();
            const instance2 = QaxLSPService.getInstance();
            expect(instance1).toBe(instance2);
        });

        test('should dispose and reset instance', () => {
            const instance1 = QaxLSPService.getInstance();
            QaxLSPService.dispose();
            const instance2 = QaxLSPService.getInstance();
            expect(instance1).not.toBe(instance2);
        });
    });

    describe('getDocumentSymbols', () => {
        test('should return converted symbols when LSP returns symbols', async () => {
            const mockSymbols = [
                {
                    name: 'testFunction',
                    kind: vscode.SymbolKind.Function,
                    selectionRange: mockRange,
                    range: mockRange,
                    detail: 'function detail',
                    children: [
                        {
                            name: 'childSymbol',
                            kind: vscode.SymbolKind.Variable,
                            selectionRange: mockRange,
                            range: mockRange,
                            detail: 'variable detail',
                        }
                    ],
                }
            ];

            mockExecuteCommand.mockResolvedValue(mockSymbols);

            const result = await service.getDocumentSymbols(mockDocument);

            expect(mockExecuteCommand).toHaveBeenCalledWith(
                'vscode.executeDocumentSymbolProvider',
                mockDocument.uri
            );
            expect(result).toHaveLength(2); // parent + child
            expect(result[0].name).toBe('testFunction');
            expect(result[1].name).toBe('childSymbol');
            expect(result[1].containerName).toBe('testFunction');
        });

        test('should return empty array when LSP returns null', async () => {
            mockExecuteCommand.mockResolvedValue(null);

            const result = await service.getDocumentSymbols(mockDocument);

            expect(result).toEqual([]);
        });

        test('should return empty array when LSP throws error', async () => {
            mockExecuteCommand.mockRejectedValue(new Error('LSP error'));

            const result = await service.getDocumentSymbols(mockDocument);

            expect(result).toEqual([]);
        });
    });

    describe('getReferences', () => {
        test('should return references when LSP succeeds', async () => {
            const mockReferences = [mockLocation];
            mockExecuteCommand.mockResolvedValue(mockReferences);

            const result = await service.getReferences(mockDocument, mockPosition);

            expect(mockExecuteCommand).toHaveBeenCalledWith(
                'vscode.executeReferenceProvider',
                mockDocument.uri,
                mockPosition
            );
            expect(result).toEqual(mockReferences);
        });

        test('should return empty array when LSP returns null', async () => {
            mockExecuteCommand.mockResolvedValue(null);

            const result = await service.getReferences(mockDocument, mockPosition);

            expect(result).toEqual([]);
        });

        test('should return empty array when LSP throws error', async () => {
            mockExecuteCommand.mockRejectedValue(new Error('LSP error'));

            const result = await service.getReferences(mockDocument, mockPosition);

            expect(result).toEqual([]);
        });
    });

    describe('getDefinitions', () => {
        test('should return definitions when LSP succeeds', async () => {
            const mockDefinitions = [mockLocation];
            mockExecuteCommand.mockResolvedValue(mockDefinitions);

            const result = await service.getDefinitions(mockDocument, mockPosition);

            expect(mockExecuteCommand).toHaveBeenCalledWith(
                'vscode.executeDefinitionProvider',
                mockDocument.uri,
                mockPosition
            );
            expect(result).toEqual(mockDefinitions);
        });

        test('should return empty array when LSP returns null', async () => {
            mockExecuteCommand.mockResolvedValue(null);

            const result = await service.getDefinitions(mockDocument, mockPosition);

            expect(result).toEqual([]);
        });

        test('should return empty array when LSP throws error', async () => {
            mockExecuteCommand.mockRejectedValue(new Error('LSP error'));

            const result = await service.getDefinitions(mockDocument, mockPosition);

            expect(result).toEqual([]);
        });
    });

    describe('getTypeDefinitions', () => {
        test('should return type definitions when LSP succeeds', async () => {
            const mockTypeDefinitions = [mockLocation];
            mockExecuteCommand.mockResolvedValue(mockTypeDefinitions);

            const result = await service.getTypeDefinitions(mockDocument, mockPosition);

            expect(mockExecuteCommand).toHaveBeenCalledWith(
                'vscode.executeTypeDefinitionProvider',
                mockDocument.uri,
                mockPosition
            );
            expect(result).toEqual(mockTypeDefinitions);
        });

        test('should return empty array when LSP returns null', async () => {
            mockExecuteCommand.mockResolvedValue(null);

            const result = await service.getTypeDefinitions(mockDocument, mockPosition);

            expect(result).toEqual([]);
        });

        test('should return empty array when LSP throws error', async () => {
            mockExecuteCommand.mockRejectedValue(new Error('LSP error'));

            const result = await service.getTypeDefinitions(mockDocument, mockPosition);

            expect(result).toEqual([]);
        });
    });

    describe('getRenameInfo', () => {
        test('should return rename info when LSP succeeds', async () => {
            const mockRenameInfo = {
                range: mockRange,
                placeholder: 'testSymbol'
            };
            mockExecuteCommand.mockResolvedValue(mockRenameInfo);

            const result = await service.getRenameInfo(mockDocument, mockPosition);

            expect(mockExecuteCommand).toHaveBeenCalledWith(
                'vscode.prepareRename',
                mockDocument.uri,
                mockPosition
            );
            expect(result).toEqual(mockRenameInfo);
        });

        test('should return null when LSP returns null', async () => {
            mockExecuteCommand.mockResolvedValue(null);

            const result = await service.getRenameInfo(mockDocument, mockPosition);

            expect(result).toBeNull();
        });

        test('should return null when LSP throws error', async () => {
            mockExecuteCommand.mockRejectedValue(new Error('LSP error'));

            const result = await service.getRenameInfo(mockDocument, mockPosition);

            expect(result).toBeNull();
        });
    });

    describe('previewRename', () => {
        test('should return workspace edit when LSP succeeds', async () => {
            const mockWorkspaceEdit = { size: 1 };
            mockExecuteCommand.mockResolvedValue(mockWorkspaceEdit);

            const result = await service.previewRename(mockDocument, mockPosition, 'newName');

            expect(mockExecuteCommand).toHaveBeenCalledWith(
                'vscode.executeDocumentRenameProvider',
                mockDocument.uri,
                mockPosition,
                'newName'
            );
            expect(result).toEqual(mockWorkspaceEdit);
        });

        test('should return null when LSP returns null', async () => {
            mockExecuteCommand.mockResolvedValue(null);

            const result = await service.previewRename(mockDocument, mockPosition, 'newName');

            expect(result).toBeNull();
        });

        test('should return null when LSP throws error', async () => {
            mockExecuteCommand.mockRejectedValue(new Error('LSP error'));

            const result = await service.previewRename(mockDocument, mockPosition, 'newName');

            expect(result).toBeNull();
        });
    });

    describe('getHoverInfo', () => {
        test('should return first hover when LSP returns array', async () => {
            const mockHover = { contents: ['test hover'] };
            mockExecuteCommand.mockResolvedValue([mockHover]);

            const result = await service.getHoverInfo(mockDocument, mockPosition);

            expect(mockExecuteCommand).toHaveBeenCalledWith(
                'vscode.executeHoverProvider',
                mockDocument.uri,
                mockPosition
            );
            expect(result).toEqual(mockHover);
        });

        test('should return null when LSP returns empty array', async () => {
            mockExecuteCommand.mockResolvedValue([]);

            const result = await service.getHoverInfo(mockDocument, mockPosition);

            expect(result).toBeNull();
        });

        test('should return null when LSP returns null', async () => {
            mockExecuteCommand.mockResolvedValue(null);

            const result = await service.getHoverInfo(mockDocument, mockPosition);

            expect(result).toBeNull();
        });

        test('should return null when LSP throws error', async () => {
            mockExecuteCommand.mockRejectedValue(new Error('LSP error'));

            const result = await service.getHoverInfo(mockDocument, mockPosition);

            expect(result).toBeNull();
        });
    });

    describe('isLSPAvailable', () => {
        test('should return true when LSP returns symbols', async () => {
            mockExecuteCommand.mockResolvedValue([]);

            const result = await service.isLSPAvailable(mockDocument);

            expect(result).toBe(true);
        });

        test('should return false when LSP returns undefined', async () => {
            mockExecuteCommand.mockResolvedValue(undefined);

            const result = await service.isLSPAvailable(mockDocument);

            expect(result).toBe(false);
        });

        test('should return false when LSP throws error', async () => {
            mockExecuteCommand.mockRejectedValue(new Error('LSP error'));

            const result = await service.isLSPAvailable(mockDocument);

            expect(result).toBe(false);
        });
    });

    describe('getReferencesFromOriginalContent', () => {
        const originalContent = `
function testFunction() {
    const testVariable = 'hello';
    console.log(testVariable);
    return testVariable;
}

testFunction();
const result = testFunction();
`;

        test('should find references using text search', async () => {
            const position = new vscode.Position(1, 9); // position of 'testFunction'

            const result = await service.getReferencesFromOriginalContent(
                originalContent,
                '/test/file.js',
                position,
                'javascript'
            );

            expect(result.length).toBeGreaterThan(0);
            expect(result[0].uri.fsPath).toBe('/test/file.js');
        });

        test('should return empty array when symbol not found at position', async () => {
            const position = new vscode.Position(0, 0); // empty line

            const result = await service.getReferencesFromOriginalContent(
                originalContent,
                '/test/file.js',
                position,
                'javascript'
            );

            expect(result).toEqual([]);
        });

        test('should return empty array when position is out of bounds', async () => {
            const position = new vscode.Position(100, 0); // line doesn't exist

            const result = await service.getReferencesFromOriginalContent(
                originalContent,
                '/test/file.js',
                position,
                'javascript'
            );

            expect(result).toEqual([]);
        });

        test('should handle special characters in symbol names', async () => {
            const specialContent = 'const $special_var$ = 1;\nconsole.log($special_var$);';
            const position = new vscode.Position(0, 6); // position of '$special_var$'

            const result = await service.getReferencesFromOriginalContent(
                specialContent,
                '/test/file.js',
                position,
                'javascript'
            );

            expect(result.length).toBe(2); // definition + usage
        });

        test('should return empty array when error occurs', async () => {
            // Test with invalid content that might cause errors
            const result = await service.getReferencesFromOriginalContent(
                null as any,
                '/test/file.js',
                mockPosition,
                'javascript'
            );

            expect(result).toEqual([]);
        });
    });

    describe('getDefinitionsFromOriginalContent', () => {
        const originalContent = `
function testFunction() {
    return 'hello';
}

const testVariable = 'world';
let anotherVar = 42;
var oldStyleVar = true;

class TestClass {
    testMethod() {
        return 'method';
    }
}

def pythonFunction():
    return 'python'
`;

        test('should find JavaScript function definition', async () => {
            const position = new vscode.Position(1, 9); // position of 'testFunction'

            const result = await service.getDefinitionsFromOriginalContent(
                originalContent,
                '/test/file.js',
                position,
                'javascript'
            );

            expect(result.length).toBeGreaterThan(0);
            expect(result[0].uri.fsPath).toBe('/test/file.js');
        });

        test('should find variable definitions', async () => {
            const jsContent = 'const testVar = 123;\nlet testVar2 = 456;\nvar testVar3 = 789;';
            const position = new vscode.Position(0, 6); // position of 'testVar'

            const result = await service.getDefinitionsFromOriginalContent(
                jsContent,
                '/test/file.js',
                position,
                'javascript'
            );

            expect(result.length).toBe(1);
        });

        test('should find class definitions', async () => {
            const jsContent = 'class MyClass { constructor() {} }';
            const position = new vscode.Position(0, 6); // position of 'MyClass'

            const result = await service.getDefinitionsFromOriginalContent(
                jsContent,
                '/test/file.js',
                position,
                'javascript'
            );

            expect(result.length).toBe(1);
        });

        test('should find Python function definitions', async () => {
            const pythonContent = 'def my_function():\n    return "hello"';
            const position = new vscode.Position(0, 4); // position of 'my_function'

            const result = await service.getDefinitionsFromOriginalContent(
                pythonContent,
                '/test/file.py',
                position,
                'python'
            );

            expect(result.length).toBe(1);
        });

        test('should find Python class definitions', async () => {
            const pythonContent = 'class MyPythonClass:\n    def __init__(self):\n        pass';
            const position = new vscode.Position(0, 6); // position of 'MyPythonClass'

            const result = await service.getDefinitionsFromOriginalContent(
                pythonContent,
                '/test/file.py',
                position,
                'python'
            );

            expect(result.length).toBe(1);
        });

        test('should use default patterns for unknown languages', async () => {
            const unknownContent = 'function unknownFunc() { return 1; }';
            const position = new vscode.Position(0, 9); // position of 'unknownFunc'

            const result = await service.getDefinitionsFromOriginalContent(
                unknownContent,
                '/test/file.unknown',
                position,
                'unknown'
            );

            expect(result.length).toBe(1);
        });

        test('should return empty array when symbol not found', async () => {
            const position = new vscode.Position(0, 0); // empty line

            const result = await service.getDefinitionsFromOriginalContent(
                originalContent,
                '/test/file.js',
                position,
                'javascript'
            );

            expect(result).toEqual([]);
        });

        test('should return empty array when error occurs', async () => {
            const result = await service.getDefinitionsFromOriginalContent(
                null as any,
                '/test/file.js',
                mockPosition,
                'javascript'
            );

            expect(result).toEqual([]);
        });
    });

    describe('isDefinitionLocation', () => {
        const originalContent = `
function testFunction() {
    const testVariable = 'hello';
    return testVariable;
}
`;

        test('should return true when position is at definition', async () => {
            const position = new vscode.Position(1, 9); // position of 'testFunction'

            const result = await service.isDefinitionLocation(
                originalContent,
                '/test/file.js',
                position,
                'javascript'
            );

            expect(result).toBe(true);
        });

        test('should return false when position is not at definition', async () => {
            const position = new vscode.Position(3, 11); // position of 'testVariable' usage

            const result = await service.isDefinitionLocation(
                originalContent,
                '/test/file.js',
                position,
                'javascript'
            );

            expect(result).toBe(false);
        });

        test('should return false when no definitions found', async () => {
            const position = new vscode.Position(0, 0); // empty line

            const result = await service.isDefinitionLocation(
                originalContent,
                '/test/file.js',
                position,
                'javascript'
            );

            expect(result).toBe(false);
        });

        test('should return false when error occurs', async () => {
            const result = await service.isDefinitionLocation(
                null as any,
                '/test/file.js',
                mockPosition,
                'javascript'
            );

            expect(result).toBe(false);
        });
    });

    describe('detectSymbolRename', () => {
        beforeEach(() => {
            mockExecuteCommand.mockClear();
        });

        test('should detect symbol rename when references exist', async () => {
            const mockReferences = [mockLocation];
            const mockDefinitions = [mockLocation];

            mockExecuteCommand
                .mockResolvedValueOnce(mockReferences) // getReferences call
                .mockResolvedValueOnce(mockDefinitions); // getDefinitions call

            const oldPosition = new vscode.Position(0, 0);
            const newPosition = new vscode.Position(0, 10);

            const result = await service.detectSymbolRename(
                mockDocument,
                oldPosition,
                newPosition,
                'oldName',
                'newName'
            );

            expect(result).not.toBeNull();
            expect(result?.type).toBe(QaxChangeType.VARIABLE_RENAME);
            expect(result?.oldValue).toBe('oldName');
            expect(result?.newValue).toBe('newName');
            expect(result?.confidence).toBe(0.9);
        });

        test('should return null when no references or definitions exist', async () => {
            mockExecuteCommand
                .mockResolvedValueOnce([]) // getReferences call
                .mockResolvedValueOnce([]); // getDefinitions call

            const oldPosition = new vscode.Position(0, 0);
            const newPosition = new vscode.Position(0, 10);

            const result = await service.detectSymbolRename(
                mockDocument,
                oldPosition,
                newPosition,
                'oldName',
                'newName'
            );

            expect(result).toBeNull();
        });

        test('should return null when error occurs', async () => {
            mockExecuteCommand.mockRejectedValue(new Error('LSP error'));

            const oldPosition = new vscode.Position(0, 0);
            const newPosition = new vscode.Position(0, 10);

            const result = await service.detectSymbolRename(
                mockDocument,
                oldPosition,
                newPosition,
                'oldName',
                'newName'
            );

            expect(result).toBeNull();
        });
    });

    describe('Private Methods', () => {
        describe('extractSymbolAtPosition', () => {
            test('should extract symbol at valid position', () => {
                const content = 'const testVariable = 123;';
                const position = new vscode.Position(0, 6); // position of 'testVariable'

                const result = (service as any).extractSymbolAtPosition(content, position);

                expect(result).toBe('testVariable');
            });

            test('should extract symbol with special characters', () => {
                const content = 'const $test_var$ = 123;';
                const position = new vscode.Position(0, 6); // position of '$test_var$'

                const result = (service as any).extractSymbolAtPosition(content, position);

                expect(result).toBe('$test_var$');
            });

            test('should return null when position is out of bounds (line)', () => {
                const content = 'const test = 123;';
                const position = new vscode.Position(10, 0); // line doesn't exist

                const result = (service as any).extractSymbolAtPosition(content, position);

                expect(result).toBeNull();
            });

            test('should return null when position is out of bounds (character)', () => {
                const content = 'const test = 123;';
                const position = new vscode.Position(0, 100); // character doesn't exist

                const result = (service as any).extractSymbolAtPosition(content, position);

                expect(result).toBeNull();
            });

            test('should return null when no symbol at position', () => {
                const content = 'const test = 123;';
                const position = new vscode.Position(0, 11); // position of '='

                const result = (service as any).extractSymbolAtPosition(content, position);

                expect(result).toBeNull();
            });

            test('should handle position at start of symbol', () => {
                const content = 'testSymbol';
                const position = new vscode.Position(0, 0); // start of symbol

                const result = (service as any).extractSymbolAtPosition(content, position);

                expect(result).toBe('testSymbol');
            });

            test('should handle position at end of symbol', () => {
                const content = 'testSymbol';
                const position = new vscode.Position(0, 9); // end of symbol

                const result = (service as any).extractSymbolAtPosition(content, position);

                expect(result).toBe('testSymbol');
            });
        });

        describe('isIdentifierChar', () => {
            test('should return true for letters', () => {
                expect((service as any).isIdentifierChar('a')).toBe(true);
                expect((service as any).isIdentifierChar('Z')).toBe(true);
            });

            test('should return true for numbers', () => {
                expect((service as any).isIdentifierChar('0')).toBe(true);
                expect((service as any).isIdentifierChar('9')).toBe(true);
            });

            test('should return true for underscore and dollar', () => {
                expect((service as any).isIdentifierChar('_')).toBe(true);
                expect((service as any).isIdentifierChar('$')).toBe(true);
            });

            test('should return false for special characters', () => {
                expect((service as any).isIdentifierChar(' ')).toBe(false);
                expect((service as any).isIdentifierChar('.')).toBe(false);
                expect((service as any).isIdentifierChar('(')).toBe(false);
                expect((service as any).isIdentifierChar(')')).toBe(false);
                expect((service as any).isIdentifierChar('=')).toBe(false);
            });
        });

        describe('isPositionInRange', () => {
            test('should return true when position is within range', () => {
                const position = new vscode.Position(1, 5);
                const range = new vscode.Range(
                    new vscode.Position(1, 0),
                    new vscode.Position(1, 10)
                );

                const result = (service as any).isPositionInRange(position, range);

                expect(result).toBe(true);
            });

            test('should return true when position is at range start', () => {
                const position = new vscode.Position(1, 0);
                const range = new vscode.Range(
                    new vscode.Position(1, 0),
                    new vscode.Position(1, 10)
                );

                const result = (service as any).isPositionInRange(position, range);

                expect(result).toBe(true);
            });

            test('should return true when position is at range end', () => {
                const position = new vscode.Position(1, 10);
                const range = new vscode.Range(
                    new vscode.Position(1, 0),
                    new vscode.Position(1, 10)
                );

                const result = (service as any).isPositionInRange(position, range);

                expect(result).toBe(true);
            });

            test('should return false when position is before range', () => {
                const position = new vscode.Position(0, 5);
                const range = new vscode.Range(
                    new vscode.Position(1, 0),
                    new vscode.Position(1, 10)
                );

                const result = (service as any).isPositionInRange(position, range);

                expect(result).toBe(false);
            });

            test('should return false when position is after range', () => {
                const position = new vscode.Position(2, 5);
                const range = new vscode.Range(
                    new vscode.Position(1, 0),
                    new vscode.Position(1, 10)
                );

                const result = (service as any).isPositionInRange(position, range);

                expect(result).toBe(false);
            });

            test('should return false when position character is before range start', () => {
                const position = new vscode.Position(1, 0);
                const range = new vscode.Range(
                    new vscode.Position(1, 5),
                    new vscode.Position(1, 10)
                );

                const result = (service as any).isPositionInRange(position, range);

                expect(result).toBe(false);
            });

            test('should return false when position character is after range end', () => {
                const position = new vscode.Position(1, 15);
                const range = new vscode.Range(
                    new vscode.Position(1, 0),
                    new vscode.Position(1, 10)
                );

                const result = (service as any).isPositionInRange(position, range);

                expect(result).toBe(false);
            });
        });
    });

    describe('findSymbolOccurrences', () => {
        test('should find all occurrences of a symbol', () => {
            const content = 'const test = 1;\nfunction test() {}\ntest();';
            const result = (service as any).findSymbolOccurrences(content, 'test', '/test/file.js');

            expect(result.length).toBe(3);
            expect(result[0].range.start.line).toBe(0);
            expect(result[1].range.start.line).toBe(1);
            expect(result[2].range.start.line).toBe(2);
        });

        test('should not match partial words', () => {
            const content = 'const testing = 1;\nconst test = 2;';
            const result = (service as any).findSymbolOccurrences(content, 'test', '/test/file.js');

            expect(result.length).toBe(1);
            expect(result[0].range.start.character).toBe(6);
        });

        test('should handle empty content', () => {
            const result = (service as any).findSymbolOccurrences('', 'test', '/test/file.js');
            expect(result).toEqual([]);
        });

        test('should handle symbols with special regex characters', () => {
            const content = 'const $test.var = 1;\n$test.var++;';
            const result = (service as any).findSymbolOccurrences(content, '$test.var', '/test/file.js');

            expect(result.length).toBe(2);
        });
    });

    describe('findSymbolDefinitions', () => {
        test('should find JavaScript function definitions', () => {
            const content = 'function myFunc() {}\nconst myFunc2 = () => {};';
            const result = (service as any).findSymbolDefinitions(content, 'myFunc', '/test/file.js', 'javascript');

            expect(result.length).toBe(1);
            expect(result[0].range.start.line).toBe(0);
        });

        test('should find variable definitions', () => {
            const content = 'const myVar = 1;\nlet myVar2 = 2;\nvar myVar3 = 3;';
            const result = (service as any).findSymbolDefinitions(content, 'myVar', '/test/file.js', 'javascript');

            expect(result.length).toBe(1);
        });

        test('should find class definitions', () => {
            const content = 'class MyClass {}\nfunction notAClass() {}';
            const result = (service as any).findSymbolDefinitions(content, 'MyClass', '/test/file.js', 'javascript');

            expect(result.length).toBe(1);
        });

        test('should find method definitions', () => {
            const content = 'class Test {\n  myMethod() {}\n}';
            const result = (service as any).findSymbolDefinitions(content, 'myMethod', '/test/file.js', 'javascript');

            expect(result.length).toBe(1);
        });

        test('should find Python function definitions', () => {
            const content = 'def my_function():\n    pass';
            const result = (service as any).findSymbolDefinitions(content, 'my_function', '/test/file.py', 'python');

            expect(result.length).toBe(1);
        });

        test('should find Python class definitions', () => {
            const content = 'class MyPythonClass:\n    def __init__(self):\n        pass';
            const result = (service as any).findSymbolDefinitions(content, 'MyPythonClass', '/test/file.py', 'python');

            expect(result.length).toBe(1);
        });

        test('should use default patterns for unknown languages', () => {
            const content = 'function unknownFunc() {}\nconst unknownVar = 1;';
            const result = (service as any).findSymbolDefinitions(content, 'unknownFunc', '/test/file.unknown', 'unknown');

            expect(result.length).toBe(1);
        });

        test('should handle empty content', () => {
            const result = (service as any).findSymbolDefinitions('', 'test', '/test/file.js', 'javascript');
            expect(result).toEqual([]);
        });

        test('should handle symbols with special regex characters', () => {
            const content = 'function $special_func$() {}';
            const result = (service as any).findSymbolDefinitions(content, '$special_func$', '/test/file.js', 'javascript');

            expect(result.length).toBe(1);
        });
    });

    describe('getDefinitionPatterns', () => {
        test('should return JavaScript patterns', () => {
            const patterns = (service as any).getDefinitionPatterns('testSymbol', 'javascript');
            expect(patterns.length).toBe(5);
            expect(patterns[0].source).toContain('function');
        });

        test('should return TypeScript patterns', () => {
            const patterns = (service as any).getDefinitionPatterns('testSymbol', 'typescript');
            expect(patterns.length).toBe(5);
            expect(patterns[0].source).toContain('function');
        });

        test('should return Python patterns', () => {
            const patterns = (service as any).getDefinitionPatterns('testSymbol', 'python');
            expect(patterns.length).toBe(2);
            expect(patterns[0].source).toContain('def');
        });

        test('should return default patterns for unknown languages', () => {
            const patterns = (service as any).getDefinitionPatterns('testSymbol', 'unknown');
            expect(patterns.length).toBe(2);
        });

        test('should escape special regex characters in symbol name', () => {
            const patterns = (service as any).getDefinitionPatterns('$test.symbol', 'javascript');
            expect(patterns[0].source).toContain('\\$test\\.symbol');
        });
    });

    describe('convertDocumentSymbol', () => {
        test('should convert symbol with children', () => {
            const mockSymbol = {
                name: 'parentSymbol',
                kind: vscode.SymbolKind.Function,
                selectionRange: mockRange,
                range: mockRange,
                detail: 'function detail',
                children: [
                    {
                        name: 'childSymbol',
                        kind: vscode.SymbolKind.Variable,
                        selectionRange: mockRange,
                        range: mockRange,
                        detail: 'variable detail',
                    }
                ],
            };

            const result = (service as any).convertDocumentSymbol(mockSymbol);

            expect(result.length).toBe(2);
            expect(result[0].name).toBe('parentSymbol');
            expect(result[0].containerName).toBeUndefined();
            expect(result[1].name).toBe('childSymbol');
            expect(result[1].containerName).toBe('parentSymbol');
        });

        test('should convert symbol without children', () => {
            const mockSymbol = {
                name: 'simpleSymbol',
                kind: vscode.SymbolKind.Variable,
                selectionRange: mockRange,
                range: mockRange,
                detail: 'variable detail',
            };

            const result = (service as any).convertDocumentSymbol(mockSymbol);

            expect(result.length).toBe(1);
            expect(result[0].name).toBe('simpleSymbol');
            expect(result[0].containerName).toBeUndefined();
        });

        test('should handle nested children', () => {
            const mockSymbol = {
                name: 'grandparent',
                kind: vscode.SymbolKind.Class,
                selectionRange: mockRange,
                range: mockRange,
                children: [
                    {
                        name: 'parent',
                        kind: vscode.SymbolKind.Method,
                        selectionRange: mockRange,
                        range: mockRange,
                        children: [
                            {
                                name: 'child',
                                kind: vscode.SymbolKind.Variable,
                                selectionRange: mockRange,
                                range: mockRange,
                            }
                        ],
                    }
                ],
            };

            const result = (service as any).convertDocumentSymbol(mockSymbol);

            expect(result.length).toBe(3);
            expect(result[0].name).toBe('grandparent');
            expect(result[1].name).toBe('parent');
            expect(result[1].containerName).toBe('grandparent');
            expect(result[2].name).toBe('child');
            expect(result[2].containerName).toBe('parent');
        });
    });

    describe('Edge Cases and Error Handling', () => {
        test('should handle null/undefined inputs gracefully', async () => {
            expect(await service.getReferences(null as any, mockPosition)).toEqual([]);
            expect(await service.getDefinitions(mockDocument, null as any)).toEqual([]);
            expect(await service.getTypeDefinitions(null as any, null as any)).toEqual([]);
        });

        test('should handle LSP timeout/slow responses', async () => {
            mockExecuteCommand.mockImplementation(() =>
                new Promise(resolve => setTimeout(() => resolve([]), 100))
            );

            const result = await service.getReferences(mockDocument, mockPosition);
            expect(result).toEqual([]);
        });

        test('should handle malformed LSP responses', async () => {
            mockExecuteCommand.mockResolvedValue({ invalid: 'response' });

            const result = await service.getDocumentSymbols(mockDocument);
            expect(result).toEqual([]);
        });

        test('should handle very large files', async () => {
            const largeContent = 'const test = 1;\n'.repeat(10000);
            const position = new vscode.Position(5000, 6);

            const result = await service.getReferencesFromOriginalContent(
                largeContent,
                '/test/large.js',
                position,
                'javascript'
            );

            expect(Array.isArray(result)).toBe(true);
        });

        test('should handle files with special characters', async () => {
            const specialContent = 'const 测试变量 = 1;\nconsole.log(测试变量);';
            const position = new vscode.Position(0, 6);

            const result = await service.getReferencesFromOriginalContent(
                specialContent,
                '/test/special.js',
                position,
                'javascript'
            );

            expect(result.length).toBeGreaterThan(0);
        });
    });

    describe('Resource Management', () => {
        test('should dispose resources properly', () => {
            const mockDispose = jest.fn();
            mockOnDidChangeDiagnostics.mockReturnValue({ dispose: mockDispose });

            const testService = QaxLSPService.getInstance();
            testService.dispose();

            expect(mockDispose).toHaveBeenCalled();
        });

        test('should handle multiple dispose calls', () => {
            const mockDispose = jest.fn();
            mockOnDidChangeDiagnostics.mockReturnValue({ dispose: mockDispose });

            const testService = QaxLSPService.getInstance();
            testService.dispose();
            testService.dispose(); // Should not throw

            expect(mockDispose).toHaveBeenCalledTimes(1);
        });
    });
});
