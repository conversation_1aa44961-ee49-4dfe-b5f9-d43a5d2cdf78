#!/usr/bin/env node

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const util = require('util');

const execAsync = util.promisify(exec);

/**
 * Complete test suite runner for QaxLSPService with 95% coverage target
 */
class CompleteTestSuite {
    constructor() {
        this.projectRoot = __dirname;
        this.requiredDependencies = [
            'jest',
            '@types/jest', 
            'ts-jest',
            'typescript'
        ];
    }

    async run() {
        console.log('🚀 QaxLSPService Complete Test Suite');
        console.log('🎯 Target: 95% Code Coverage');
        console.log('=' .repeat(50) + '\n');

        try {
            // Step 1: Environment check
            await this.checkEnvironment();
            
            // Step 2: Install dependencies
            await this.installDependencies();
            
            // Step 3: Validate test files
            await this.validateTestFiles();
            
            // Step 4: Run tests
            const testResults = await this.runTests();
            
            // Step 5: Analyze coverage
            await this.analyzeCoverage();
            
            // Step 6: Generate report
            this.generateFinalReport(testResults);
            
        } catch (error) {
            console.error('\n❌ Test suite failed:', error.message);
            process.exit(1);
        }
    }

    async checkEnvironment() {
        console.log('🔍 Checking environment...');
        
        // Check Node.js version
        const nodeVersion = process.version;
        console.log(`   Node.js version: ${nodeVersion}`);
        
        if (parseInt(nodeVersion.slice(1)) < 14) {
            throw new Error('Node.js 14+ is required');
        }

        // Check if npm is available
        try {
            await execAsync('npm --version');
            console.log('   ✅ npm is available');
        } catch (error) {
            throw new Error('npm is not available');
        }

        // Check TypeScript files exist
        const requiredFiles = [
            'src/services/autocomplete/qaxNextEdit/services/QaxLSPService.ts',
            'src/services/autocomplete/qaxNextEdit/services/__tests__/QaxLSPService.test.ts'
        ];

        for (const file of requiredFiles) {
            if (!fs.existsSync(file)) {
                throw new Error(`Required file not found: ${file}`);
            }
        }
        
        console.log('   ✅ All required files exist\n');
    }

    async installDependencies() {
        console.log('📦 Installing dependencies...');
        
        // Check if package.json exists
        if (!fs.existsSync('package.json')) {
            console.log('   Creating package.json...');
            const packageJson = {
                name: 'qax-lsp-service-tests',
                version: '1.0.0',
                description: 'Test suite for QaxLSPService',
                scripts: {
                    test: 'jest',
                    'test:coverage': 'jest --coverage',
                    'test:watch': 'jest --watch'
                },
                devDependencies: {}
            };
            fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
        }

        // Install dependencies
        const installCommand = `npm install --save-dev ${this.requiredDependencies.join(' ')}`;
        console.log(`   Running: ${installCommand}`);
        
        try {
            const { stdout, stderr } = await execAsync(installCommand);
            if (stderr && !stderr.includes('WARN')) {
                console.warn('   ⚠️  Installation warnings:', stderr);
            }
            console.log('   ✅ Dependencies installed successfully\n');
        } catch (error) {
            throw new Error(`Failed to install dependencies: ${error.message}`);
        }
    }

    async validateTestFiles() {
        console.log('🔍 Validating test files...');
        
        // Check test file syntax
        const testFile = 'src/services/autocomplete/qaxNextEdit/services/__tests__/QaxLSPService.test.ts';
        const testContent = fs.readFileSync(testFile, 'utf8');
        
        // Count test cases
        const testCases = (testContent.match(/test\(/g) || []).length;
        const describeCases = (testContent.match(/describe\(/g) || []).length;
        
        console.log(`   Test suites: ${describeCases}`);
        console.log(`   Test cases: ${testCases}`);
        
        if (testCases < 50) {
            console.warn('   ⚠️  Low test case count, may not achieve 95% coverage');
        }
        
        // Check for essential test patterns
        const essentialPatterns = [
            'error handling',
            'edge case',
            'null',
            'undefined',
            'empty',
            'async',
            'mock'
        ];
        
        const missingPatterns = essentialPatterns.filter(pattern => 
            !testContent.toLowerCase().includes(pattern.replace(' ', ''))
        );
        
        if (missingPatterns.length > 0) {
            console.warn(`   ⚠️  Missing test patterns: ${missingPatterns.join(', ')}`);
        }
        
        console.log('   ✅ Test files validated\n');
    }

    async runTests() {
        console.log('🧪 Running tests with coverage...');
        
        return new Promise((resolve, reject) => {
            const jestArgs = [
                '--config=jest.config.js',
                '--testPathPatterns=QaxLSPService.test.ts',
                '--coverage',
                '--verbose',
                '--detectOpenHandles',
                '--forceExit',
                '--passWithNoTests=false'
            ];

            const testProcess = spawn('npx', ['jest', ...jestArgs], {
                stdio: 'pipe',
                shell: true,
                cwd: this.projectRoot
            });

            let stdout = '';
            let stderr = '';

            testProcess.stdout.on('data', (data) => {
                const output = data.toString();
                stdout += output;
                process.stdout.write(output);
            });

            testProcess.stderr.on('data', (data) => {
                const output = data.toString();
                stderr += output;
                process.stderr.write(output);
            });

            testProcess.on('close', (code) => {
                const results = {
                    exitCode: code,
                    stdout,
                    stderr,
                    success: code === 0
                };

                if (code === 0) {
                    console.log('\n   ✅ All tests passed!\n');
                    resolve(results);
                } else {
                    console.log('\n   ❌ Some tests failed or coverage below threshold\n');
                    resolve(results); // Don't reject, we want to analyze the results
                }
            });

            testProcess.on('error', (error) => {
                reject(new Error(`Test execution failed: ${error.message}`));
            });
        });
    }

    async analyzeCoverage() {
        console.log('📊 Analyzing coverage...');
        
        // Check if coverage files exist
        const coverageFiles = [
            'coverage/lcov.info',
            'coverage/coverage-final.json'
        ];

        const existingCoverageFiles = coverageFiles.filter(file => fs.existsSync(file));
        
        if (existingCoverageFiles.length === 0) {
            console.warn('   ⚠️  No coverage files found');
            return;
        }

        // Parse coverage data if available
        if (fs.existsSync('coverage/coverage-final.json')) {
            try {
                const coverageData = JSON.parse(fs.readFileSync('coverage/coverage-final.json', 'utf8'));
                const serviceFile = Object.keys(coverageData).find(key => 
                    key.includes('QaxLSPService.ts')
                );

                if (serviceFile) {
                    const coverage = coverageData[serviceFile];
                    console.log('   Coverage Summary:');
                    console.log(`     Lines: ${coverage.s ? Object.values(coverage.s).filter(v => v > 0).length : 'N/A'}`);
                    console.log(`     Functions: ${coverage.f ? Object.values(coverage.f).filter(v => v > 0).length : 'N/A'}`);
                    console.log(`     Branches: ${coverage.b ? Object.values(coverage.b).flat().filter(v => v > 0).length : 'N/A'}`);
                }
            } catch (error) {
                console.warn('   ⚠️  Could not parse coverage data:', error.message);
            }
        }

        console.log('   ✅ Coverage analysis complete\n');
    }

    generateFinalReport(testResults) {
        console.log('📋 Final Test Report');
        console.log('=' .repeat(50));
        
        if (testResults.success) {
            console.log('✅ STATUS: ALL TESTS PASSED');
            console.log('🎯 COVERAGE: TARGET ACHIEVED (95%+)');
            console.log('🚀 RESULT: READY FOR PRODUCTION');
            
            console.log('\n📊 Test Statistics:');
            const testCount = (testResults.stdout.match(/✓/g) || []).length;
            const suiteCount = (testResults.stdout.match(/PASS/g) || []).length;
            console.log(`   Test Suites: ${suiteCount} passed`);
            console.log(`   Tests: ${testCount} passed`);
            
            // Extract coverage percentages from output
            const coverageMatch = testResults.stdout.match(/All files\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)/);
            if (coverageMatch) {
                console.log('\n📈 Coverage Breakdown:');
                console.log(`   Statements: ${coverageMatch[1]}%`);
                console.log(`   Branches: ${coverageMatch[2]}%`);
                console.log(`   Functions: ${coverageMatch[3]}%`);
                console.log(`   Lines: ${coverageMatch[4]}%`);
            }
            
        } else {
            console.log('❌ STATUS: TESTS FAILED OR COVERAGE BELOW TARGET');
            console.log('🔧 ACTION REQUIRED: Review and fix issues');
            
            if (testResults.stderr) {
                console.log('\n🐛 Error Details:');
                console.log(testResults.stderr);
            }
        }

        console.log('\n📁 Generated Files:');
        console.log('   - coverage/lcov-report/index.html (detailed coverage report)');
        console.log('   - coverage/lcov.info (coverage data)');
        console.log('   - Test output above');

        console.log('\n🎉 Test suite execution complete!');
        console.log('=' .repeat(50));
        
        process.exit(testResults.success ? 0 : 1);
    }
}

// Run the complete test suite
if (require.main === module) {
    const testSuite = new CompleteTestSuite();
    testSuite.run().catch(error => {
        console.error('\n💥 Fatal error:', error.message);
        process.exit(1);
    });
}

module.exports = CompleteTestSuite;
