#!/usr/bin/env node

/**
 * 真实的上下文提取测试程序
 * 直接测试编译后的extractIntelligentContext函数
 */

const fs = require("fs")
const path = require("path")

// 检查编译后的文件是否存在
const extensionPath = path.join(__dirname, "dist", "extension.js")

if (!fs.existsSync(extensionPath)) {
	console.log("❌ 编译后的文件不存在，请先运行 npm run compile")
	console.log(`期望路径: ${extensionPath}`)
	process.exit(1)
}

console.log("✅ 找到编译后的扩展文件")
console.log("⚠️  注意：由于esbuild打包的特性，我们将创建一个简化的测试来验证inter-declaration逻辑")

// 测试文件内容
const testFiles = {
	"test1.js": `// File header comment
import { something } from 'module';

/**
 * First function with documentation
 */
function firstFunction() {
    const x = 1;
    return x + 2;
}

// Comment between functions
// Another comment line

/**
 * Second function
 */
function secondFunction(param) {
    if (param > 0) {
        return param * 2;
    }
    return 0;
}

// End of file comment`,

	"test2.ts": `interface User {
    id: number;
    name: string;
}

// Interface and class separator comment

class UserService {
    private users: User[] = [];
    
    addUser(user: User): void {
        this.users.push(user);
    }
}

// Another comment block
// Multiple lines of comments

export function processUsers(users: User[]): number {
    return users.length;
}`,

	"test3.py": `"""Module docstring"""
import os
import sys

def first_function():
    """First function docstring"""
    x = 1
    return x + 2

# Comment between functions
# Multiple comment lines

class MyClass:
    """Class docstring"""
    
    def __init__(self):
        self.value = 0
    
    def method(self, param):
        return param * 2

# End comment`,
}

// 测试用例
const testCases = [
	{
		name: "JS - 光标在函数之间的注释区域",
		fileName: "test1.js",
		cursorLine: 11, // "// Comment between functions"
		cursorChar: 5,
		expectedStrategy: "inter-declaration",
		description: "应该检测到光标在两个函数之间，使用inter-declaration策略",
	},
	{
		name: "JS - 光标在第一个函数内部",
		fileName: "test1.js",
		cursorLine: 7, // "const x = 1;"
		cursorChar: 10,
		expectedStrategy: "meaningful-parent",
		description: "应该检测到光标在函数内部，使用meaningful-parent策略",
	},
	{
		name: "JS - 光标在文件开头",
		fileName: "test1.js",
		cursorLine: 0, // "// File header comment"
		cursorChar: 0,
		expectedStrategy: "inter-declaration",
		description: "应该检测到光标在文件开头，使用inter-declaration策略",
	},
	{
		name: "TS - 光标在接口和类之间",
		fileName: "test2.ts",
		cursorLine: 5, // "// Interface and class separator comment"
		cursorChar: 0,
		expectedStrategy: "inter-declaration",
		description: "应该检测到光标在接口和类之间，使用inter-declaration策略",
	},
	{
		name: "TS - 光标在接口内部",
		fileName: "test2.ts",
		cursorLine: 2, // "name: string;"
		cursorChar: 10,
		expectedStrategy: "meaningful-parent",
		description: "应该检测到光标在接口内部，使用meaningful-parent策略",
	},
]

// 创建临时测试文件
function createTestFiles() {
	const tempDir = path.join(__dirname, "temp-test-files")
	if (!fs.existsSync(tempDir)) {
		fs.mkdirSync(tempDir)
	}

	for (const [fileName, content] of Object.entries(testFiles)) {
		fs.writeFileSync(path.join(tempDir, fileName), content)
	}

	return tempDir
}

// 清理临时文件
function cleanupTestFiles(tempDir) {
	if (fs.existsSync(tempDir)) {
		for (const fileName of Object.keys(testFiles)) {
			const filePath = path.join(tempDir, fileName)
			if (fs.existsSync(filePath)) {
				fs.unlinkSync(filePath)
			}
		}
		fs.rmdirSync(tempDir)
	}
}

// 模拟VS Code的TextDocument和Position
class MockPosition {
	constructor(line, character) {
		this.line = line
		this.character = character
	}
}

class MockTextDocument {
	constructor(filePath, content) {
		this.uri = { fsPath: filePath }
		this.fileName = filePath
		this.languageId = path.extname(filePath).slice(1) || "plaintext"
		this._content = content
		this._lines = content.split("\n")
	}

	get lineCount() {
		return this._lines.length
	}

	lineAt(line) {
		const text = this._lines[line] || ""
		return {
			text,
			lineNumber: line,
		}
	}

	getText() {
		return this._content
	}

	offsetAt(position) {
		let offset = 0
		for (let i = 0; i < position.line && i < this._lines.length; i++) {
			offset += this._lines[i].length + 1 // +1 for newline
		}
		return offset + position.character
	}
}

// 简化的inter-declaration检测逻辑测试
function testInterDeclarationLogic() {
	console.log("🧪 开始inter-declaration逻辑测试")
	console.log("=".repeat(60))

	let passedTests = 0
	let totalTests = testCases.length

	for (let i = 0; i < testCases.length; i++) {
		const testCase = testCases[i]
		console.log(`\n📋 测试 ${i + 1}/${totalTests}: ${testCase.name}`)
		console.log(`📝 描述: ${testCase.description}`)
		console.log(`📍 光标位置: 第${testCase.cursorLine + 1}行, 第${testCase.cursorChar + 1}字符`)

		try {
			const content = testFiles[testCase.fileName]
			const lines = content.split("\n")
			const cursorOffset = lines.slice(0, testCase.cursorLine).join("\n").length + testCase.cursorChar

			// 使用更精确的声明检测
			const declarations = findTopLevelDeclarations(content)

			// 检查光标是否在声明内部
			const insideDeclaration = declarations.find((decl) => {
				return cursorOffset > decl.bodyStart && cursorOffset < decl.end
			})

			let strategy
			if (insideDeclaration) {
				strategy = "meaningful-parent"
			} else {
				// 检查是否在声明之间
				let precedingDeclaration = null
				let followingDeclaration = null

				for (const decl of declarations) {
					if (decl.end <= cursorOffset) {
						precedingDeclaration = decl
					} else if (decl.start > cursorOffset && !followingDeclaration) {
						followingDeclaration = decl
						break
					}
				}

				if (precedingDeclaration || followingDeclaration) {
					strategy = "inter-declaration"
				} else {
					strategy = "context-window"
				}
			}

			console.log(`✅ 检测到策略: ${strategy}`)
			console.log(`📊 找到 ${declarations.length} 个顶级声明`)

			// 检查策略是否匹配预期
			const strategyMatches = strategy === testCase.expectedStrategy
			if (strategyMatches) {
				console.log(`✅ 策略匹配预期: ${testCase.expectedStrategy}`)
				passedTests++
			} else {
				console.log(`❌ 策略不匹配! 预期: ${testCase.expectedStrategy}, 实际: ${strategy}`)

				// 调试信息
				console.log(`🔍 调试信息:`)
				console.log(`   光标偏移: ${cursorOffset}`)
				console.log(`   声明列表:`)
				declarations.forEach((decl, idx) => {
					const isInside = cursorOffset > decl.bodyStart && cursorOffset < decl.end
					const isBefore = decl.start > cursorOffset
					const isAfter = decl.end <= cursorOffset
					console.log(
						`     ${idx + 1}. ${decl.type} (${decl.start}-${decl.end}, body:${decl.bodyStart}) ${isInside ? "[INSIDE]" : ""} ${isBefore ? "[BEFORE]" : ""} ${isAfter ? "[AFTER]" : ""}`,
					)
				})
			}
		} catch (error) {
			console.log(`❌ 测试失败，错误: ${error.message}`)
		}
	}

	console.log(`\n🏁 测试总结`)
	console.log("=".repeat(60))
	console.log(`✅ 通过: ${passedTests}/${totalTests}`)
	console.log(`❌ 失败: ${totalTests - passedTests}/${totalTests}`)
	console.log(`📊 成功率: ${Math.round((passedTests / totalTests) * 100)}%`)

	if (passedTests === totalTests) {
		console.log("🎉 所有测试都通过了!")
		return true
	} else {
		console.log("⚠️  部分测试失败，需要调整逻辑。")
		return false
	}
}

// 更精确的顶级声明查找
function findTopLevelDeclarations(content) {
	const declarations = []

	// JavaScript/TypeScript函数
	const functionRegex = /function\s+\w+\s*\([^)]*\)\s*\{/g
	let match
	while ((match = functionRegex.exec(content)) !== null) {
		const start = match.index
		const bodyStart = start + match[0].length
		const end = findMatchingBrace(content, start)
		declarations.push({
			type: "function",
			start,
			bodyStart,
			end,
		})
	}

	// JavaScript/TypeScript类
	const classRegex = /class\s+\w+[^{]*\{/g
	while ((match = classRegex.exec(content)) !== null) {
		const start = match.index
		const bodyStart = start + match[0].length
		const end = findMatchingBrace(content, start)
		declarations.push({
			type: "class",
			start,
			bodyStart,
			end,
		})
	}

	// TypeScript接口
	const interfaceRegex = /interface\s+\w+[^{]*\{/g
	while ((match = interfaceRegex.exec(content)) !== null) {
		const start = match.index
		const bodyStart = start + match[0].length
		const end = findMatchingBrace(content, start)
		declarations.push({
			type: "interface",
			start,
			bodyStart,
			end,
		})
	}

	// Python函数
	const pythonDefRegex = /def\s+\w+\s*\([^)]*\):/g
	while ((match = pythonDefRegex.exec(content)) !== null) {
		const start = match.index
		const bodyStart = start + match[0].length
		const end = findPythonBlockEnd(content, start)
		declarations.push({
			type: "def",
			start,
			bodyStart,
			end,
		})
	}

	// Python类
	const pythonClassRegex = /class\s+\w+[^:]*:/g
	while ((match = pythonClassRegex.exec(content)) !== null) {
		const start = match.index
		const bodyStart = start + match[0].length
		const end = findPythonBlockEnd(content, start)
		declarations.push({
			type: "class",
			start,
			bodyStart,
			end,
		})
	}

	return declarations.sort((a, b) => a.start - b.start)
}

// 查找匹配的大括号
function findMatchingBrace(content, start) {
	const openBrace = content.indexOf("{", start)
	if (openBrace === -1) return content.length

	let braceCount = 1
	let pos = openBrace + 1

	while (pos < content.length && braceCount > 0) {
		if (content[pos] === "{") {
			braceCount++
		} else if (content[pos] === "}") {
			braceCount--
		}
		pos++
	}

	return pos
}

// 查找Python块结束位置
function findPythonBlockEnd(content, start) {
	const lines = content.split("\n")
	const startLine = content.slice(0, start).split("\n").length - 1

	// 找到定义行的缩进级别
	const defLine = lines[startLine]
	const baseIndent = defLine.match(/^(\s*)/)[1].length

	// 查找下一个同级或更高级别的声明
	for (let i = startLine + 1; i < lines.length; i++) {
		const line = lines[i].trim()
		if (line === "") continue // 跳过空行

		const currentIndent = lines[i].match(/^(\s*)/)[1].length
		if (currentIndent <= baseIndent && line.match(/^(def|class)\s+/)) {
			return lines.slice(0, i).join("\n").length
		}
	}

	return content.length
}

// 执行测试
if (require.main === module) {
	const success = testInterDeclarationLogic()
	process.exit(success ? 0 : 1)
}

module.exports = { testInterDeclarationLogic }
