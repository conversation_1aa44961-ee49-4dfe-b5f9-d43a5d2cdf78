import { ClineMessage } from "@shared/ExtensionMessage"
import { TodoItem } from "@/components/chat/IntegratedTodoList"

/**
 * Parse markdown checklist to TodoItem array
 * @param md Markdown checklist string
 * @returns Array of TodoItem objects
 */
function parseMarkdownChecklist(md: string): TodoItem[] {
	if (typeof md !== "string") return []
	const lines = md
		.split(/\r?\n/)
		.map((l) => l.trim())
		.filter(Boolean)
	const todos: TodoItem[] = []
	for (const line of lines) {
		const match = line.match(/^\[\s*([ xX\-~])\s*\]\s+(.+)$/)
		if (!match) continue
		let status: "pending" | "in_progress" | "completed" = "pending"
		if (match[1] === "x" || match[1] === "X") status = "completed"
		else if (match[1] === "-" || match[1] === "~") status = "in_progress"
		// ID 只基于任务内容生成，不包含状态，确保状态变化时 ID 不变
		const id = generateId(match[2])
		todos.push({
			id,
			content: match[2],
			status,
		})
	}
	return todos
}

/**
 * Generate a simple ID for todo items
 * @param input Input string to generate ID from
 * @returns Generated ID string
 */
function generateId(input: string): string {
	// Simple hash function for browser environment
	let hash = 0
	for (let i = 0; i < input.length; i++) {
		const char = input.charCodeAt(i)
		hash = (hash << 5) - hash + char
		hash = hash & hash // Convert to 32bit integer
	}
	return Math.abs(hash).toString(16)
}

/**
 * Extract the latest todo list from ClineMessage history
 * @param clineMessages Array of ClineMessage objects
 * @returns Array of TodoItem objects or empty array if none found
 */
export function getLatestTodo(clineMessages: ClineMessage[]): TodoItem[] {
	console.log("[todolist] getLatestTodo called with", clineMessages.length, "messages")

	// 只处理完整的消息，排除 partial 消息以避免处理不完整的数据
	// 现在后端会在工具执行完成后发送一个 partial=false 的完整消息
	const relevantMessages = clineMessages.filter(
		(msg) =>
			((msg.type === "ask" && msg.ask === "tool") ||
				(msg.type === "say" && msg.say === "tool") ||
				(msg.type === "say" && msg.say === "user_edit_todos")) &&
			!msg.partial, // 只处理完整消息
	)
	console.log("[todolist] Found", relevantMessages.length, "complete relevant messages")

	const parsedMessages = relevantMessages.map((msg) => {
		try {
			const parsed = JSON.parse(msg.text ?? "{}")
			console.log("[todolist] Parsed complete message:", {
				type: msg.type,
				ask: msg.ask,
				say: msg.say,
				tool: parsed.tool,
				todosType: typeof parsed.todos,
				isArray: Array.isArray(parsed.todos),
			})
			return parsed
		} catch (error) {
			console.log("[todolist] Failed to parse message:", msg.text?.substring(0, 100))
			return null
		}
	})

	const todoMessages = parsedMessages.filter((item) => {
		if (!item || item.tool !== "updateTodoList") return false
		// Support both array format (new) and string format (legacy)
		const isValid = Array.isArray(item.todos) || typeof item.todos === "string"
		console.log("[todolist] Todo message validation:", {
			tool: item.tool,
			todosType: typeof item.todos,
			isArray: Array.isArray(item.todos),
			isValid,
		})
		return isValid
	})
	console.log("[todolist] Found", todoMessages.length, "valid todo messages")

	const todos = todoMessages
		.map((item) => {
			// Convert string format to array format for consistency
			if (typeof item.todos === "string") {
				console.log("[todolist] Converting string format to array:", item.todos)
				return parseMarkdownChecklist(item.todos)
			}
			console.log("[todolist] Using array format:", item.todos)
			return item.todos
		})
		.pop()

	console.log("[todolist] Final result:", todos)
	if (todos && Array.isArray(todos) && todos.length > 0) {
		return todos
	} else {
		return []
	}
}
