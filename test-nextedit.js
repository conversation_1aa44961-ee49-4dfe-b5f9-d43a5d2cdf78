// Test file for NextEdit functionality
class Calendar {
	constructor() {
		this.events = []
	}

	showEventDetail(event) {
		console.log("Showing event detail:", event)
		this.displayEventInfo(event)
		this.highlightEvent(event)
	}

	displayEventInfo(event) {
		console.log("Event info:", event.title, event.date)
	}

	highlightEvent(event) {
		console.log("Highlighting event:", event.id)
	}

	addEvent(event) {
		this.events.push(event)
		this.showEventDetail(event)
	}

	removeEvent(eventId) {
		this.events = this.events.filter((e) => e.id !== eventId)
	}
}

// Usage
const calendar = new Calendar()
calendar.addEvent({ id: 1, title: "Meeting", date: "2024-01-15" })
calendar.showEventDetail({ id: 2, title: "Conference", date: "2024-01-20" })
