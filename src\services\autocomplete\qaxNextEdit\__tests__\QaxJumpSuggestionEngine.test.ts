import * as assert from "assert"
import * as vscode from "vscode"
import { QaxJumpSuggestionEngine } from "../services/QaxJumpSuggestionEngine"
import { QaxChangeDetection, QaxChangeType, QaxAnalysisContext, DEFAULT_QAX_NEXT_EDIT_CONFIG } from "../types/QaxNextEditTypes"

// Mock LSP Service
const mockLSPService = {
	getReferences: async () => [] as vscode.Location[],
	getDefinitions: async () => [] as vscode.Location[],
}

// Mock VS Code API
const mockVscode = {
	workspace: {
		findTextInFiles: async () => new Map(),
	},
	Uri: {
		file: (path: string) => ({ fsPath: path, scheme: "file" }),
	},
	Range: vscode.Range,
	Position: vscode.Position,
	Location: vscode.Location,
}

Object.assign(vscode, mockVscode)

// Mock LSP service will be set up in beforeEach

describe("QaxJumpSuggestionEngine", () => {
	let engine: QaxJumpSuggestionEngine

	beforeEach(() => {
		engine = new QaxJumpSuggestionEngine(DEFAULT_QAX_NEXT_EDIT_CONFIG)
	})

	describe("Initialization", () => {
		it("should initialize with config", () => {
			const customConfig = {
				...DEFAULT_QAX_NEXT_EDIT_CONFIG,
				maxSuggestions: 5,
			}
			const customEngine = new QaxJumpSuggestionEngine(customConfig)
			assert.ok(customEngine)
		})

		it("should update config", () => {
			const newConfig = {
				...DEFAULT_QAX_NEXT_EDIT_CONFIG,
				maxSuggestions: 10,
			}
			engine.updateConfig(newConfig)
			// Config should be updated (no direct way to test without exposing internal state)
		})
	})

	describe("Jump Suggestion Generation", () => {
		it("should return empty array for empty changes", async () => {
			const context: QaxAnalysisContext = {
				filePath: "test.ts",
				document: {} as vscode.TextDocument,
				changes: [],
				beforeContent: "",
				afterContent: "",
				languageId: "typescript",
			}

			const suggestions = await engine.generateJumpSuggestions([], context)
			assert.strictEqual(suggestions.length, 0)
		})

		it("should handle unsupported change types gracefully", async () => {
			const changes: QaxChangeDetection[] = [
				{
					type: "unsupported_type" as QaxChangeType,
					filePath: "test.ts",
					range: new vscode.Range(0, 0, 0, 10),
					oldValue: "test",
					confidence: 0.9,
				},
			]

			const context: QaxAnalysisContext = {
				filePath: "test.ts",
				document: {} as vscode.TextDocument,
				changes: [],
				beforeContent: "",
				afterContent: "",
				languageId: "typescript",
			}

			const suggestions = await engine.generateJumpSuggestions(changes, context)
			assert.strictEqual(suggestions.length, 0)
		})

		it("should limit suggestions to maxSuggestions", async () => {
			const config = {
				...DEFAULT_QAX_NEXT_EDIT_CONFIG,
				maxSuggestions: 2,
			}
			const limitedEngine = new QaxJumpSuggestionEngine(config)

			// Mock many references
			const manyReferences = Array.from(
				{ length: 5 },
				(_, i) => new vscode.Location(vscode.Uri.file("test.ts"), new vscode.Range(i, 0, i, 10)),
			)

			mockLSPService.getReferences = async () => manyReferences

			const changes: QaxChangeDetection[] = [
				{
					type: QaxChangeType.VARIABLE_RENAME,
					filePath: "test.ts",
					range: new vscode.Range(0, 0, 0, 10),
					oldValue: "oldName",
					newValue: "newName",
					confidence: 0.9,
					metadata: {
						symbolName: "oldName",
						references: manyReferences,
					},
				},
			]

			const context: QaxAnalysisContext = {
				filePath: "test.ts",
				document: {} as vscode.TextDocument,
				changes: [],
				beforeContent: "",
				afterContent: "",
				languageId: "typescript",
			}

			const suggestions = await limitedEngine.generateJumpSuggestions(changes, context)
			assert.ok(suggestions.length <= 2) // Should be limited to maxSuggestions

			// Restore mock
			mockLSPService.getReferences = async () => []
		})
	})

	describe("Variable Rename Suggestions", () => {
		it("should generate suggestions for variable rename", async () => {
			const references = [
				new vscode.Location(vscode.Uri.file("test.ts"), new vscode.Range(1, 0, 1, 7)),
				new vscode.Location(vscode.Uri.file("other.ts"), new vscode.Range(2, 5, 2, 12)),
			]

			mockLSPService.getReferences = async () => references

			const changes: QaxChangeDetection[] = [
				{
					type: QaxChangeType.VARIABLE_RENAME,
					filePath: "test.ts",
					range: new vscode.Range(0, 0, 0, 7),
					oldValue: "oldName",
					newValue: "newName",
					confidence: 0.9,
					metadata: {
						symbolName: "oldName",
						references,
					},
				},
			]

			const context: QaxAnalysisContext = {
				filePath: "test.ts",
				document: {} as vscode.TextDocument,
				changes: [],
				beforeContent: "",
				afterContent: "",
				languageId: "typescript",
			}

			const suggestions = await engine.generateJumpSuggestions(changes, context)
			assert.strictEqual(suggestions.length, 2) // Should exclude the original change location

			// Check first suggestion
			const firstSuggestion = suggestions[0]
			assert.strictEqual(firstSuggestion.changeType, QaxChangeType.VARIABLE_RENAME)
			assert.strictEqual(firstSuggestion.description, "Update variable name from 'oldName' to 'newName'")
			assert.ok(firstSuggestion.suggestedEdit)
			assert.strictEqual(firstSuggestion.suggestedEdit.newText, "newName")

			// Restore mock
			mockLSPService.getReferences = async () => []
		})

		it("should skip original change location", async () => {
			const originalRange = new vscode.Range(0, 0, 0, 7)
			const references = [
				new vscode.Location(vscode.Uri.file("test.ts"), originalRange), // Same as original
				new vscode.Location(vscode.Uri.file("test.ts"), new vscode.Range(1, 0, 1, 7)),
			]

			mockLSPService.getReferences = async () => references

			const changes: QaxChangeDetection[] = [
				{
					type: QaxChangeType.VARIABLE_RENAME,
					filePath: "test.ts",
					range: originalRange,
					oldValue: "oldName",
					newValue: "newName",
					confidence: 0.9,
					metadata: {
						symbolName: "oldName",
						references,
					},
				},
			]

			const context: QaxAnalysisContext = {
				filePath: "test.ts",
				document: {} as vscode.TextDocument,
				changes: [],
				beforeContent: "",
				afterContent: "",
				languageId: "typescript",
			}

			const suggestions = await engine.generateJumpSuggestions(changes, context)
			assert.strictEqual(suggestions.length, 1) // Should exclude the original location

			// Restore mock
			mockLSPService.getReferences = async () => []
		})

		it("should handle missing metadata gracefully", async () => {
			const changes: QaxChangeDetection[] = [
				{
					type: QaxChangeType.VARIABLE_RENAME,
					filePath: "test.ts",
					range: new vscode.Range(0, 0, 0, 7),
					oldValue: "oldName",
					newValue: "newName",
					confidence: 0.9,
					// No metadata
				},
			]

			const context: QaxAnalysisContext = {
				filePath: "test.ts",
				document: {} as vscode.TextDocument,
				changes: [],
				beforeContent: "",
				afterContent: "",
				languageId: "typescript",
			}

			const suggestions = await engine.generateJumpSuggestions(changes, context)
			assert.strictEqual(suggestions.length, 0) // Should handle gracefully
		})
	})

	describe("Function Parameter Change Suggestions", () => {
		it("should generate suggestions for parameter changes", async () => {
			// Mock workspace search to find function calls
			const mockSearchResults = new Map([[vscode.Uri.file("caller.ts"), [{ range: new vscode.Range(5, 0, 5, 15) }]]])
			mockVscode.workspace.findTextInFiles = async () => mockSearchResults

			const changes: QaxChangeDetection[] = [
				{
					type: QaxChangeType.FUNCTION_PARAMETER_CHANGE,
					filePath: "test.ts",
					range: new vscode.Range(0, 0, 0, 20),
					oldValue: "function test(a)",
					newValue: "function test(a, b)",
					confidence: 0.9,
					metadata: {
						symbolName: "test",
					},
				},
			]

			const context: QaxAnalysisContext = {
				filePath: "test.ts",
				document: {} as vscode.TextDocument,
				changes: [],
				beforeContent: "",
				afterContent: "",
				languageId: "typescript",
			}

			const suggestions = await engine.generateJumpSuggestions(changes, context)
			assert.ok(suggestions.length > 0)

			const suggestion = suggestions[0]
			assert.strictEqual(suggestion.changeType, QaxChangeType.FUNCTION_PARAMETER_CHANGE)
			assert.ok(suggestion.description.includes("Update function call 'test'"))

			// Restore mock
			mockVscode.workspace.findTextInFiles = async () => new Map()
		})
	})

	describe("Function Call Deletion Suggestions", () => {
		it("should generate suggestions for call deletions", async () => {
			// Mock workspace search to find related function calls
			const mockSearchResults = new Map([[vscode.Uri.file("other.ts"), [{ range: new vscode.Range(3, 0, 3, 10) }]]])
			mockVscode.workspace.findTextInFiles = async () => mockSearchResults

			const changes: QaxChangeDetection[] = [
				{
					type: QaxChangeType.FUNCTION_CALL_DELETION,
					filePath: "test.ts",
					range: new vscode.Range(0, 0, 0, 7),
					oldValue: "test()",
					confidence: 0.8,
					metadata: {
						symbolName: "test",
					},
				},
			]

			const context: QaxAnalysisContext = {
				filePath: "test.ts",
				document: {} as vscode.TextDocument,
				changes: [],
				beforeContent: "",
				afterContent: "",
				languageId: "typescript",
			}

			const suggestions = await engine.generateJumpSuggestions(changes, context)
			assert.ok(suggestions.length > 0)

			const suggestion = suggestions[0]
			assert.strictEqual(suggestion.changeType, QaxChangeType.FUNCTION_CALL_DELETION)
			assert.ok(suggestion.description.includes("Consider removing this call"))

			// Restore mock
			mockVscode.workspace.findTextInFiles = async () => new Map()
		})
	})

	describe("Variable Deletion Suggestions", () => {
		it("should generate suggestions for variable deletions", async () => {
			const references = [new vscode.Location(vscode.Uri.file("usage.ts"), new vscode.Range(2, 0, 2, 10))]

			const changes: QaxChangeDetection[] = [
				{
					type: QaxChangeType.VARIABLE_DELETION,
					filePath: "test.ts",
					range: new vscode.Range(0, 0, 0, 15),
					oldValue: "let deletedVar",
					confidence: 0.9,
					metadata: {
						symbolName: "deletedVar",
						references,
					},
				},
			]

			const context: QaxAnalysisContext = {
				filePath: "test.ts",
				document: {} as vscode.TextDocument,
				changes: [],
				beforeContent: "",
				afterContent: "",
				languageId: "typescript",
			}

			const suggestions = await engine.generateJumpSuggestions(changes, context)
			assert.ok(suggestions.length > 0)

			const suggestion = suggestions[0]
			assert.strictEqual(suggestion.changeType, QaxChangeType.VARIABLE_DELETION)
			assert.ok(suggestion.description.includes("Remove usage of deleted variable"))
		})
	})

	describe("Priority Calculation", () => {
		it("should assign higher priority to same-file suggestions", async () => {
			const sameFileRef = new vscode.Location(vscode.Uri.file("test.ts"), new vscode.Range(1, 0, 1, 7))
			const otherFileRef = new vscode.Location(vscode.Uri.file("other.ts"), new vscode.Range(1, 0, 1, 7))

			mockLSPService.getReferences = async () => [sameFileRef, otherFileRef]

			const changes: QaxChangeDetection[] = [
				{
					type: QaxChangeType.VARIABLE_RENAME,
					filePath: "test.ts",
					range: new vscode.Range(0, 0, 0, 7),
					oldValue: "oldName",
					newValue: "newName",
					confidence: 0.9,
					metadata: {
						symbolName: "oldName",
						references: [sameFileRef, otherFileRef],
					},
				},
			]

			const context: QaxAnalysisContext = {
				filePath: "test.ts",
				document: {} as vscode.TextDocument,
				changes: [],
				beforeContent: "",
				afterContent: "",
				languageId: "typescript",
			}

			const suggestions = await engine.generateJumpSuggestions(changes, context)
			assert.strictEqual(suggestions.length, 2)

			// Same file suggestion should have higher priority
			const sameFileSuggestion = suggestions.find((s) => s.filePath === "test.ts")
			const otherFileSuggestion = suggestions.find((s) => s.filePath === "other.ts")

			assert.ok(sameFileSuggestion)
			assert.ok(otherFileSuggestion)
			assert.ok(sameFileSuggestion.priority > otherFileSuggestion.priority)

			// Restore mock
			mockLSPService.getReferences = async () => []
		})

		it("should sort suggestions by priority", async () => {
			const references = [
				new vscode.Location(vscode.Uri.file("test.ts"), new vscode.Range(1, 0, 1, 7)), // Same file, higher priority
				new vscode.Location(vscode.Uri.file("other.ts"), new vscode.Range(1, 0, 1, 7)), // Other file, lower priority
			]

			mockLSPService.getReferences = async () => references

			const changes: QaxChangeDetection[] = [
				{
					type: QaxChangeType.VARIABLE_RENAME,
					filePath: "test.ts",
					range: new vscode.Range(0, 0, 0, 7),
					oldValue: "oldName",
					newValue: "newName",
					confidence: 0.9,
					metadata: {
						symbolName: "oldName",
						references,
					},
				},
			]

			const context: QaxAnalysisContext = {
				filePath: "test.ts",
				document: {} as vscode.TextDocument,
				changes: [],
				beforeContent: "",
				afterContent: "",
				languageId: "typescript",
			}

			const suggestions = await engine.generateJumpSuggestions(changes, context)
			assert.strictEqual(suggestions.length, 2)

			// Should be sorted by priority (highest first)
			assert.ok(suggestions[0].priority >= suggestions[1].priority)

			// Restore mock
			mockLSPService.getReferences = async () => []
		})
	})
})
