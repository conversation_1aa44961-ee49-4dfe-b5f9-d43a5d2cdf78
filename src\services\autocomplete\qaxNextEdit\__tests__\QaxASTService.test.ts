import * as assert from "assert"
import * as vscode from "vscode"
import { QaxASTService } from "../services/QaxASTService"
import { QaxChangeType } from "../types/QaxNextEditTypes"

// Mock Tree-sitter parser
const mockParser = {
	parse: (content: string) => ({
		rootNode: createMockNode("program", content, 0, 0, content.split("\n").length - 1, content.length),
	}),
}

// Mock getParserForFile function - will be set up in beforeEach

function createMockNode(
	type: string,
	text: string,
	startRow: number,
	startCol: number,
	endRow: number,
	endCol: number,
	children: any[] = [],
): any {
	return {
		type,
		text,
		startPosition: { row: startRow, column: startCol },
		endPosition: { row: endRow, column: endCol },
		childCount: children.length,
		namedChildCount: children.length,
		child: (index: number) => children[index],
		children,
	}
}

// Mock VS Code API
const mockVscode = {
	workspace: {
		openTextDocument: async (options: any) => ({
			uri: { fsPath: "mock.ts" },
			languageId: "typescript",
			getText: (range?: vscode.Range) => {
				if (options.content) return options.content
				return "mock content"
			},
		}),
	},
	Position: vscode.Position,
	Range: vscode.Range,
}

Object.assign(vscode, mockVscode)

describe("QaxASTService", () => {
	let service: QaxASTService

	beforeEach(() => {
		QaxASTService.dispose()
		service = QaxASTService.getInstance()
	})

	afterEach(() => {
		QaxASTService.dispose()
	})

	describe("Service Initialization", () => {
		it("should create a singleton instance", () => {
			const instance1 = QaxASTService.getInstance()
			const instance2 = QaxASTService.getInstance()
			assert.strictEqual(instance1, instance2)
		})

		it("should dispose cleanly", () => {
			QaxASTService.dispose()
			QaxASTService.dispose()
			// 应该能够重新创建实例
			const newService = QaxASTService.getInstance()
			assert.ok(newService)
			QaxASTService.dispose()
		})
	})

	describe("Document Parsing", () => {
		it("should parse TypeScript document successfully", async () => {
			const mockDocument = {
				uri: { fsPath: "test.ts" },
				getText: () => "function test() { return 42; }",
			} as vscode.TextDocument

			const ast = await service.parseDocument(mockDocument)
			assert.ok(ast)
			assert.strictEqual(ast.type, "program")
		})

		it("should return null for unsupported file types", async () => {
			const mockDocument = {
				uri: { fsPath: "test.txt" },
				getText: () => "plain text",
			} as vscode.TextDocument

			const ast = await service.parseDocument(mockDocument)
			assert.strictEqual(ast, null)
		})

		it("should handle parsing errors gracefully", async () => {
			// Mock parser to throw error
			const originalParse = mockParser.parse
			mockParser.parse = () => {
				throw new Error("Parse error")
			}

			const mockDocument = {
				uri: { fsPath: "test.ts" },
				getText: () => "invalid syntax",
			} as vscode.TextDocument

			const ast = await service.parseDocument(mockDocument)
			assert.strictEqual(ast, null)

			// Restore original parser
			mockParser.parse = originalParse
		})
	})

	describe("Node Conversion", () => {
		it("should convert tree-sitter node to QaxASTNode", async () => {
			const mockDocument = {
				uri: { fsPath: "test.ts" },
				getText: (range?: vscode.Range) => {
					if (!range) return "function test() {}"
					return "test"
				},
			} as vscode.TextDocument

			// Create a more complex mock node structure
			const childNode = createMockNode("identifier", "test", 0, 9, 0, 13)
			const rootNode = createMockNode("function_declaration", "function test() {}", 0, 0, 0, 18, [childNode])

			mockParser.parse = () => ({ rootNode })

			const ast = await service.parseDocument(mockDocument)
			assert.ok(ast)
			assert.strictEqual(ast.type, "function_declaration")
			assert.ok(ast.children)
			assert.strictEqual(ast.children.length, 1)
			assert.strictEqual(ast.children[0].type, "identifier")
			assert.strictEqual(ast.children[0].parent, ast)
		})
	})

	describe("Variable Declaration Finding", () => {
		it("should find variable declarations", () => {
			const mockAST = {
				type: "program",
				text: "let x = 5; const y = 10;",
				range: new vscode.Range(0, 0, 0, 24),
				children: [
					{
						type: "variable_declarator",
						text: "x = 5",
						range: new vscode.Range(0, 4, 0, 9),
						children: [],
					},
					{
						type: "const_declaration",
						text: "y = 10",
						range: new vscode.Range(0, 15, 0, 21),
						children: [],
					},
				],
			}

			const declarations = service.findVariableDeclarations(mockAST)
			assert.strictEqual(declarations.length, 2)
			assert.strictEqual(declarations[0].type, "variable_declarator")
			assert.strictEqual(declarations[1].type, "const_declaration")
		})

		it("should return empty array when no declarations found", () => {
			const mockAST = {
				type: "program",
				text: "console.log('hello');",
				range: new vscode.Range(0, 0, 0, 21),
				children: [],
			}

			const declarations = service.findVariableDeclarations(mockAST)
			assert.strictEqual(declarations.length, 0)
		})
	})

	describe("Function Declaration Finding", () => {
		it("should find function declarations", () => {
			const mockAST = {
				type: "program",
				text: "function test() {} const arrow = () => {}",
				range: new vscode.Range(0, 0, 0, 41),
				children: [
					{
						type: "function_declaration",
						text: "function test() {}",
						range: new vscode.Range(0, 0, 0, 18),
						children: [],
					},
					{
						type: "arrow_function",
						text: "() => {}",
						range: new vscode.Range(0, 25, 0, 33),
						children: [],
					},
				],
			}

			const functions = service.findFunctionDeclarations(mockAST)
			assert.strictEqual(functions.length, 2)
			assert.strictEqual(functions[0].type, "function_declaration")
			assert.strictEqual(functions[1].type, "arrow_function")
		})
	})

	describe("Function Parameter Finding", () => {
		it("should find function parameters", () => {
			const mockFunctionAST = {
				type: "function_declaration",
				text: "function test(a, b) {}",
				range: new vscode.Range(0, 0, 0, 22),
				children: [
					{
						type: "formal_parameters",
						text: "(a, b)",
						range: new vscode.Range(0, 13, 0, 19),
						children: [
							{
								type: "identifier",
								text: "a",
								range: new vscode.Range(0, 14, 0, 15),
								children: [],
							},
							{
								type: "identifier",
								text: "b",
								range: new vscode.Range(0, 17, 0, 18),
								children: [],
							},
						],
					},
				],
			}

			const parameters = service.findFunctionParameters(mockFunctionAST)
			assert.strictEqual(parameters.length, 2)
			assert.strictEqual(parameters[0].text, "a")
			assert.strictEqual(parameters[1].text, "b")
		})

		it("should return empty array for functions without parameters", () => {
			const mockFunctionAST = {
				type: "function_declaration",
				text: "function test() {}",
				range: new vscode.Range(0, 0, 0, 18),
				children: [],
			}

			const parameters = service.findFunctionParameters(mockFunctionAST)
			assert.strictEqual(parameters.length, 0)
		})
	})

	describe("Function Call Finding", () => {
		it("should find function calls", () => {
			const mockAST = {
				type: "program",
				text: "test(); obj.method();",
				range: new vscode.Range(0, 0, 0, 21),
				children: [
					{
						type: "call_expression",
						text: "test()",
						range: new vscode.Range(0, 0, 0, 6),
						children: [],
					},
					{
						type: "call_expression",
						text: "obj.method()",
						range: new vscode.Range(0, 8, 0, 20),
						children: [],
					},
				],
			}

			const calls = service.findFunctionCalls(mockAST)
			assert.strictEqual(calls.length, 2)
			assert.strictEqual(calls[0].text, "test()")
			assert.strictEqual(calls[1].text, "obj.method()")
		})
	})

	describe("Variable Rename Detection", () => {
		it("should detect variable rename", () => {
			const oldAST = {
				type: "program",
				text: "let oldName = 5;",
				range: new vscode.Range(0, 0, 0, 16),
				children: [
					{
						type: "variable_declarator",
						text: "oldName = 5",
						range: new vscode.Range(0, 4, 0, 15),
						children: [
							{
								type: "identifier",
								text: "oldName",
								range: new vscode.Range(0, 4, 0, 11),
								children: [],
							},
						],
					},
				],
			}

			const newAST = {
				type: "program",
				text: "let newName = 5;",
				range: new vscode.Range(0, 0, 0, 16),
				children: [
					{
						type: "variable_declarator",
						text: "newName = 5",
						range: new vscode.Range(0, 4, 0, 15),
						children: [
							{
								type: "identifier",
								text: "newName",
								range: new vscode.Range(0, 4, 0, 11),
								children: [],
							},
						],
					},
				],
			}

			const changes = service.detectVariableRename(oldAST, newAST)
			assert.strictEqual(changes.length, 1)
			assert.strictEqual(changes[0].type, QaxChangeType.VARIABLE_RENAME)
			assert.strictEqual(changes[0].oldValue, "oldName")
			assert.strictEqual(changes[0].newValue, "newName")
		})

		it("should return empty array when no renames detected", () => {
			const ast = {
				type: "program",
				text: "let name = 5;",
				range: new vscode.Range(0, 0, 0, 13),
				children: [],
			}

			const changes = service.detectVariableRename(ast, ast)
			assert.strictEqual(changes.length, 0)
		})
	})

	describe("Function Parameter Change Detection", () => {
		it("should detect parameter count change", () => {
			const oldAST = {
				type: "program",
				text: "function test(a) {}",
				range: new vscode.Range(0, 0, 0, 19),
				children: [
					{
						type: "function_declaration",
						text: "function test(a) {}",
						range: new vscode.Range(0, 0, 0, 19),
						children: [
							{
								type: "identifier",
								text: "test",
								range: new vscode.Range(0, 9, 0, 13),
								children: [],
							},
							{
								type: "formal_parameters",
								text: "(a)",
								range: new vscode.Range(0, 13, 0, 16),
								children: [
									{
										type: "identifier",
										text: "a",
										range: new vscode.Range(0, 14, 0, 15),
										children: [],
									},
								],
							},
						],
					},
				],
			}

			const newAST = {
				type: "program",
				text: "function test(a, b) {}",
				range: new vscode.Range(0, 0, 0, 22),
				children: [
					{
						type: "function_declaration",
						text: "function test(a, b) {}",
						range: new vscode.Range(0, 0, 0, 22),
						children: [
							{
								type: "identifier",
								text: "test",
								range: new vscode.Range(0, 9, 0, 13),
								children: [],
							},
							{
								type: "formal_parameters",
								text: "(a, b)",
								range: new vscode.Range(0, 13, 0, 19),
								children: [
									{
										type: "identifier",
										text: "a",
										range: new vscode.Range(0, 14, 0, 15),
										children: [],
									},
									{
										type: "identifier",
										text: "b",
										range: new vscode.Range(0, 17, 0, 18),
										children: [],
									},
								],
							},
						],
					},
				],
			}

			const changes = service.detectFunctionParameterChanges(oldAST, newAST)
			assert.strictEqual(changes.length, 1)
			assert.strictEqual(changes[0].type, QaxChangeType.FUNCTION_PARAMETER_CHANGE)
			assert.strictEqual(changes[0].metadata?.oldParamCount, 1)
			assert.strictEqual(changes[0].metadata?.newParamCount, 2)
		})
	})

	describe("Function Call Deletion Detection", () => {
		it("should detect deleted function calls", () => {
			const oldAST = {
				type: "program",
				text: "test(); other();",
				range: new vscode.Range(0, 0, 0, 16),
				children: [
					{
						type: "call_expression",
						text: "test()",
						range: new vscode.Range(0, 0, 0, 6),
						children: [
							{
								type: "identifier",
								text: "test",
								range: new vscode.Range(0, 0, 0, 4),
								children: [],
							},
						],
					},
					{
						type: "call_expression",
						text: "other()",
						range: new vscode.Range(0, 8, 0, 15),
						children: [
							{
								type: "identifier",
								text: "other",
								range: new vscode.Range(0, 8, 0, 13),
								children: [],
							},
						],
					},
				],
			}

			const newAST = {
				type: "program",
				text: "other();",
				range: new vscode.Range(0, 0, 0, 8),
				children: [
					{
						type: "call_expression",
						text: "other()",
						range: new vscode.Range(0, 0, 0, 7),
						children: [
							{
								type: "identifier",
								text: "other",
								range: new vscode.Range(0, 0, 0, 5),
								children: [],
							},
						],
					},
				],
			}

			const changes = service.detectFunctionCallDeletions(oldAST, newAST)
			assert.strictEqual(changes.length, 1)
			assert.strictEqual(changes[0].type, QaxChangeType.FUNCTION_CALL_DELETION)
			assert.strictEqual(changes[0].metadata?.symbolName, "test")
		})
	})
})
