import * as vscode from 'vscode';
import { QaxASTService } from '../QaxASTService';
import { QaxChangeType, QaxASTNode } from '../../types/QaxNextEditTypes';

// Mock tree-sitter dependencies
jest.mock('../../../tree-sitter/languageParser', () => ({
    getParserForFile: jest.fn(),
    getAst: jest.fn(),
}));

const mockGetParserForFile = require('../../../tree-sitter/languageParser').getParserForFile;
const mockGetAst = require('../../../tree-sitter/languageParser').getAst;

describe('QaxASTService', () => {
    let service: QaxASTService;
    let mockDocument: vscode.TextDocument;

    beforeEach(() => {
        jest.clearAllMocks();
        
        // Reset singleton instance
        (QaxASTService as any).instance = null;
        
        // Create mock document
        mockDocument = {
            uri: vscode.Uri.file('/test/file.js'),
            fileName: '/test/file.js',
            languageId: 'javascript',
            version: 1,
            getText: jest.fn().mockReturnValue('const test = 1;'),
            lineAt: jest.fn(),
            lineCount: 1,
        } as any;

        service = QaxASTService.getInstance();
    });

    afterEach(() => {
        QaxASTService.dispose();
    });

    describe('Singleton Pattern', () => {
        test('should return the same instance', () => {
            const instance1 = QaxASTService.getInstance();
            const instance2 = QaxASTService.getInstance();
            expect(instance1).toBe(instance2);
        });

        test('should dispose and reset instance', () => {
            const instance1 = QaxASTService.getInstance();
            QaxASTService.dispose();
            const instance2 = QaxASTService.getInstance();
            expect(instance1).not.toBe(instance2);
        });
    });

    describe('parseDocument', () => {
        test('should parse supported document successfully', async () => {
            const mockAST = {
                rootNode: {
                    type: 'program',
                    startPosition: { row: 0, column: 0 },
                    endPosition: { row: 0, column: 13 },
                    children: [],
                },
            };

            mockGetAst.mockResolvedValue(mockAST);

            const result = await service.parseDocument(mockDocument);

            expect(result).toBeDefined();
            expect(result?.type).toBe('program');
            expect(mockGetAst).toHaveBeenCalledWith('/test/file.js', 'const test = 1;');
        });

        test('should return null for unsupported file types', async () => {
            const unsupportedDoc = {
                ...mockDocument,
                languageId: 'unsupported',
                uri: vscode.Uri.file('/test/file.unsupported'),
            };

            const result = await service.parseDocument(unsupportedDoc);

            expect(result).toBeNull();
            expect(mockGetAst).not.toHaveBeenCalled();
        });

        test('should return null for empty content', async () => {
            mockDocument.getText = jest.fn().mockReturnValue('');

            const result = await service.parseDocument(mockDocument);

            expect(result).toBeNull();
            expect(mockGetAst).not.toHaveBeenCalled();
        });

        test('should return null when AST parsing fails', async () => {
            mockGetAst.mockResolvedValue(null);

            const result = await service.parseDocument(mockDocument);

            expect(result).toBeNull();
        });

        test('should handle parsing errors gracefully', async () => {
            mockGetAst.mockRejectedValue(new Error('Parsing failed'));

            const result = await service.parseDocument(mockDocument);

            expect(result).toBeNull();
        });
    });

    describe('detectVariableRename', () => {
        test('should detect variable rename changes', async () => {
            const beforeContent = 'const oldVar = 1;\nconsole.log(oldVar);';
            const afterContent = 'const newVar = 1;\nconsole.log(newVar);';

            const mockBeforeAST = {
                rootNode: {
                    type: 'program',
                    children: [
                        {
                            type: 'variable_declaration',
                            children: [
                                {
                                    type: 'variable_declarator',
                                    children: [
                                        {
                                            type: 'identifier',
                                            text: 'oldVar',
                                            startPosition: { row: 0, column: 6 },
                                            endPosition: { row: 0, column: 12 },
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            };

            const mockAfterAST = {
                rootNode: {
                    type: 'program',
                    children: [
                        {
                            type: 'variable_declaration',
                            children: [
                                {
                                    type: 'variable_declarator',
                                    children: [
                                        {
                                            type: 'identifier',
                                            text: 'newVar',
                                            startPosition: { row: 0, column: 6 },
                                            endPosition: { row: 0, column: 12 },
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            };

            mockGetAst
                .mockResolvedValueOnce(mockBeforeAST)
                .mockResolvedValueOnce(mockAfterAST);

            const result = await service.detectVariableRename(
                '/test/file.js',
                beforeContent,
                afterContent,
                'javascript'
            );

            expect(result.length).toBeGreaterThan(0);
            expect(result[0].type).toBe(QaxChangeType.VARIABLE_RENAME);
            expect(result[0].oldValue).toBe('oldVar');
            expect(result[0].newValue).toBe('newVar');
        });

        test('should return empty array when no changes detected', async () => {
            const content = 'const test = 1;';

            const mockAST = {
                rootNode: {
                    type: 'program',
                    children: []
                }
            };

            mockGetAst.mockResolvedValue(mockAST);

            const result = await service.detectVariableRename(
                '/test/file.js',
                content,
                content,
                'javascript'
            );

            expect(result).toEqual([]);
        });

        test('should handle AST parsing failures', async () => {
            mockGetAst.mockResolvedValue(null);

            const result = await service.detectVariableRename(
                '/test/file.js',
                'const test = 1;',
                'const newTest = 1;',
                'javascript'
            );

            expect(result).toEqual([]);
        });
    });

    describe('detectFunctionParameterChanges', () => {
        test('should detect function parameter changes', async () => {
            const beforeContent = 'function test(oldParam) { return oldParam; }';
            const afterContent = 'function test(newParam) { return newParam; }';

            const mockBeforeAST = {
                rootNode: {
                    type: 'program',
                    children: [
                        {
                            type: 'function_declaration',
                            children: [
                                {
                                    type: 'identifier',
                                    text: 'test'
                                },
                                {
                                    type: 'formal_parameters',
                                    children: [
                                        {
                                            type: 'identifier',
                                            text: 'oldParam',
                                            startPosition: { row: 0, column: 14 },
                                            endPosition: { row: 0, column: 22 },
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            };

            const mockAfterAST = {
                rootNode: {
                    type: 'program',
                    children: [
                        {
                            type: 'function_declaration',
                            children: [
                                {
                                    type: 'identifier',
                                    text: 'test'
                                },
                                {
                                    type: 'formal_parameters',
                                    children: [
                                        {
                                            type: 'identifier',
                                            text: 'newParam',
                                            startPosition: { row: 0, column: 14 },
                                            endPosition: { row: 0, column: 22 },
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            };

            mockGetAst
                .mockResolvedValueOnce(mockBeforeAST)
                .mockResolvedValueOnce(mockAfterAST);

            const result = await service.detectFunctionParameterChanges(
                '/test/file.js',
                beforeContent,
                afterContent,
                'javascript'
            );

            expect(result.length).toBeGreaterThan(0);
            expect(result[0].type).toBe(QaxChangeType.FUNCTION_PARAMETER_RENAME);
        });

        test('should return empty array when no parameter changes', async () => {
            const content = 'function test(param) { return param; }';

            const mockAST = {
                rootNode: {
                    type: 'program',
                    children: []
                }
            };

            mockGetAst.mockResolvedValue(mockAST);

            const result = await service.detectFunctionParameterChanges(
                '/test/file.js',
                content,
                content,
                'javascript'
            );

            expect(result).toEqual([]);
        });
    });

    describe('detectFunctionCallDeletions', () => {
        test('should detect function call deletions', async () => {
            const beforeContent = 'test();\nconsole.log("hello");';
            const afterContent = 'console.log("hello");';

            const mockBeforeAST = {
                rootNode: {
                    type: 'program',
                    children: [
                        {
                            type: 'expression_statement',
                            children: [
                                {
                                    type: 'call_expression',
                                    children: [
                                        {
                                            type: 'identifier',
                                            text: 'test',
                                            startPosition: { row: 0, column: 0 },
                                            endPosition: { row: 0, column: 4 },
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            };

            const mockAfterAST = {
                rootNode: {
                    type: 'program',
                    children: []
                }
            };

            mockGetAst
                .mockResolvedValueOnce(mockBeforeAST)
                .mockResolvedValueOnce(mockAfterAST);

            const result = await service.detectFunctionCallDeletions(
                '/test/file.js',
                beforeContent,
                afterContent,
                'javascript'
            );

            expect(result.length).toBeGreaterThan(0);
            expect(result[0].type).toBe(QaxChangeType.FUNCTION_CALL_DELETION);
        });
    });

    describe('detectVariableDeletions', () => {
        test('should detect variable deletions', async () => {
            const beforeContent = 'const test = 1;\nconst keep = 2;';
            const afterContent = 'const keep = 2;';

            const mockBeforeAST = {
                rootNode: {
                    type: 'program',
                    children: [
                        {
                            type: 'variable_declaration',
                            children: [
                                {
                                    type: 'variable_declarator',
                                    children: [
                                        {
                                            type: 'identifier',
                                            text: 'test',
                                            startPosition: { row: 0, column: 6 },
                                            endPosition: { row: 0, column: 10 },
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            };

            const mockAfterAST = {
                rootNode: {
                    type: 'program',
                    children: []
                }
            };

            mockGetAst
                .mockResolvedValueOnce(mockBeforeAST)
                .mockResolvedValueOnce(mockAfterAST);

            const result = await service.detectVariableDeletions(
                '/test/file.js',
                beforeContent,
                afterContent,
                'javascript'
            );

            expect(result.length).toBeGreaterThan(0);
            expect(result[0].type).toBe(QaxChangeType.VARIABLE_DELETION);
        });
    });

    describe('detectImportChanges', () => {
        test('should detect import changes', async () => {
            const beforeContent = 'import { oldFunc } from "./module";';
            const afterContent = 'import { newFunc } from "./module";';

            const mockBeforeAST = {
                rootNode: {
                    type: 'program',
                    children: [
                        {
                            type: 'import_statement',
                            children: [
                                {
                                    type: 'import_specifier',
                                    children: [
                                        {
                                            type: 'identifier',
                                            text: 'oldFunc',
                                            startPosition: { row: 0, column: 9 },
                                            endPosition: { row: 0, column: 16 },
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            };

            const mockAfterAST = {
                rootNode: {
                    type: 'program',
                    children: [
                        {
                            type: 'import_statement',
                            children: [
                                {
                                    type: 'import_specifier',
                                    children: [
                                        {
                                            type: 'identifier',
                                            text: 'newFunc',
                                            startPosition: { row: 0, column: 9 },
                                            endPosition: { row: 0, column: 16 },
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            };

            mockGetAst
                .mockResolvedValueOnce(mockBeforeAST)
                .mockResolvedValueOnce(mockAfterAST);

            const result = await service.detectImportChanges(
                '/test/file.js',
                beforeContent,
                afterContent,
                'javascript'
            );

            expect(result.length).toBeGreaterThan(0);
            expect(result[0].type).toBe(QaxChangeType.IMPORT_CHANGE);
        });
    });

    describe('detectTypeChanges', () => {
        test('should detect type changes in TypeScript', async () => {
            const beforeContent = 'let test: string = "hello";';
            const afterContent = 'let test: number = 42;';

            const mockBeforeAST = {
                rootNode: {
                    type: 'program',
                    children: [
                        {
                            type: 'variable_declaration',
                            children: [
                                {
                                    type: 'variable_declarator',
                                    children: [
                                        {
                                            type: 'type_annotation',
                                            children: [
                                                {
                                                    type: 'predefined_type',
                                                    text: 'string',
                                                    startPosition: { row: 0, column: 10 },
                                                    endPosition: { row: 0, column: 16 },
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            };

            const mockAfterAST = {
                rootNode: {
                    type: 'program',
                    children: [
                        {
                            type: 'variable_declaration',
                            children: [
                                {
                                    type: 'variable_declarator',
                                    children: [
                                        {
                                            type: 'type_annotation',
                                            children: [
                                                {
                                                    type: 'predefined_type',
                                                    text: 'number',
                                                    startPosition: { row: 0, column: 10 },
                                                    endPosition: { row: 0, column: 16 },
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            };

            mockGetAst
                .mockResolvedValueOnce(mockBeforeAST)
                .mockResolvedValueOnce(mockAfterAST);

            const result = await service.detectTypeChanges(
                '/test/file.ts',
                beforeContent,
                afterContent,
                'typescript'
            );

            expect(result.length).toBeGreaterThan(0);
            expect(result[0].type).toBe(QaxChangeType.TYPE_CHANGE);
        });
    });

    describe('isASTAvailable', () => {
        test('should return true when AST is available', async () => {
            mockGetAst.mockResolvedValue({
                rootNode: { type: 'program' }
            });

            const result = await service.isASTAvailable('/test/file.js', 'javascript');

            expect(result).toBe(true);
        });

        test('should return false when AST is not available', async () => {
            mockGetAst.mockResolvedValue(null);

            const result = await service.isASTAvailable('/test/file.js', 'javascript');

            expect(result).toBe(false);
        });

        test('should return false for unsupported file types', async () => {
            const result = await service.isASTAvailable('/test/file.unsupported', 'unsupported');

            expect(result).toBe(false);
            expect(mockGetAst).not.toHaveBeenCalled();
        });

        test('should handle errors gracefully', async () => {
            mockGetAst.mockRejectedValue(new Error('AST error'));

            const result = await service.isASTAvailable('/test/file.js', 'javascript');

            expect(result).toBe(false);
        });
    });

    describe('Helper Methods', () => {
        describe('isSupportedFile', () => {
            test('should support JavaScript files', () => {
                expect((service as any).isSupportedFile('/test/file.js', 'javascript')).toBe(true);
                expect((service as any).isSupportedFile('/test/file.jsx', 'javascriptreact')).toBe(true);
            });

            test('should support TypeScript files', () => {
                expect((service as any).isSupportedFile('/test/file.ts', 'typescript')).toBe(true);
                expect((service as any).isSupportedFile('/test/file.tsx', 'typescriptreact')).toBe(true);
            });

            test('should support Python files', () => {
                expect((service as any).isSupportedFile('/test/file.py', 'python')).toBe(true);
            });

            test('should not support unsupported files', () => {
                expect((service as any).isSupportedFile('/test/file.txt', 'plaintext')).toBe(false);
                expect((service as any).isSupportedFile('/test/file.unknown', 'unknown')).toBe(false);
            });
        });

        describe('convertTreeSitterNode', () => {
            test('should convert tree-sitter node to QaxASTNode', () => {
                const treeSitterNode = {
                    type: 'identifier',
                    text: 'testVar',
                    startPosition: { row: 0, column: 6 },
                    endPosition: { row: 0, column: 13 },
                    children: [],
                };

                const result = (service as any).convertTreeSitterNode(treeSitterNode);

                expect(result.type).toBe('identifier');
                expect(result.text).toBe('testVar');
                expect(result.range.start.line).toBe(0);
                expect(result.range.start.character).toBe(6);
                expect(result.range.end.line).toBe(0);
                expect(result.range.end.character).toBe(13);
            });

            test('should handle nodes with children', () => {
                const treeSitterNode = {
                    type: 'function_declaration',
                    text: 'function test() {}',
                    startPosition: { row: 0, column: 0 },
                    endPosition: { row: 0, column: 18 },
                    children: [
                        {
                            type: 'identifier',
                            text: 'test',
                            startPosition: { row: 0, column: 9 },
                            endPosition: { row: 0, column: 13 },
                            children: [],
                        }
                    ],
                };

                const result = (service as any).convertTreeSitterNode(treeSitterNode);

                expect(result.children).toHaveLength(1);
                expect(result.children[0].type).toBe('identifier');
            });
        });
    });

    describe('Error Handling and Edge Cases', () => {
        test('should handle null/undefined inputs gracefully', async () => {
            const result = await service.detectVariableRename(
                null as any,
                null as any,
                null as any,
                null as any
            );

            expect(result).toEqual([]);
        });

        test('should handle empty content', async () => {
            const result = await service.detectVariableRename(
                '/test/file.js',
                '',
                '',
                'javascript'
            );

            expect(result).toEqual([]);
        });

        test('should handle malformed AST', async () => {
            mockGetAst.mockResolvedValue({
                rootNode: null
            });

            const result = await service.detectVariableRename(
                '/test/file.js',
                'const test = 1;',
                'const newTest = 1;',
                'javascript'
            );

            expect(result).toEqual([]);
        });

        test('should handle very large files', async () => {
            const largeContent = 'const test = 1;\n'.repeat(10000);

            mockGetAst.mockResolvedValue({
                rootNode: { type: 'program', children: [] }
            });

            const result = await service.detectVariableRename(
                '/test/file.js',
                largeContent,
                largeContent.replace('test', 'newTest'),
                'javascript'
            );

            expect(result).toEqual([]);
        });
    });
});
