# QaxNextEdit Service

QaxNextEdit 是一个基于 LSP 和 AST 分析的智能代码编辑建议服务，它能够检测代码修改并提供精确的跳转和修改建议。

## 功能特性

### 核心功能
1. **智能变更检测** - 基于 LSP 和 AST 分析检测代码变更类型
2. **精确跳转建议** - 提供需要同步修改的精确位置
3. **多种检测类型** - 支持变量重命名、函数参数变更、函数调用删除等
4. **实时分析** - 监听文件变更，自动触发分析
5. **可配置服务** - 用户可选择使用原始 NextEdit 或新的 QaxNextEdit

### 检测类型
- **变量重命名** - 检测变量名修改，提供所有引用位置的跳转建议
- **函数参数变更** - 检测函数参数的增加、删除或修改，提供调用点的更新建议
- **函数调用删除** - 检测函数调用的删除，提供相关调用的清理建议
- **变量删除** - 检测变量删除，提供使用位置的清理建议
- **导入变更** - 检测导入语句的变更（待实现）
- **类型变更** - 检测类型定义的变更（待实现）

### 技术特性
- **LSP 集成** - 利用语言服务器获取符号信息、引用、定义等
- **AST 分析** - 基于 Tree-sitter 进行代码结构分析
- **多语言支持** - 支持 TypeScript、JavaScript、Python、Java、C#、C++、Rust、Go 等
- **防抖机制** - 避免频繁修改导致过多分析请求
- **置信度评估** - 为每个检测结果提供置信度评分
- **优先级排序** - 根据位置、置信度等因素对建议进行优先级排序

## 使用方法

### 启用 QaxNextEdit
1. 打开 VS Code 设置
2. 搜索 `qax-code.nextEdit.useQaxNextEdit`
3. 设置为 `true` 以启用 QaxNextEdit（默认使用原始 NextEdit）

### 配置选项
```json
{
  "qax-code.nextEdit.useQaxNextEdit": false,
  "qax-code.nextEdit.enabled": true,
  "qax-code.nextEdit.enableLSPIntegration": true,
  "qax-code.nextEdit.enableASTAnalysis": true,
  "qax-code.nextEdit.debounceDelayMs": 1500,
  "qax-code.nextEdit.maxSuggestions": 8,
  "qax-code.nextEdit.confidenceThreshold": 0.7,
  "qax-code.nextEdit.supportedLanguages": [
    "typescript", "javascript", "python", "java", "csharp",
    "cpp", "c", "rust", "go", "php", "ruby", "swift", "kotlin"
  ],
  "qax-code.nextEdit.analysisDepth": "deep"
}
```

### 命令
- `qaxNextEdit.toggle` - 启用/禁用 QaxNextEdit
- `qaxNextEdit.showSuggestions` - 显示当前文件的建议
- `qaxNextEdit.applyAllSuggestions` - 应用所有建议
- `qaxNextEdit.clearSuggestions` - 清除所有建议
- `qaxNextEdit.showStatus` - 显示服务状态
- `qaxNextEdit.nextSuggestion` - 导航到下一个建议
- `qaxNextEdit.previousSuggestion` - 导航到上一个建议

### 快捷键（建议）
```json
{
  "key": "ctrl+shift+n",
  "command": "qaxNextEdit.showSuggestions"
},
{
  "key": "ctrl+shift+right",
  "command": "qaxNextEdit.nextSuggestion"
},
{
  "key": "ctrl+shift+left",
  "command": "qaxNextEdit.previousSuggestion"
}
```

## 工作流程

1. **文件监听** - 监听文档变更事件
2. **变更分析** - 使用 LSP 和 AST 分析变更类型
3. **建议生成** - 根据检测结果生成跳转建议
4. **UI 显示** - 在编辑器中高亮显示建议位置
5. **用户交互** - 用户可以应用或忽略建议

## 架构设计

### 核心组件
1. **QaxNextEditService** - 主服务类，管理整个生命周期
2. **QaxChangeDetector** - 变更检测器，分析代码变更
3. **QaxLSPService** - LSP 集成服务，获取语言服务器信息
4. **QaxASTService** - AST 分析服务，基于 Tree-sitter 解析代码
5. **QaxJumpSuggestionEngine** - 跳转建议引擎，生成修改建议
6. **QaxNextEditUIProvider** - UI 提供者，管理用户界面
7. **QaxNextEditProvider** - 服务提供者，管理注册和配置

### 数据流
```
文件修改 → 变更检测 → LSP/AST 分析 → 建议生成 → UI 展示 → 用户操作
```

### 集成点
- **AutocompleteConfigManager** - 复用自动完成配置
- **VS Code API** - 文件变更、编辑器焦点、语言服务器
- **Tree-sitter** - 复用现有的解析器基础设施

## 开发说明

### 文件结构
```
src/services/autocomplete/qaxNextEdit/
├── types/
│   └── QaxNextEditTypes.ts          # 类型定义
├── services/
│   ├── QaxLSPService.ts             # LSP 集成服务
│   ├── QaxASTService.ts             # AST 分析服务
│   ├── QaxChangeDetector.ts         # 变更检测器
│   └── QaxJumpSuggestionEngine.ts   # 跳转建议引擎
├── QaxNextEditService.ts            # 主服务类
├── QaxNextEditUIProvider.ts         # UI 提供者
├── QaxNextEditProvider.ts           # 服务提供者
└── README.md                        # 说明文档
```

### 扩展点
1. **新检测类型** - 在 `QaxChangeType` 枚举中添加
2. **自定义分析** - 扩展 `QaxChangeDetector` 类
3. **UI 定制** - 扩展 `QaxNextEditUIProvider` 类
4. **语言支持** - 在配置中添加新的语言 ID

## 性能考虑

1. **防抖机制** - 避免频繁的分析请求
2. **缓存结果** - 缓存分析结果避免重复计算
3. **按需加载** - 只在需要时加载语言解析器
4. **置信度过滤** - 过滤低置信度的检测结果
5. **优先级排序** - 优先显示高优先级的建议

## 故障排除

### 常见问题
1. **LSP 不可用** - 检查语言服务器是否正确安装和配置
2. **AST 解析失败** - 检查 Tree-sitter 解析器是否支持该语言
3. **建议不准确** - 调整置信度阈值或分析深度
4. **性能问题** - 增加防抖延迟或减少最大建议数

### 调试信息
- 查看控制台输出中的 `QaxNextEdit` 相关日志
- 使用 `qaxNextEdit.showStatus` 命令查看服务状态
- 检查配置是否正确设置

## 与原始 NextEdit 的区别

| 特性 | 原始 NextEdit | QaxNextEdit |
|------|---------------|-------------|
| 分析方式 | AI 模型推理 | LSP + AST 分析 |
| 检测精度 | 依赖模型质量 | 基于语法结构 |
| 响应速度 | 依赖 API 调用 | 本地分析 |
| 离线支持 | 不支持 | 支持 |
| 语言支持 | 通用 | 特定语言 |
| 建议类型 | 通用建议 | 特定变更类型 |
| 配置复杂度 | 简单 | 较复杂 |

## 未来计划

1. **更多检测类型** - 支持导入变更、类型变更等
2. **智能合并** - 自动合并相关的修改建议
3. **批量操作** - 支持批量应用建议
4. **自定义规则** - 允许用户定义自定义检测规则
5. **性能优化** - 进一步优化分析性能
6. **更多语言** - 支持更多编程语言
