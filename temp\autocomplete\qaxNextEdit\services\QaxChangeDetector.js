"use strict"
var __createBinding =
	(this && this.__createBinding) ||
	(Object.create
		? function (o, m, k, k2) {
				if (k2 === undefined) k2 = k
				var desc = Object.getOwnPropertyDescriptor(m, k)
				if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
					desc = {
						enumerable: true,
						get: function () {
							return m[k]
						},
					}
				}
				Object.defineProperty(o, k2, desc)
			}
		: function (o, m, k, k2) {
				if (k2 === undefined) k2 = k
				o[k2] = m[k]
			})
var __setModuleDefault =
	(this && this.__setModuleDefault) ||
	(Object.create
		? function (o, v) {
				Object.defineProperty(o, "default", { enumerable: true, value: v })
			}
		: function (o, v) {
				o["default"] = v
			})
var __importStar =
	(this && this.__importStar) ||
	function (mod) {
		if (mod && mod.__esModule) return mod
		var result = {}
		if (mod != null)
			for (var k in mod)
				if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k)
		__setModuleDefault(result, mod)
		return result
	}
Object.defineProperty(exports, "__esModule", { value: true })
exports.QaxChangeDetector = void 0
const vscode = __importStar(require("vscode"))
const QaxNextEditTypes_1 = require("../types/QaxNextEditTypes")
const QaxLSPService_1 = require("./QaxLSPService")
const QaxASTService_1 = require("./QaxASTService")
const QaxJumpSuggestionEngine_1 = require("./QaxJumpSuggestionEngine")
/**
 * 修改检测器，分析代码变更并识别需要同步修改的位置
 */
class QaxChangeDetector {
	constructor(config) {
		this.config = config
		this.lspService = QaxLSPService_1.QaxLSPService.getInstance()
		this.astService = QaxASTService_1.QaxASTService.getInstance()
		this.jumpSuggestionEngine = new QaxJumpSuggestionEngine_1.QaxJumpSuggestionEngine(config)
	}
	/**
	 * 分析代码变更
	 */
	async analyzeChanges(context) {
		const startTime = Date.now()
		const detectedChanges = []
		try {
			// 检查语言是否支持
			if (!this.config.supportedLanguages.includes(context.languageId)) {
				return {
					detectedChanges: [],
					jumpSuggestions: [],
					analysisTime: Date.now() - startTime,
					confidence: 0,
					metadata: {
						lspAvailable: false,
						astParsed: false,
						symbolsFound: 0,
						referencesFound: 0,
						unsupportedLanguage: true,
					},
				}
			}
			// 1. 基于文本差异的快速检测
			const textChanges = await this.detectTextChanges(context)
			detectedChanges.push(...textChanges)
			// 2. LSP 集成检测（如果启用）
			let lspAvailable = false
			if (this.config.enableLSPIntegration) {
				lspAvailable = await this.lspService.isLSPAvailable(context.document)
				if (lspAvailable) {
					const lspChanges = await this.detectLSPChanges(context)
					detectedChanges.push(...lspChanges)
				}
			}
			// 3. AST 分析检测（如果启用）
			let astParsed = false
			if (this.config.enableASTAnalysis) {
				const astChanges = await this.detectASTChanges(context)
				if (astChanges.length > 0) {
					astParsed = true
					detectedChanges.push(...astChanges)
				}
			}
			// 4. 合并和去重检测结果
			const mergedChanges = this.mergeDetectedChanges(detectedChanges)
			// 5. 过滤低置信度的检测结果
			const filteredChanges = mergedChanges.filter((change) => change.confidence >= this.config.confidenceThreshold)
			// 6. 生成跳转建议
			const jumpSuggestions = await this.generateJumpSuggestions(filteredChanges, context)
			const analysisTime = Date.now() - startTime
			const confidence = this.calculateOverallConfidence(filteredChanges)
			return {
				detectedChanges: filteredChanges,
				jumpSuggestions,
				analysisTime,
				confidence,
				metadata: {
					lspAvailable,
					astParsed,
					symbolsFound: context.symbols?.length || 0,
					referencesFound: filteredChanges.reduce((sum, change) => sum + (change.metadata?.references?.length || 0), 0),
					originalChangesCount: detectedChanges.length,
					filteredChangesCount: filteredChanges.length,
				},
			}
		} catch (error) {
			console.error("QaxChangeDetector: Analysis failed:", error)
			return {
				detectedChanges: [],
				jumpSuggestions: [],
				analysisTime: Date.now() - startTime,
				confidence: 0,
				metadata: {
					lspAvailable: false,
					astParsed: false,
					symbolsFound: 0,
					referencesFound: 0,
					error: error instanceof Error ? error.message : "Unknown error",
				},
			}
		}
	}
	/**
	 * 基于文本差异的快速检测
	 */
	async detectTextChanges(context) {
		const changes = []
		// 分析文档变更事件
		for (const change of context.changes) {
			const changeText = change.text
			const rangeLength = change.rangeLength
			// 检测变量重命名模式
			if (this.isVariableRenamePattern(changeText, rangeLength)) {
				changes.push({
					type: QaxNextEditTypes_1.QaxChangeType.VARIABLE_RENAME,
					filePath: context.filePath,
					range: change.range,
					oldValue: context.beforeContent.substring(
						context.document.offsetAt(change.range.start),
						context.document.offsetAt(change.range.start) + rangeLength,
					),
					newValue: changeText,
					confidence: 0.6, // 基于文本的检测置信度较低
					metadata: {
						detectionMethod: "text_diff",
					},
				})
			}
			// 检测函数调用删除模式
			if (this.isFunctionCallDeletionPattern(changeText, rangeLength)) {
				changes.push({
					type: QaxNextEditTypes_1.QaxChangeType.FUNCTION_CALL_DELETION,
					filePath: context.filePath,
					range: change.range,
					oldValue: context.beforeContent.substring(
						context.document.offsetAt(change.range.start),
						context.document.offsetAt(change.range.start) + rangeLength,
					),
					confidence: 0.5,
					metadata: {
						detectionMethod: "text_diff",
					},
				})
			}
		}
		return changes
	}
	/**
	 * 基于 LSP 的检测
	 */
	async detectLSPChanges(context) {
		const changes = []
		// 获取文档符号
		const symbols = await this.lspService.getDocumentSymbols(context.document)
		context.symbols = symbols
		// 分析每个变更位置
		for (const change of context.changes) {
			const position = change.range.start
			// 获取该位置的引用和定义
			const references = await this.lspService.getReferences(context.document, position)
			const definitions = await this.lspService.getDefinitions(context.document, position)
			if (references.length > 0 || definitions.length > 0) {
				// 检测符号重命名
				const renameDetection = await this.lspService.detectSymbolRename(
					context.document,
					position,
					change.range.end,
					context.beforeContent.substring(
						context.document.offsetAt(change.range.start),
						context.document.offsetAt(change.range.start) + change.rangeLength,
					),
					change.text,
				)
				if (renameDetection) {
					renameDetection.filePath = context.filePath
					renameDetection.metadata = {
						...renameDetection.metadata,
						detectionMethod: "lsp",
						references,
						definitions,
					}
					changes.push(renameDetection)
				}
			}
		}
		return changes
	}
	/**
	 * 基于 AST 的检测
	 */
	async detectASTChanges(context) {
		const changes = []
		try {
			// 解析新旧内容的 AST
			const beforeDocument = await vscode.workspace.openTextDocument({
				content: context.beforeContent,
				language: context.languageId,
			})
			const afterDocument = context.document
			const beforeAST = await this.astService.parseDocument(beforeDocument)
			const afterAST = await this.astService.parseDocument(afterDocument)
			if (!beforeAST || !afterAST) {
				return changes
			}
			// 检测变量重命名
			const variableRenames = this.astService.detectVariableRename(beforeAST, afterAST)
			variableRenames.forEach((change) => {
				change.filePath = context.filePath
				change.metadata = {
					...change.metadata,
					detectionMethod: "ast",
				}
			})
			changes.push(...variableRenames)
			// 检测函数参数变更
			const parameterChanges = this.astService.detectFunctionParameterChanges(beforeAST, afterAST)
			parameterChanges.forEach((change) => {
				change.filePath = context.filePath
				change.metadata = {
					...change.metadata,
					detectionMethod: "ast",
				}
			})
			changes.push(...parameterChanges)
			// 检测函数调用删除
			const callDeletions = this.astService.detectFunctionCallDeletions(beforeAST, afterAST)
			callDeletions.forEach((change) => {
				change.filePath = context.filePath
				change.metadata = {
					...change.metadata,
					detectionMethod: "ast",
				}
			})
			changes.push(...callDeletions)
		} catch (error) {
			console.warn("QaxChangeDetector: AST analysis failed:", error)
		}
		return changes
	}
	/**
	 * 合并和去重检测结果
	 */
	mergeDetectedChanges(changes) {
		const merged = []
		const seen = new Set()
		// 按置信度排序，优先保留高置信度的检测结果
		const sorted = changes.sort((a, b) => b.confidence - a.confidence)
		for (const change of sorted) {
			// 创建唯一标识符
			const key = `${change.type}-${change.range.start.line}-${change.range.start.character}-${change.oldValue}`
			if (!seen.has(key)) {
				seen.add(key)
				merged.push(change)
			} else {
				// 如果已存在相同的检测结果，合并元数据
				const existing = merged.find(
					(m) => m.type === change.type && m.range.isEqual(change.range) && m.oldValue === change.oldValue,
				)
				if (existing && existing.confidence < change.confidence) {
					// 用更高置信度的结果替换
					const index = merged.indexOf(existing)
					merged[index] = change
				}
			}
		}
		return merged
	}
	/**
	 * 生成跳转建议
	 */
	async generateJumpSuggestions(changes, context) {
		return await this.jumpSuggestionEngine.generateJumpSuggestions(changes, context)
	}
	/**
	 * 计算整体置信度
	 */
	calculateOverallConfidence(changes) {
		if (changes.length === 0) return 0
		const totalConfidence = changes.reduce((sum, change) => sum + change.confidence, 0)
		return totalConfidence / changes.length
	}
	/**
	 * 检测是否为变量重命名模式
	 */
	isVariableRenamePattern(changeText, rangeLength) {
		// 简单的启发式规则
		return (
			rangeLength > 0 && // 有内容被替换
			changeText.length > 0 && // 有新内容
			/^[a-zA-Z_$][a-zA-Z0-9_$]*$/.test(changeText) && // 新内容是有效标识符
			changeText !== changeText.toLowerCase() // 不是全小写（可能是常量）
		)
	}
	/**
	 * 检测是否为函数调用删除模式
	 */
	isFunctionCallDeletionPattern(changeText, rangeLength) {
		// 简单的启发式规则
		return (
			rangeLength > 0 && // 有内容被删除
			changeText === "" // 没有新内容（纯删除）
		)
	}
	/**
	 * 更新配置
	 */
	updateConfig(config) {
		this.config = config
		this.jumpSuggestionEngine.updateConfig(config)
	}
}
exports.QaxChangeDetector = QaxChangeDetector
