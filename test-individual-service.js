#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');

const serviceName = process.argv[2] || 'QaxChangeDetector';

console.log(`🧪 Testing ${serviceName}...\n`);

// Create Jest config for the specific service
const jestConfig = {
    preset: 'ts-jest',
    testEnvironment: 'node',
    roots: ['<rootDir>/src'],
    testMatch: [`**/${serviceName}.test.ts`],
    transform: {
        '^.+\\.ts$': 'ts-jest',
    },
    collectCoverageFrom: [
        `src/services/autocomplete/qaxNextEdit/services/${serviceName}.ts`
    ],
    coverageDirectory: `coverage/${serviceName}`,
    coverageReporters: ['text', 'json'],
    testTimeout: 30000,
    verbose: true,
    collectCoverage: true,
    coverageThreshold: {
        global: {
            branches: 85,
            functions: 85,
            lines: 85,
            statements: 85
        }
    },
    moduleNameMapping: {
        '^vscode$': '<rootDir>/src/services/autocomplete/qaxNextEdit/services/__tests__/__mocks__/vscode.ts'
    },
    setupFilesAfterEnv: ['<rootDir>/src/services/autocomplete/qaxNextEdit/services/__tests__/setup.ts']
};

// Write config
const configFile = `jest.${serviceName}.config.js`;
fs.writeFileSync(configFile, `module.exports = ${JSON.stringify(jestConfig, null, 2)};`);

// Run Jest
const jestProcess = spawn('npx', [
    'jest',
    `--config=${configFile}`,
    '--coverage',
    '--verbose'
], {
    stdio: 'inherit',
    shell: true,
    cwd: __dirname
});

jestProcess.on('close', (code) => {
    // Clean up
    try {
        fs.unlinkSync(configFile);
    } catch (error) {
        // Ignore
    }

    console.log(`\n${serviceName} test completed with exit code: ${code}`);
    process.exit(code);
});

jestProcess.on('error', (error) => {
    console.error('Error running tests:', error.message);
    process.exit(1);
});
