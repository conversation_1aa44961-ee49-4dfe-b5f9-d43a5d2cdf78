# 原符号引用查找问题修复

## 问题描述

您发现了一个关键问题：**当我们在已经修改的位置调用LSP查询时，LSP看到的是新符号，自然找不到原符号的引用。**

### 问题场景
```javascript
// 原始代码
function showAddNextEventModal(date) { ... }
showAddNextEventModal(today);

// 用户修改后
function showAddNewEventModal(date) { ... }  // <- 在这个位置调用LSP
showAddNextEventModal(today);  // <- 这个引用找不到了
```

当我们在修改后的位置（新符号 `showAddNewEventModal`）调用LSP时：
- LSP查找的是 `showAddNewEventModal` 的引用
- 但其他位置还是 `showAddNextEventModal`
- 结果：找不到任何引用！

## 解决方案

### 核心思路
**使用原始文档内容创建临时文档，在临时文档上查询原符号的引用**

### 技术实现

#### 1. 新增LSP方法
```typescript
async getReferencesFromOriginalContent(
    originalContent: string,
    filePath: string, 
    position: vscode.Position,
    languageId: string
): Promise<vscode.Location[]>
```

#### 2. 实现步骤
```typescript
// 1. 创建包含原始内容的临时文档
const tempDocument = await vscode.workspace.openTextDocument({
    content: originalContent,  // 使用修改前的内容
    language: languageId
});

// 2. 在临时文档上查询引用
const references = await vscode.commands.executeCommand(
    "vscode.executeReferenceProvider",
    tempDocument.uri,
    position  // 相同的位置，但文档内容是原始的
);

// 3. 将临时文档的引用位置映射回原始文档
const mappedReferences = references.map(ref => {
    if (ref.uri.toString() === tempDocument.uri.toString()) {
        return new vscode.Location(originalUri, ref.range);
    }
    return ref; // 外部文件的引用直接使用
});
```

### 集成到现有系统

#### 1. 修改JumpSuggestionEngine
```typescript
// 之前：使用当前文档查询（错误）
const lspReferences = await this.lspService.getReferences(context.document, position);

// 现在：使用原始内容查询（正确）
const lspReferences = await this.lspService.getReferencesFromOriginalContent(
    context.beforeContent,  // 修改前的内容
    context.filePath,
    position,
    context.languageId
);
```

#### 2. 修改ChangeDetector
在符号检测时就收集原符号的引用信息：
```typescript
// 获取原符号的引用信息（使用原始内容）
let references: vscode.Location[] = []
if (symbolChange.symbolName && this.config.enableLSPIntegration) {
    references = await this.lspService.getReferencesFromOriginalContent(
        context.beforeContent,
        context.filePath,
        symbolChange.range.start,
        context.languageId
    );
}

// 将引用信息包含在元数据中
metadata: {
    detectionMethod: "symbol_based",
    symbolName: symbolChange.symbolName,
    symbolType: symbolChange.symbolType,
    context: symbolChange.context,
    references: references, // 包含原符号的引用信息
}
```

## 测试验证

### 测试场景
```javascript
// 原始内容
function showAddNextEventModal(date) { ... }
const handleClick = () => showAddNextEventModal(new Date());
<div onClick={() => showAddNextEventModal(selectedDate)}>

// 修改后
function showAddNewEventModal(date) { ... }  // 用户修改了这里
const handleClick = () => showAddNextEventModal(new Date());  // 这里需要被找到
<div onClick={() => showAddNextEventModal(selectedDate)}>    // 这里也需要被找到
```

### 测试结果
✅ **成功找到2个原符号的引用**
- Reference 1: 第10行的函数调用
- Reference 2: 第15行的函数调用

## 优势

### 1. 准确性
- ✅ 查找的是原符号的引用，不是新符号
- ✅ 避免了"找不到引用"的问题

### 2. 完整性  
- ✅ 能找到所有需要更新的位置
- ✅ 包括当前文件和外部文件的引用

### 3. 兼容性
- ✅ 利用VSCode的LSP基础设施
- ✅ 支持所有LSP支持的语言
- ✅ 自动处理语法高亮和语义分析

## 工作流程

```mermaid
graph TD
    A[用户修改符号] --> B[检测到符号变化]
    B --> C[创建包含原始内容的临时文档]
    C --> D[在临时文档上查询原符号引用]
    D --> E[LSP返回临时文档中的引用]
    E --> F[将引用位置映射回原始文档]
    F --> G[生成跳转建议]
    G --> H[用户看到需要更新的其他位置]
```

## 预期效果

### 用户体验改进
**之前**：
```
修改: showAddNextEventModal -> showAddNewEventModal
建议: (无) - 找不到引用
```

**现在**：
```
修改: showAddNextEventModal -> showAddNewEventModal  
建议:
  ✅ 更新第8行的函数调用
  ✅ 更新第12行的JSX中的调用
  ✅ 更新其他文件中的引用
```

## 总结

这个修复解决了LSP引用查找的根本问题：
1. **问题根源**：在修改后的位置查询新符号的引用
2. **解决方案**：在原始内容中查询原符号的引用  
3. **技术手段**：临时文档 + 位置映射
4. **最终效果**：准确找到所有需要更新的引用位置

现在QaxNextEdit能够正确理解用户的重命名意图，并提供准确的跳转建议来帮助用户完成完整的重构。
