#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

/**
 * Comprehensive test runner for all QAX services with 95% coverage target
 */
class AllServiceTestRunner {
    constructor() {
        this.services = [
            {
                name: 'QaxLSPService',
                testFile: 'QaxLSPService.test.ts',
                sourceFile: 'src/services/autocomplete/qaxNextEdit/services/QaxLSPService.ts'
            },
            {
                name: 'QaxChangeDetector',
                testFile: 'QaxChangeDetector.test.ts',
                sourceFile: 'src/services/autocomplete/qaxNextEdit/services/QaxChangeDetector.ts'
            },
            {
                name: 'QaxJumpSuggestionEngine',
                testFile: 'QaxJumpSuggestionEngine.test.ts',
                sourceFile: 'src/services/autocomplete/qaxNextEdit/services/QaxJumpSuggestionEngine.ts'
            },
            {
                name: 'QaxASTService',
                testFile: 'QaxASTService.test.ts',
                sourceFile: 'src/services/autocomplete/qaxNextEdit/services/QaxASTService.ts'
            }
        ];
        
        this.targetCoverage = 95;
        this.results = [];
    }

    async run() {
        console.log('🚀 QAX Services Complete Test Suite');
        console.log('🎯 Target: 95% Code Coverage for All Services');
        console.log('=' .repeat(60) + '\n');

        try {
            // Check environment
            await this.checkEnvironment();
            
            // Install dependencies if needed
            await this.ensureDependencies();
            
            // Run tests for each service
            for (const service of this.services) {
                console.log(`\n📋 Testing ${service.name}...`);
                console.log('-'.repeat(40));
                
                const result = await this.runServiceTest(service);
                this.results.push(result);
                
                this.displayServiceResult(result);
            }
            
            // Generate final report
            this.generateFinalReport();
            
        } catch (error) {
            console.error('\n❌ Test suite failed:', error.message);
            process.exit(1);
        }
    }

    async checkEnvironment() {
        console.log('🔍 Checking environment...');
        
        // Check if all test files exist
        for (const service of this.services) {
            const testPath = `src/services/autocomplete/qaxNextEdit/services/__tests__/${service.testFile}`;
            if (!fs.existsSync(testPath)) {
                throw new Error(`Test file not found: ${testPath}`);
            }
            
            if (!fs.existsSync(service.sourceFile)) {
                throw new Error(`Source file not found: ${service.sourceFile}`);
            }
        }
        
        console.log('   ✅ All test and source files exist');
    }

    async ensureDependencies() {
        console.log('📦 Ensuring dependencies...');
        
        const requiredDeps = ['jest', '@types/jest', 'ts-jest', 'typescript'];
        const packageJsonPath = 'package.json';
        
        if (!fs.existsSync(packageJsonPath)) {
            console.log('   Creating package.json...');
            const packageJson = {
                name: 'qax-services-tests',
                version: '1.0.0',
                scripts: {
                    test: 'jest',
                    'test:coverage': 'jest --coverage'
                },
                devDependencies: {}
            };
            fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
        }
        
        console.log('   ✅ Dependencies ready');
    }

    async runServiceTest(service) {
        const jestConfig = {
            preset: 'ts-jest',
            testEnvironment: 'node',
            roots: ['<rootDir>/src'],
            testMatch: [`**/${service.testFile}`],
            transform: {
                '^.+\\.ts$': 'ts-jest',
            },
            collectCoverageFrom: [service.sourceFile],
            coverageDirectory: `coverage/${service.name}`,
            coverageReporters: ['text', 'json', 'lcov'],
            testTimeout: 30000,
            verbose: false,
            collectCoverage: true,
            coverageThreshold: {
                global: {
                    branches: this.targetCoverage,
                    functions: this.targetCoverage,
                    lines: this.targetCoverage,
                    statements: this.targetCoverage
                }
            },
            moduleNameMapping: {
                '^vscode$': '<rootDir>/src/services/autocomplete/qaxNextEdit/services/__tests__/__mocks__/vscode.ts'
            },
            setupFilesAfterEnv: ['<rootDir>/src/services/autocomplete/qaxNextEdit/services/__tests__/setup.ts']
        };

        // Write temporary config
        const configFile = `jest.${service.name}.config.js`;
        fs.writeFileSync(configFile, `module.exports = ${JSON.stringify(jestConfig, null, 2)};`);

        return new Promise((resolve) => {
            const startTime = Date.now();
            
            const jestProcess = spawn('npx', [
                'jest',
                `--config=${configFile}`,
                '--coverage',
                '--silent'
            ], {
                stdio: 'pipe',
                shell: true,
                cwd: __dirname
            });

            let stdout = '';
            let stderr = '';

            jestProcess.stdout.on('data', (data) => {
                stdout += data.toString();
            });

            jestProcess.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            jestProcess.on('close', (code) => {
                const endTime = Date.now();
                const duration = endTime - startTime;

                // Clean up config file
                try {
                    fs.unlinkSync(configFile);
                } catch (error) {
                    // Ignore cleanup errors
                }

                // Parse coverage from output
                const coverage = this.parseCoverage(stdout);
                
                // Count tests
                const testStats = this.parseTestStats(stdout);

                const result = {
                    service: service.name,
                    success: code === 0,
                    duration,
                    coverage,
                    testStats,
                    stdout,
                    stderr
                };

                resolve(result);
            });
        });
    }

    parseCoverage(output) {
        const coverage = {
            statements: 0,
            branches: 0,
            functions: 0,
            lines: 0
        };

        // Try to extract coverage percentages from Jest output
        const coverageMatch = output.match(/All files\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)/);
        if (coverageMatch) {
            coverage.statements = parseFloat(coverageMatch[1]);
            coverage.branches = parseFloat(coverageMatch[2]);
            coverage.functions = parseFloat(coverageMatch[3]);
            coverage.lines = parseFloat(coverageMatch[4]);
        }

        return coverage;
    }

    parseTestStats(output) {
        const stats = {
            total: 0,
            passed: 0,
            failed: 0,
            suites: 0
        };

        // Extract test statistics
        const testMatch = output.match(/Tests:\s+(\d+)\s+passed,\s+(\d+)\s+total/);
        if (testMatch) {
            stats.passed = parseInt(testMatch[1]);
            stats.total = parseInt(testMatch[2]);
            stats.failed = stats.total - stats.passed;
        }

        const suiteMatch = output.match(/Test Suites:\s+(\d+)\s+passed,\s+(\d+)\s+total/);
        if (suiteMatch) {
            stats.suites = parseInt(suiteMatch[2]);
        }

        return stats;
    }

    displayServiceResult(result) {
        const { service, success, duration, coverage, testStats } = result;
        
        console.log(`\n📊 ${service} Results:`);
        
        if (success) {
            console.log('   ✅ Status: PASSED');
        } else {
            console.log('   ❌ Status: FAILED');
        }
        
        console.log(`   ⏱️  Duration: ${duration}ms`);
        console.log(`   🧪 Tests: ${testStats.passed}/${testStats.total} passed`);
        
        if (testStats.failed > 0) {
            console.log(`   ❌ Failed: ${testStats.failed}`);
        }
        
        console.log('\n   📈 Coverage:');
        console.log(`      Statements: ${coverage.statements.toFixed(1)}%`);
        console.log(`      Branches: ${coverage.branches.toFixed(1)}%`);
        console.log(`      Functions: ${coverage.functions.toFixed(1)}%`);
        console.log(`      Lines: ${coverage.lines.toFixed(1)}%`);
        
        const avgCoverage = (coverage.statements + coverage.branches + coverage.functions + coverage.lines) / 4;
        
        if (avgCoverage >= this.targetCoverage) {
            console.log(`   🎯 Coverage Target: ✅ ACHIEVED (${avgCoverage.toFixed(1)}%)`);
        } else {
            console.log(`   🎯 Coverage Target: ❌ BELOW TARGET (${avgCoverage.toFixed(1)}% < ${this.targetCoverage}%)`);
        }
    }

    generateFinalReport() {
        console.log('\n' + '='.repeat(60));
        console.log('📋 FINAL TEST REPORT');
        console.log('='.repeat(60));
        
        const totalTests = this.results.reduce((sum, r) => sum + r.testStats.total, 0);
        const totalPassed = this.results.reduce((sum, r) => sum + r.testStats.passed, 0);
        const totalFailed = this.results.reduce((sum, r) => sum + r.testStats.failed, 0);
        const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);
        
        const allPassed = this.results.every(r => r.success);
        const avgCoverages = this.results.map(r => {
            const { coverage } = r;
            return (coverage.statements + coverage.branches + coverage.functions + coverage.lines) / 4;
        });
        const overallAvgCoverage = avgCoverages.reduce((sum, c) => sum + c, 0) / avgCoverages.length;
        
        console.log('\n📊 Overall Statistics:');
        console.log(`   Services Tested: ${this.services.length}`);
        console.log(`   Total Tests: ${totalTests}`);
        console.log(`   Passed: ${totalPassed}`);
        console.log(`   Failed: ${totalFailed}`);
        console.log(`   Total Duration: ${(totalDuration / 1000).toFixed(1)}s`);
        console.log(`   Average Coverage: ${overallAvgCoverage.toFixed(1)}%`);
        
        console.log('\n🎯 Service Coverage Summary:');
        this.results.forEach(result => {
            const avgCov = (result.coverage.statements + result.coverage.branches + 
                           result.coverage.functions + result.coverage.lines) / 4;
            const status = avgCov >= this.targetCoverage ? '✅' : '❌';
            console.log(`   ${status} ${result.service}: ${avgCov.toFixed(1)}%`);
        });
        
        console.log('\n🏆 FINAL RESULT:');
        if (allPassed && overallAvgCoverage >= this.targetCoverage) {
            console.log('✅ ALL SERVICES PASSED WITH 95%+ COVERAGE!');
            console.log('🎉 QAX Services are fully tested and ready for production!');
            
            console.log('\n📁 Coverage Reports Generated:');
            this.services.forEach(service => {
                console.log(`   - coverage/${service.name}/lcov-report/index.html`);
            });
            
            process.exit(0);
        } else {
            console.log('❌ SOME SERVICES FAILED OR COVERAGE BELOW TARGET');
            console.log('🔧 Please review the results above and fix any issues');
            
            if (!allPassed) {
                console.log('\n💡 Failed Services:');
                this.results.filter(r => !r.success).forEach(r => {
                    console.log(`   - ${r.service}: Check test failures`);
                });
            }
            
            if (overallAvgCoverage < this.targetCoverage) {
                console.log('\n💡 Low Coverage Services:');
                this.results.forEach(r => {
                    const avgCov = (r.coverage.statements + r.coverage.branches + 
                                   r.coverage.functions + r.coverage.lines) / 4;
                    if (avgCov < this.targetCoverage) {
                        console.log(`   - ${r.service}: ${avgCov.toFixed(1)}% (need ${this.targetCoverage}%+)`);
                    }
                });
            }
            
            process.exit(1);
        }
    }
}

// Run the test suite
if (require.main === module) {
    const runner = new AllServiceTestRunner();
    runner.run().catch(error => {
        console.error('\n💥 Fatal error:', error.message);
        process.exit(1);
    });
}

module.exports = AllServiceTestRunner;
