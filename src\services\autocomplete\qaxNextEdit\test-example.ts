/**
 * QaxNextEdit 功能测试示例
 *
 * 这个文件用于测试 QaxNextEdit 的各种检测功能
 */

// 测试变量重命名检测
let oldVariableName = "test value"
console.log(oldVariableName)

// 测试函数参数变更检测
function testFunction(param1: string, param2: number) {
	return param1 + param2.toString()
}

// 函数调用
testFunction("hello", 42)
testFunction("world", 123)

// 测试函数调用删除检测
function deprecatedFunction() {
	return "deprecated"
}

deprecatedFunction() // 这个调用可能会被删除

// 测试类和方法
class TestClass {
	private value: string

	constructor(initialValue: string) {
		this.value = initialValue
	}

	getValue(): string {
		return this.value
	}

	setValue(newValue: string): void {
		this.value = newValue
	}
}

const instance = new TestClass("initial")
console.log(instance.getValue())
instance.setValue("updated")

// 测试导入（如果有的话）
// import { someFunction } from './other-module'

/**
 * 测试场景：
 *
 * 1. 变量重命名：
 *    - 将 oldVariableName 重命名为 newVariableName
 *    - QaxNextEdit 应该检测到这个变更并提供跳转到所有使用位置的建议
 *
 * 2. 函数参数变更：
 *    - 修改 testFunction 的参数（添加、删除或重命名参数）
 *    - QaxNextEdit 应该检测到变更并提供更新所有调用点的建议
 *
 * 3. 函数调用删除：
 *    - 删除 deprecatedFunction() 调用
 *    - QaxNextEdit 应该检测到删除并建议清理其他相关调用
 *
 * 4. 方法重命名：
 *    - 重命名 TestClass 的方法
 *    - QaxNextEdit 应该提供更新所有调用点的建议
 */

export { TestClass, testFunction }
