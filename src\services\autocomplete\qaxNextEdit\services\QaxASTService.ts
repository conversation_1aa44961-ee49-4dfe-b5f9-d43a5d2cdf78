import * as vscode from "vscode"
import { QaxASTNode, QaxChangeDetection, QaxChangeType } from "../types/QaxNextEditTypes"
import { getParserForFile, getAst } from "../../../tree-sitter/languageParser"

/**
 * AST 分析服务，用于解析代码并检测变量名修改、函数参数变更、函数调用删除等
 */
export class QaxASTService {
	private static instance: QaxASTService | null = null

	private constructor() {}

	public static getInstance(): QaxASTService {
		if (!QaxASTService.instance) {
			QaxASTService.instance = new QaxASTService()
		}
		return QaxASTService.instance
	}

	public static dispose(): void {
		if (QaxASTService.instance) {
			QaxASTService.instance = null
		}
	}

	/**
	 * 解析文档生成 AST
	 */
	async parseDocument(document: vscode.TextDocument): Promise<QaxASTNode | null> {
		try {
			// 检查是否是支持的文件类型
			if (!this.isSupportedFile(document.uri.fsPath, document.languageId)) {
				console.log(`QaxASTService: Unsupported file type for ${document.uri.fsPath} (${document.languageId})`)
				return null
			}

			const content = document.getText()
			if (!content || content.trim().length === 0) {
				console.log(`QaxASTService: Empty content for ${document.uri.fsPath}`)
				return null
			}

			// 使用正确的 tree-sitter 服务
			const ast = await getAst(document.uri.fsPath, content)
			if (!ast || !ast.rootNode) {
				// 不要输出警告，因为这是正常情况（某些文件类型不支持或解析器未加载）
				console.log(`QaxASTService: No AST available for ${document.uri.fsPath}`)
				return null
			}

			return this.convertTreeSitterNode(ast.rootNode, document)
		} catch (error) {
			console.warn(
				`QaxASTService: Failed to parse document ${document.uri.fsPath}, falling back to non-AST analysis:`,
				error,
			)
			return null
		}
	}

	/**
	 * 直接解析内容生成 AST，不创建临时文档
	 */
	async parseContentDirectly(content: string, filePath: string, languageId: string): Promise<QaxASTNode | null> {
		try {
			// 检查是否是支持的文件类型
			if (!this.isSupportedFile(filePath, languageId)) {
				console.log(`QaxASTService: Unsupported file type for ${filePath} (${languageId})`)
				return null
			}

			if (!content || content.trim().length === 0) {
				console.log(`QaxASTService: Empty content for ${filePath}`)
				return null
			}

			// 使用正确的 tree-sitter 服务
			const ast = await getAst(filePath, content)
			if (!ast || !ast.rootNode) {
				// 不要输出警告，因为这是正常情况（某些文件类型不支持或解析器未加载）
				console.log(`QaxASTService: No AST available for ${filePath}`)
				return null
			}

			// 创建一个虚拟文档对象用于节点转换
			const virtualDocument = {
				getText: (range?: vscode.Range) => {
					if (!range) return content
					// 简单的范围文本提取
					const lines = content.split('\n')
					const startLine = range.start.line
					const endLine = range.end.line
					const startChar = range.start.character
					const endChar = range.end.character

					if (startLine === endLine) {
						return lines[startLine]?.substring(startChar, endChar) || ''
					} else {
						const result = []
						for (let i = startLine; i <= endLine; i++) {
							if (i === startLine) {
								result.push(lines[i]?.substring(startChar) || '')
							} else if (i === endLine) {
								result.push(lines[i]?.substring(0, endChar) || '')
							} else {
								result.push(lines[i] || '')
							}
						}
						return result.join('\n')
					}
				}
			} as vscode.TextDocument

			return this.convertTreeSitterNode(ast.rootNode, virtualDocument)
		} catch (error) {
			console.warn(
				`QaxASTService: Failed to parse content directly for ${filePath}, falling back to non-AST analysis:`,
				error,
			)
			return null
		}
	}

	/**
	 * 检查是否是支持的文件类型
	 */
	private isSupportedFile(filePath: string, languageId: string): boolean {
		// 支持的语言 ID
		const supportedLanguages = [
			"javascript",
			"typescript",
			"python",
			"java",
			"cpp",
			"c",
			"csharp",
			"php",
			"ruby",
			"go",
			"rust",
			"swift",
			"kotlin",
			"scala",
		]

		// 支持的文件扩展名
		const supportedExtensions = [
			".js",
			".jsx",
			".ts",
			".tsx",
			".py",
			".java",
			".cpp",
			".c",
			".h",
			".hpp",
			".cs",
			".php",
			".rb",
			".go",
			".rs",
			".swift",
			".kt",
			".scala",
		]

		// 检查语言 ID
		if (supportedLanguages.includes(languageId)) {
			return true
		}

		// 检查文件扩展名
		const ext = filePath.toLowerCase().split(".").pop()
		return ext ? supportedExtensions.some((supported) => supported.endsWith(ext)) : false
	}

	/**
	 * 将 Tree-sitter 节点转换为 QaxASTNode
	 */
	private convertTreeSitterNode(node: any, document: vscode.TextDocument): QaxASTNode {
		const startPosition = new vscode.Position(node.startPosition.row, node.startPosition.column)
		const endPosition = new vscode.Position(node.endPosition.row, node.endPosition.column)
		const range = new vscode.Range(startPosition, endPosition)
		const text = document.getText(range)

		const result: QaxASTNode = {
			type: node.type,
			text,
			range,
			children: [],
			metadata: {
				fieldName: node.fieldName,
				childCount: node.childCount,
				namedChildCount: node.namedChildCount,
			},
		}

		// 递归处理子节点
		if (node.childCount > 0) {
			for (let i = 0; i < node.childCount; i++) {
				const child = node.child(i)
				const childNode = this.convertTreeSitterNode(child, document)
				childNode.parent = result
				result.children!.push(childNode)
			}
		}

		return result
	}

	/**
	 * 查找变量声明节点
	 */
	findVariableDeclarations(root: QaxASTNode): QaxASTNode[] {
		const declarations: QaxASTNode[] = []

		const traverse = (node: QaxASTNode) => {
			// 根据不同语言的 AST 结构查找变量声明
			if (
				node.type === "variable_declarator" ||
				node.type === "declaration" ||
				node.type === "let_declaration" ||
				node.type === "const_declaration" ||
				node.type === "var_declaration" ||
				node.type.includes("variable") ||
				node.type.includes("declaration")
			) {
				declarations.push(node)
			}

			// 递归遍历子节点
			if (node.children) {
				for (const child of node.children) {
					traverse(child)
				}
			}
		}

		traverse(root)
		return declarations
	}

	/**
	 * 查找函数声明节点
	 */
	findFunctionDeclarations(root: QaxASTNode): QaxASTNode[] {
		const declarations: QaxASTNode[] = []

		const traverse = (node: QaxASTNode) => {
			// 根据不同语言的 AST 结构查找函数声明
			if (
				node.type === "function_declaration" ||
				node.type === "method_definition" ||
				node.type === "function" ||
				node.type === "method" ||
				node.type === "arrow_function" ||
				node.type.includes("function") ||
				node.type.includes("method")
			) {
				declarations.push(node)
			}

			// 递归遍历子节点
			if (node.children) {
				for (const child of node.children) {
					traverse(child)
				}
			}
		}

		traverse(root)
		return declarations
	}

	/**
	 * 查找函数参数节点
	 */
	findFunctionParameters(functionNode: QaxASTNode): QaxASTNode[] {
		const parameters: QaxASTNode[] = []

		const traverse = (node: QaxASTNode) => {
			// 根据不同语言的 AST 结构查找函数参数
			if (
				node.type === "formal_parameters" ||
				node.type === "parameter_list" ||
				node.type === "parameters" ||
				node.type.includes("parameter")
			) {
				// 找到参数列表节点，获取其子节点
				if (node.children) {
					for (const child of node.children) {
						if (
							child.type === "identifier" ||
							child.type === "parameter" ||
							child.type === "formal_parameter" ||
							child.type.includes("parameter")
						) {
							parameters.push(child)
						}
					}
				}
				return // 找到参数列表后不需要继续遍历
			}

			// 递归遍历子节点
			if (node.children) {
				for (const child of node.children) {
					traverse(child)
				}
			}
		}

		traverse(functionNode)
		return parameters
	}

	/**
	 * 查找函数调用节点
	 */
	findFunctionCalls(root: QaxASTNode): QaxASTNode[] {
		const calls: QaxASTNode[] = []

		const traverse = (node: QaxASTNode) => {
			// 根据不同语言的 AST 结构查找函数调用
			if (
				node.type === "call_expression" ||
				node.type === "method_invocation" ||
				node.type === "function_call" ||
				node.type.includes("call")
			) {
				calls.push(node)
			}

			// 递归遍历子节点
			if (node.children) {
				for (const child of node.children) {
					traverse(child)
				}
			}
		}

		traverse(root)
		return calls
	}

	/**
	 * 检测变量重命名
	 */
	detectVariableRename(oldAst: QaxASTNode, newAst: QaxASTNode): QaxChangeDetection[] {
		const changes: QaxChangeDetection[] = []

		// 获取旧 AST 中的变量声明
		const oldDeclarations = this.findVariableDeclarations(oldAst)
		const newDeclarations = this.findVariableDeclarations(newAst)

		// 比较变量名
		// 这里使用简单的启发式方法，实际实现需要更复杂的比较逻辑
		for (const oldDecl of oldDeclarations) {
			const oldName = this.extractVariableName(oldDecl)
			if (!oldName) continue

			// 查找相似位置的新声明
			const newDecl = this.findSimilarPositionNode(oldDecl, newDeclarations)
			if (newDecl) {
				const newName = this.extractVariableName(newDecl)
				if (newName && oldName !== newName) {
					changes.push({
						type: QaxChangeType.VARIABLE_RENAME,
						filePath: "", // 需要在调用处填充
						range: newDecl.range,
						oldValue: oldName,
						newValue: newName,
						confidence: 0.85,
						metadata: {
							symbolName: oldName,
							symbolType: "variable",
							oldNode: oldDecl,
							newNode: newDecl,
						},
					})
				}
			}
		}

		return changes
	}

	/**
	 * 检测函数参数变更
	 */
	detectFunctionParameterChanges(oldAst: QaxASTNode, newAst: QaxASTNode): QaxChangeDetection[] {
		const changes: QaxChangeDetection[] = []

		// 获取旧 AST 中的函数声明
		const oldFunctions = this.findFunctionDeclarations(oldAst)
		const newFunctions = this.findFunctionDeclarations(newAst)

		// 比较函数参数
		for (const oldFunc of oldFunctions) {
			const oldParams = this.findFunctionParameters(oldFunc)

			// 查找相似位置的新函数
			const newFunc = this.findSimilarPositionNode(oldFunc, newFunctions)
			if (newFunc) {
				const newParams = this.findFunctionParameters(newFunc)

				// 检测参数数量变化
				if (oldParams.length !== newParams.length) {
					changes.push({
						type: QaxChangeType.FUNCTION_PARAMETER_CHANGE,
						filePath: "", // 需要在调用处填充
						range: newFunc.range,
						oldValue: oldParams.map((p) => p.text).join(", "),
						newValue: newParams.map((p) => p.text).join(", "),
						confidence: 0.9,
						metadata: {
							symbolName: this.extractFunctionName(oldFunc) || "",
							symbolType: "function",
							oldParamCount: oldParams.length,
							newParamCount: newParams.length,
							oldParams: oldParams,
							newParams: newParams,
						},
					})
				} else {
					// 检测参数名称变化
					for (let i = 0; i < oldParams.length; i++) {
						const oldParam = oldParams[i]
						const newParam = newParams[i]

						if (oldParam.text !== newParam.text) {
							changes.push({
								type: QaxChangeType.FUNCTION_PARAMETER_CHANGE,
								filePath: "", // 需要在调用处填充
								range: newParam.range,
								oldValue: oldParam.text,
								newValue: newParam.text,
								confidence: 0.85,
								metadata: {
									symbolName: this.extractFunctionName(oldFunc) || "",
									symbolType: "parameter",
									paramIndex: i,
								},
							})
						}
					}
				}
			}
		}

		return changes
	}

	/**
	 * 检测函数调用删除
	 */
	detectFunctionCallDeletions(oldAst: QaxASTNode, newAst: QaxASTNode): QaxChangeDetection[] {
		const changes: QaxChangeDetection[] = []

		// 获取旧 AST 中的函数调用
		const oldCalls = this.findFunctionCalls(oldAst)
		const newCalls = this.findFunctionCalls(newAst)

		// 查找在新 AST 中不存在的函数调用
		for (const oldCall of oldCalls) {
			const callName = this.extractFunctionCallName(oldCall)
			if (!callName) continue

			// 检查是否在新 AST 中找到相同的调用
			const found = newCalls.some((newCall) => {
				const newCallName = this.extractFunctionCallName(newCall)
				return newCallName === callName && this.isSimilarPosition(oldCall, newCall)
			})

			if (!found) {
				changes.push({
					type: QaxChangeType.FUNCTION_CALL_DELETION,
					filePath: "", // 需要在调用处填充
					range: oldCall.range,
					oldValue: oldCall.text,
					confidence: 0.8,
					metadata: {
						symbolName: callName,
						symbolType: "function_call",
					},
				})
			}
		}

		return changes
	}

	/**
	 * 提取变量名
	 */
	private extractVariableName(node: QaxASTNode): string | null {
		// 在子节点中查找标识符
		if (node.children) {
			for (const child of node.children) {
				if (child.type === "identifier") {
					return child.text
				}
			}
		}
		return null
	}

	/**
	 * 提取函数名
	 */
	private extractFunctionName(node: QaxASTNode): string | null {
		// 在子节点中查找标识符
		if (node.children) {
			for (const child of node.children) {
				if (child.type === "identifier") {
					return child.text
				}
			}
		}
		return null
	}

	/**
	 * 提取函数调用名
	 */
	private extractFunctionCallName(node: QaxASTNode): string | null {
		// 在子节点中查找标识符
		if (node.children) {
			for (const child of node.children) {
				if (child.type === "identifier" || child.type === "member_expression" || child.type === "property_identifier") {
					return child.text
				}
			}
		}
		return null
	}

	/**
	 * 查找位置相似的节点
	 */
	private findSimilarPositionNode(node: QaxASTNode, candidates: QaxASTNode[]): QaxASTNode | null {
		for (const candidate of candidates) {
			if (this.isSimilarPosition(node, candidate)) {
				return candidate
			}
		}
		return null
	}

	/**
	 * 判断两个节点位置是否相似
	 */
	private isSimilarPosition(node1: QaxASTNode, node2: QaxASTNode): boolean {
		// 简单的位置相似性判断，可以根据需要调整
		const lineDiff = Math.abs(node1.range.start.line - node2.range.start.line)
		return lineDiff <= 3 // 允许 3 行的差异
	}
}
