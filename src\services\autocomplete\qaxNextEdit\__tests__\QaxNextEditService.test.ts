import * as assert from "assert"
import * as vscode from "vscode"
import { QaxNextEditService } from "../QaxNextEditService"
import { QaxChangeType, DEFAULT_QAX_NEXT_EDIT_CONFIG } from "../types/QaxNextEditTypes"

// Mock VS Code API
const mockVscode = {
	workspace: {
		onDidChangeTextDocument: () => ({ dispose: () => {} }),
		onDidOpenTextDocument: () => ({ dispose: () => {} }),
		onDidCloseTextDocument: () => ({ dispose: () => {} }),
		getConfiguration: () => ({
			get: (key: string, defaultValue?: any) => defaultValue,
		}),
	},
	window: {
		onDidChangeActiveTextEditor: () => ({ dispose: () => {} }),
		createStatusBarItem: () => ({
			show: () => {},
			hide: () => {},
			dispose: () => {},
		}),
		createTextEditorDecorationType: () => ({
			dispose: () => {},
		}),
	},
	languages: {
		registerHoverProvider: () => ({ dispose: () => {} }),
	},
}

// Mock vscode module
Object.assign(vscode, mockVscode)

describe("QaxNextEditService", () => {
	let service: QaxNextEditService

	beforeEach(() => {
		// 清理之前的实例
		QaxNextEditService.dispose()
		service = QaxNextEditService.getInstance(DEFAULT_QAX_NEXT_EDIT_CONFIG)
	})

	afterEach(() => {
		service.dispose()
	})

	describe("Service Initialization", () => {
		it("should create a singleton instance", () => {
			const instance1 = QaxNextEditService.getInstance()
			const instance2 = QaxNextEditService.getInstance()
			assert.strictEqual(instance1, instance2)
		})

		it("should initialize with default config", () => {
			const config = service.getConfig()
			assert.strictEqual(config.enabled, true)
			assert.strictEqual(config.enableLSPIntegration, true)
			assert.strictEqual(config.enableASTAnalysis, true)
			assert.strictEqual(config.debounceDelayMs, 1500)
			assert.strictEqual(config.maxSuggestions, 8)
			assert.strictEqual(config.confidenceThreshold, 0.7)
		})

		it("should initialize with custom config", () => {
			QaxNextEditService.dispose()
			const customConfig = {
				...DEFAULT_QAX_NEXT_EDIT_CONFIG,
				debounceDelayMs: 2000,
				maxSuggestions: 5,
			}
			const customService = QaxNextEditService.getInstance(customConfig)
			const config = customService.getConfig()
			assert.strictEqual(config.debounceDelayMs, 2000)
			assert.strictEqual(config.maxSuggestions, 5)
			customService.dispose()
		})
	})

	describe("Service State Management", () => {
		it("should start with correct initial state", () => {
			const state = service.getState()
			assert.strictEqual(state.isEnabled, true)
			assert.strictEqual(state.isAnalyzing, false)
			assert.strictEqual(state.pendingChanges.size, 0)
			assert.strictEqual(state.cachedResults.size, 0)
		})

		it("should enable/disable service correctly", () => {
			service.setEnabled(false)
			let state = service.getState()
			assert.strictEqual(state.isEnabled, false)

			service.setEnabled(true)
			state = service.getState()
			assert.strictEqual(state.isEnabled, true)
		})

		it("should clear caches when disabled", () => {
			// 模拟一些缓存数据
			const state = service.getState()
			state.pendingChanges.set("test.ts", {} as any)
			state.cachedResults.set("test.ts", {} as any)

			service.setEnabled(false)
			const newState = service.getState()
			assert.strictEqual(newState.pendingChanges.size, 0)
			assert.strictEqual(newState.cachedResults.size, 0)
		})
	})

	describe("Configuration Management", () => {
		it("should update config correctly", () => {
			const updates = {
				debounceDelayMs: 3000,
				maxSuggestions: 10,
				confidenceThreshold: 0.8,
			}

			service.updateConfig(updates)
			const config = service.getConfig()

			assert.strictEqual(config.debounceDelayMs, 3000)
			assert.strictEqual(config.maxSuggestions, 10)
			assert.strictEqual(config.confidenceThreshold, 0.8)
		})

		it("should preserve other config values when updating", () => {
			const originalConfig = service.getConfig()
			const updates = { debounceDelayMs: 2500 }

			service.updateConfig(updates)
			const newConfig = service.getConfig()

			assert.strictEqual(newConfig.debounceDelayMs, 2500)
			assert.strictEqual(newConfig.enabled, originalConfig.enabled)
			assert.strictEqual(newConfig.enableLSPIntegration, originalConfig.enableLSPIntegration)
		})
	})

	describe("Event Management", () => {
		it("should add and remove event listeners", () => {
			let eventReceived = false
			const callback = () => {
				eventReceived = true
			}

			service.addEventListener(callback)
			// 触发一个事件来测试
			service.setEnabled(false)
			service.setEnabled(true)

			service.removeEventListener(callback)
			// 再次触发事件，应该不会被接收
			eventReceived = false
			service.setEnabled(false)
			service.setEnabled(true)
		})

		it("should handle multiple event listeners", () => {
			let count1 = 0
			let count2 = 0
			const callback1 = () => {
				count1++
			}
			const callback2 = () => {
				count2++
			}

			service.addEventListener(callback1)
			service.addEventListener(callback2)

			// 触发事件
			service.setEnabled(false)
			service.setEnabled(true)

			// 两个监听器都应该被调用
			assert.ok(count1 > 0)
			assert.ok(count2 > 0)
		})
	})

	describe("Analysis Results", () => {
		it("should return null for non-existent file", () => {
			const result = service.getAnalysisResult("non-existent.ts")
			assert.strictEqual(result, null)
		})

		it("should return empty suggestions for non-existent file", () => {
			const suggestions = service.getJumpSuggestions("non-existent.ts")
			assert.strictEqual(suggestions.length, 0)
		})

		it("should cache analysis results", () => {
			const mockResult = {
				detectedChanges: [],
				jumpSuggestions: [],
				analysisTime: 100,
				confidence: 0.8,
				metadata: {
					lspAvailable: true,
					astParsed: true,
					symbolsFound: 0,
					referencesFound: 0,
				},
			}

			// 手动设置缓存结果
			const state = service.getState()
			state.cachedResults.set("test.ts", mockResult)

			const result = service.getAnalysisResult("test.ts")
			assert.deepStrictEqual(result, mockResult)
		})
	})

	describe("Jump Suggestions", () => {
		it("should handle empty suggestions", () => {
			const suggestions = service.getJumpSuggestions("empty.ts")
			assert.strictEqual(suggestions.length, 0)
		})

		it("should return cached suggestions", () => {
			const mockSuggestions = [
				{
					id: "test-1",
					filePath: "test.ts",
					range: new vscode.Range(0, 0, 0, 10),
					description: "Test suggestion",
					changeType: QaxChangeType.VARIABLE_RENAME,
					priority: 5,
					relatedChange: {
						type: QaxChangeType.VARIABLE_RENAME,
						filePath: "test.ts",
						range: new vscode.Range(0, 0, 0, 10),
						oldValue: "oldName",
						newValue: "newName",
						confidence: 0.9,
					},
				},
			]

			const mockResult = {
				detectedChanges: [],
				jumpSuggestions: mockSuggestions,
				analysisTime: 100,
				confidence: 0.8,
				metadata: {
					lspAvailable: true,
					astParsed: true,
					symbolsFound: 1,
					referencesFound: 0,
				},
			}

			// 手动设置缓存结果
			const state = service.getState()
			state.cachedResults.set("test.ts", mockResult)

			const suggestions = service.getJumpSuggestions("test.ts")
			assert.strictEqual(suggestions.length, 1)
			assert.strictEqual(suggestions[0].id, "test-1")
			assert.strictEqual(suggestions[0].description, "Test suggestion")
		})
	})

	describe("Suggestion Management", () => {
		it("should ignore jump suggestion", () => {
			const mockSuggestion = {
				id: "test-1",
				filePath: "test.ts",
				range: new vscode.Range(0, 0, 0, 10),
				description: "Test suggestion",
				changeType: QaxChangeType.VARIABLE_RENAME,
				priority: 5,
				relatedChange: {
					type: QaxChangeType.VARIABLE_RENAME,
					filePath: "test.ts",
					range: new vscode.Range(0, 0, 0, 10),
					oldValue: "oldName",
					newValue: "newName",
					confidence: 0.9,
				},
			}

			const mockResult = {
				detectedChanges: [],
				jumpSuggestions: [mockSuggestion],
				analysisTime: 100,
				confidence: 0.8,
				metadata: {
					lspAvailable: true,
					astParsed: true,
					symbolsFound: 1,
					referencesFound: 0,
				},
			}

			// 设置缓存结果
			const state = service.getState()
			state.cachedResults.set("test.ts", mockResult)

			// 忽略建议
			service.ignoreJumpSuggestion(mockSuggestion)

			// 检查建议是否被移除
			const suggestions = service.getJumpSuggestions("test.ts")
			assert.strictEqual(suggestions.length, 0)
		})
	})

	describe("Resource Cleanup", () => {
		it("should dispose cleanly", () => {
			const state = service.getState()
			state.pendingChanges.set("test.ts", {} as any)
			state.cachedResults.set("test.ts", {} as any)

			service.dispose()

			// 检查资源是否被清理
			const newState = service.getState()
			assert.strictEqual(newState.pendingChanges.size, 0)
			assert.strictEqual(newState.cachedResults.size, 0)
		})

		it("should handle multiple dispose calls", () => {
			service.dispose()
			service.dispose() // 应该不会抛出错误
		})
	})
})
