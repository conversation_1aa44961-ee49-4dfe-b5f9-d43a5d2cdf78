"use strict"
var __createBinding =
	(this && this.__createBinding) ||
	(Object.create
		? function (o, m, k, k2) {
				if (k2 === undefined) k2 = k
				var desc = Object.getOwnPropertyDescriptor(m, k)
				if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
					desc = {
						enumerable: true,
						get: function () {
							return m[k]
						},
					}
				}
				Object.defineProperty(o, k2, desc)
			}
		: function (o, m, k, k2) {
				if (k2 === undefined) k2 = k
				o[k2] = m[k]
			})
var __setModuleDefault =
	(this && this.__setModuleDefault) ||
	(Object.create
		? function (o, v) {
				Object.defineProperty(o, "default", { enumerable: true, value: v })
			}
		: function (o, v) {
				o["default"] = v
			})
var __importStar =
	(this && this.__importStar) ||
	function (mod) {
		if (mod && mod.__esModule) return mod
		var result = {}
		if (mod != null)
			for (var k in mod)
				if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k)
		__setModuleDefault(result, mod)
		return result
	}
Object.defineProperty(exports, "__esModule", { value: true })
exports.loadRequiredLanguageParsers = loadRequiredLanguageParsers
exports.preloadAllParsers = preloadAllParsers
exports.getCachedParser = getCachedParser
exports.getParserForFile = getParserForFile
exports.getAst = getAst
exports.getTreePathAtCursor = getTreePathAtCursor
exports.getImportQuery = getImportQuery
const path = __importStar(require("path"))
const Parser = require("web-tree-sitter")
const queries_1 = require("./queries")
// Import the new import queries
const javascript_1 = require("./queries/javascript")
const typescript_1 = require("./queries/typescript")
const python_1 = require("./queries/python")
const rust_1 = require("./queries/rust")
const go_1 = require("./queries/go")
const java_1 = require("./queries/java")
const cpp_1 = require("./queries/cpp")
const c_1 = require("./queries/c")
const c_sharp_1 = require("./queries/c-sharp")
const ruby_1 = require("./queries/ruby")
const php_1 = require("./queries/php")
const swift_1 = require("./queries/swift")
const kotlin_1 = require("./queries/kotlin")
// Global cache for parsers
const parserCache = new Map()
let isParserInitialized = false
async function loadLanguage(langName) {
	// Try multiple possible paths for WASM files
	const possiblePaths = [
		path.join(__dirname, `tree-sitter-${langName}.wasm`), // Current directory (for compiled output)
		path.join(__dirname, "..", "..", "..", "dist", `tree-sitter-${langName}.wasm`), // dist directory from out/services/tree-sitter
		path.join(process.cwd(), "dist", `tree-sitter-${langName}.wasm`), // dist directory from workspace root
		path.join(__dirname, "..", "..", "..", "node_modules", "tree-sitter-wasms", "out", `tree-sitter-${langName}.wasm`), // node_modules fallback
	]
	for (const wasmPath of possiblePaths) {
		try {
			const fs = require("fs")
			if (fs.existsSync(wasmPath)) {
				return await Parser.Language.load(wasmPath)
			}
		} catch (error) {
			// Continue to next path
		}
	}
	// If all paths fail, throw an error with helpful information
	throw new Error(`Could not find WASM file for language: ${langName}. Tried paths: ${possiblePaths.join(", ")}`)
}
async function initializeParser() {
	if (!isParserInitialized) {
		await Parser.init()
		isParserInitialized = true
	}
}
// Language extension mapping
const LANGUAGE_EXTENSIONS = {
	js: "javascript",
	jsx: "javascript",
	ts: "typescript",
	tsx: "typescript", // tsx uses typescript parser
	py: "python",
	rs: "rust",
	go: "go",
	cpp: "cpp",
	cc: "cpp",
	cxx: "cpp",
	hpp: "cpp",
	c: "c",
	h: "c",
	cs: "c_sharp",
	rb: "ruby",
	java: "java",
	php: "php",
	swift: "swift",
	kt: "kotlin",
}
/*
Using node bindings for tree-sitter is problematic in vscode extensions
because of incompatibility with electron. Going the .wasm route has the
advantage of not having to build for multiple architectures.

We use web-tree-sitter and tree-sitter-wasms which provides auto-updating prebuilt WASM binaries for tree-sitter's language parsers.

This function loads WASM modules for relevant language parsers based on input files:
1. Extracts unique file extensions
2. Maps extensions to language names
3. Loads corresponding WASM files (containing grammar rules)
4. Uses WASM modules to initialize tree-sitter parsers

This approach optimizes performance by loading only necessary parsers once for all relevant files.

Sources:
- https://github.com/tree-sitter/node-tree-sitter/issues/169
- https://github.com/tree-sitter/node-tree-sitter/issues/168
- https://github.com/Gregoor/tree-sitter-wasms/blob/main/README.md
- https://github.com/tree-sitter/tree-sitter/blob/master/lib/binding_web/README.md
- https://github.com/tree-sitter/tree-sitter/blob/master/lib/binding_web/test/query-test.js
*/
async function loadRequiredLanguageParsers(filesToParse) {
	await initializeParser()
	const extensionsToLoad = new Set(filesToParse.map((file) => path.extname(file).toLowerCase().slice(1)))
	const parsers = {}
	for (const ext of Array.from(extensionsToLoad)) {
		let language
		let query
		switch (ext) {
			case "js":
			case "jsx":
				language = await loadLanguage("javascript")
				query = language.query(queries_1.javascriptQuery)
				break
			case "ts":
				language = await loadLanguage("typescript")
				query = language.query(queries_1.typescriptQuery)
				break
			case "tsx":
				language = await loadLanguage("typescript")
				query = language.query(queries_1.typescriptQuery)
				break
			case "py":
				language = await loadLanguage("python")
				query = language.query(queries_1.pythonQuery)
				break
			case "rs":
				language = await loadLanguage("rust")
				query = language.query(queries_1.rustQuery)
				break
			case "go":
				language = await loadLanguage("go")
				query = language.query(queries_1.goQuery)
				break
			case "cpp":
			case "cc":
			case "cxx":
			case "hpp":
				language = await loadLanguage("cpp")
				query = language.query(queries_1.cppQuery)
				break
			case "c":
			case "h":
				language = await loadLanguage("c")
				query = language.query(queries_1.cQuery)
				break
			case "cs":
				language = await loadLanguage("c_sharp")
				query = language.query(queries_1.csharpQuery)
				break
			case "rb":
				language = await loadLanguage("ruby")
				query = language.query(queries_1.rubyQuery)
				break
			case "java":
				language = await loadLanguage("java")
				query = language.query(queries_1.javaQuery)
				break
			case "php":
				language = await loadLanguage("php")
				query = language.query(queries_1.phpQuery)
				break
			case "swift":
				language = await loadLanguage("swift")
				query = language.query(queries_1.swiftQuery)
				break
			case "kt":
				language = await loadLanguage("kotlin")
				query = language.query(queries_1.kotlinQuery)
				break
			default:
				throw new Error(`Unsupported language: ${ext}`)
		}
		const parser = new Parser()
		parser.setLanguage(language)
		parsers[ext] = { parser, query }
	}
	return parsers
}
/**
 * Pre-loads all supported tree-sitter parsers during extension startup.
 * This improves performance by avoiding parser loading during completion requests.
 */
async function preloadAllParsers() {
	await initializeParser()
	console.log("🌳 Pre-loading tree-sitter parsers...")
	const supportedExtensions = Object.keys(LANGUAGE_EXTENSIONS)
	const loadPromises = supportedExtensions.map(async (ext) => {
		try {
			const langName = LANGUAGE_EXTENSIONS[ext]
			// Check if already cached
			if (parserCache.has(ext)) {
				return
			}
			const language = await loadLanguage(langName)
			const query = getQueryForLanguage(langName, language)
			const parser = new Parser()
			parser.setLanguage(language)
			parserCache.set(ext, { parser, query, language })
			console.log(`🌳 Loaded parser for .${ext} (${langName})`)
		} catch (error) {
			console.warn(`🌳 Failed to load parser for .${ext}:`, error)
		}
	})
	await Promise.all(loadPromises)
	console.log(`🌳 Pre-loaded ${parserCache.size} tree-sitter parsers`)
}
/**
 * Gets a cached parser for the given file extension.
 * Returns undefined if no parser is available for the extension.
 */
function getCachedParser(fileExtension) {
	const ext = fileExtension.toLowerCase().replace(".", "")
	return parserCache.get(ext)
}
/**
 * Gets a parser for a specific file path.
 * Uses cached parsers when available, falls back to on-demand loading.
 */
async function getParserForFile(filepath) {
	const ext = path.extname(filepath).toLowerCase().slice(1)
	// Try to get from cache first
	const cached = getCachedParser(ext)
	if (cached) {
		// Create a new parser instance to avoid conflicts
		const parser = new Parser()
		parser.setLanguage(cached.language)
		return parser
	}
	// Fall back to on-demand loading for unsupported extensions
	return undefined
}
function getQueryForLanguage(langName, language) {
	switch (langName) {
		case "javascript":
			return language.query(queries_1.javascriptQuery)
		case "typescript":
			return language.query(queries_1.typescriptQuery)
		case "python":
			return language.query(queries_1.pythonQuery)
		case "rust":
			return language.query(queries_1.rustQuery)
		case "go":
			return language.query(queries_1.goQuery)
		case "cpp":
			return language.query(queries_1.cppQuery)
		case "c":
			return language.query(queries_1.cQuery)
		case "c_sharp":
			return language.query(queries_1.csharpQuery)
		case "ruby":
			return language.query(queries_1.rubyQuery)
		case "java":
			return language.query(queries_1.javaQuery)
		case "php":
			return language.query(queries_1.phpQuery)
		case "swift":
			return language.query(queries_1.swiftQuery)
		case "kotlin":
			return language.query(queries_1.kotlinQuery)
		default:
			throw new Error(`Unsupported language: ${langName}`)
	}
}
/**
 * Gets the import query for a specific language.
 * This is separate from definition queries to allow focused import parsing.
 */
function getImportQueryForLanguage(langName, language) {
	try {
		switch (langName) {
			case "javascript":
				return language.query(javascript_1.importQuery)
			case "typescript":
				return language.query(typescript_1.importQuery)
			case "python":
				return language.query(python_1.importQuery)
			case "rust":
				return language.query(rust_1.importQuery)
			case "go":
				return language.query(go_1.importQuery)
			case "java":
				return language.query(java_1.importQuery)
			case "cpp":
				return language.query(cpp_1.importQuery)
			case "c":
				return language.query(c_1.importQuery)
			case "c_sharp":
				return language.query(c_sharp_1.importQuery)
			case "ruby":
				return language.query(ruby_1.importQuery)
			case "php":
				return language.query(php_1.importQuery)
			case "swift":
				return language.query(swift_1.importQuery)
			case "kotlin":
				return language.query(kotlin_1.importQuery)
			default:
				return null // Return null for unsupported languages instead of throwing
		}
	} catch (error) {
		console.warn(`Failed to create import query for ${langName}:`, error)
		return null
	}
}
/**
 * Creates an AST from file content using the appropriate parser.
 * Consolidated version of getAst functions from autocomplete utils.
 */
async function getAst(filepath, fileContents) {
	const parser = await getParserForFile(filepath)
	if (!parser) {
		return undefined
	}
	try {
		const ast = parser.parse(fileContents)
		return ast || undefined
	} catch (e) {
		return undefined
	}
}
/**
 * Gets the path from root to the node containing the cursor position.
 * Consolidated version of getTreePathAtCursor functions from autocomplete utils.
 */
async function getTreePathAtCursor(ast, cursorIndex) {
	const path = [ast.rootNode]
	while (path[path.length - 1].childCount > 0) {
		let foundChild = false
		for (const child of path[path.length - 1].children) {
			if (child && child.startIndex <= cursorIndex && child.endIndex >= cursorIndex) {
				path.push(child)
				foundChild = true
				break
			}
		}
		if (!foundChild) {
			break
		}
	}
	return path
}
/**
 * Gets an import query for a specific file.
 * This provides a consistent interface for import parsing across languages.
 */
async function getImportQuery(filepath) {
	const ext = path.extname(filepath).toLowerCase().slice(1)
	const cached = getCachedParser(ext)
	if (!cached) {
		return null
	}
	const langName = getLanguageNameFromFilepath(filepath)
	if (!langName) {
		return null
	}
	return getImportQueryForLanguage(langName, cached.language)
}
/**
 * Helper function to determine language name from file path.
 */
function getLanguageNameFromFilepath(filepath) {
	const extension = filepath.split(".").pop()?.toLowerCase()
	switch (extension) {
		case "js":
		case "jsx":
		case "mjs":
			return "javascript"
		case "ts":
		case "tsx":
			return "typescript"
		case "py":
		case "pyi":
			return "python"
		case "rs":
			return "rust"
		case "go":
			return "go"
		case "java":
			return "java"
		case "cpp":
		case "cc":
		case "cxx":
		case "c++":
			return "cpp"
		case "c":
		case "h":
			return "c"
		case "cs":
			return "c_sharp"
		case "rb":
			return "ruby"
		case "php":
			return "php"
		case "swift":
			return "swift"
		case "kt":
		case "kts":
			return "kotlin"
		default:
			return null
	}
}
