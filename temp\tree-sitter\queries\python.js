"use strict"
Object.defineProperty(exports, "__esModule", { value: true })
exports.importQuery = exports.definitionQuery = void 0
/*
- class definitions
- function definitions
*/
exports.definitionQuery = `
(class_definition
  name: (identifier) @name.definition.class) @definition.class

(function_definition
  name: (identifier) @name.definition.function) @definition.function
`
/*
- Python import statements
- from imports, direct imports, relative imports
*/
exports.importQuery = `
; from module import symbol
(import_from_statement
  module_name: (dotted_name) @import.source
  name: (dotted_name) @import.name) @import.statement

; from module import symbol as alias
(import_from_statement
  module_name: (dotted_name) @import.source
  name: (aliased_import
    name: (dotted_name) @import.name
    alias: (identifier) @import.alias)) @import.statement

; import module
(import_statement
  name: (dotted_name) @import.name) @import.statement

; import module as alias
(import_statement
  name: (aliased_import
    name: (dotted_name) @import.name
    alias: (identifier) @import.alias)) @import.statement

; Relative imports: from .module import symbol
(import_from_statement
  module_name: (relative_import
    (dotted_name) @import.source)
  name: (dotted_name) @import.name) @import.statement
`
// Default export for backward compatibility
exports.default = exports.definitionQuery
