"use strict"
var __createBinding =
	(this && this.__createBinding) ||
	(Object.create
		? function (o, m, k, k2) {
				if (k2 === undefined) k2 = k
				var desc = Object.getOwnPropertyDescriptor(m, k)
				if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
					desc = {
						enumerable: true,
						get: function () {
							return m[k]
						},
					}
				}
				Object.defineProperty(o, k2, desc)
			}
		: function (o, m, k, k2) {
				if (k2 === undefined) k2 = k
				o[k2] = m[k]
			})
var __setModuleDefault =
	(this && this.__setModuleDefault) ||
	(Object.create
		? function (o, v) {
				Object.defineProperty(o, "default", { enumerable: true, value: v })
			}
		: function (o, v) {
				o["default"] = v
			})
var __importStar =
	(this && this.__importStar) ||
	function (mod) {
		if (mod && mod.__esModule) return mod
		var result = {}
		if (mod != null)
			for (var k in mod)
				if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k)
		__setModuleDefault(result, mod)
		return result
	}
Object.defineProperty(exports, "__esModule", { value: true })
exports.QaxNextEditUIProvider = void 0
const vscode = __importStar(require("vscode"))
/**
 * QaxNextEdit UI 提供者，管理跳转建议的显示和交互
 */
class QaxNextEditUIProvider {
	constructor() {
		this.currentSuggestions = []
		this.currentSuggestionIndex = 0
		this.currentEditor = null
		this.disposables = []
		this.hoverProvider = null
		// 锚点检测和悬停功能
		this.anchorRanges = new Map()
		this.isHoveringAnchor = false
		this.hoverTimeout = null
		// 创建高亮装饰类型
		this.decorationType = vscode.window.createTextEditorDecorationType({
			backgroundColor: new vscode.ThemeColor("editor.selectionBackground"),
			border: "2px solid",
			borderColor: new vscode.ThemeColor("editor.selectionHighlightBorder"),
			borderRadius: "4px",
		})
		// 创建浮动覆盖装饰类型
		this.overlayDecorationType = vscode.window.createTextEditorDecorationType({
			after: {
				contentText: " 🔍 QaxNextEdit",
				backgroundColor: new vscode.ThemeColor("editorWidget.background"),
				border: "1px solid",
				borderColor: new vscode.ThemeColor("editorWidget.border"),
				margin: "0 0 0 10px",
				color: new vscode.ThemeColor("editorWidget.foreground"),
				fontWeight: "bold",
			},
		})
		// 创建状态栏项
		this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100)
		this.statusBarItem.command = "qaxNextEdit.showSuggestions"
		this.statusBarItem.tooltip = "QaxNextEdit: Click to show suggestions"
		this.setupEventHandlers()
	}
	/**
	 * 设置事件处理器
	 */
	setupEventHandlers() {
		// 监听编辑器变更
		this.disposables.push(
			vscode.window.onDidChangeActiveTextEditor((editor) => {
				this.currentEditor = editor || null
				this.updateStatusBar()
			}),
		)
		// 监听文档变更
		this.disposables.push(
			vscode.workspace.onDidChangeTextDocument(() => {
				this.updateStatusBar()
			}),
		)
	}
	/**
	 * 显示跳转建议
	 */
	async showSuggestions(suggestions) {
		console.log(`📋 QaxNextEdit: Received ${suggestions.length} jump suggestions`)
		this.currentSuggestions = suggestions
		this.currentSuggestionIndex = 0
		if (suggestions.length === 0) {
			this.clearUI()
			return
		}
		// 注册锚点范围用于悬停检测
		await this.registerAnchorRanges(suggestions)
		// 更新状态栏
		this.updateStatusBar()
		// 显示第一个建议
		if (suggestions.length > 0) {
			await this.showSuggestion(suggestions[0])
		}
	}
	/**
	 * 显示单个建议
	 */
	async showSuggestion(suggestion) {
		try {
			// 打开文件
			const document = await vscode.workspace.openTextDocument(suggestion.filePath)
			const editor = await vscode.window.showTextDocument(document)
			this.currentEditor = editor
			// 高亮建议位置
			const decorations = [
				{
					range: suggestion.range,
					hoverMessage: new vscode.MarkdownString(
						`**QaxNextEdit Suggestion**\n\n${suggestion.description}\n\n` +
							(suggestion.suggestedEdit
								? `**Suggested Change:**\n\`\`\`\n${suggestion.suggestedEdit.newText}\n\`\`\``
								: "") +
							`\n\n[Apply](command:qaxNextEdit.applySuggestion?${encodeURIComponent(JSON.stringify(suggestion))}) | ` +
							`[Ignore](command:qaxNextEdit.ignoreSuggestion?${encodeURIComponent(JSON.stringify(suggestion))})`,
					),
				},
			]
			editor.setDecorations(this.decorationType, decorations)
			// 跳转到位置
			editor.selection = new vscode.Selection(suggestion.range.start, suggestion.range.end)
			editor.revealRange(suggestion.range, vscode.TextEditorRevealType.InCenter)
			console.log(`📍 QaxNextEdit: Showing suggestion at ${suggestion.filePath}:${suggestion.range.start.line + 1}`)
		} catch (error) {
			console.error("QaxNextEdit: Failed to show suggestion:", error)
		}
	}
	/**
	 * 注册锚点范围
	 */
	async registerAnchorRanges(suggestions) {
		this.anchorRanges.clear()
		for (const suggestion of suggestions) {
			const key = `${suggestion.filePath}:${suggestion.range.start.line}:${suggestion.range.start.character}`
			this.anchorRanges.set(key, {
				range: suggestion.range,
				suggestion,
			})
		}
		// 设置悬停提供者
		this.setupHoverProvider()
	}
	/**
	 * 设置悬停提供者
	 */
	setupHoverProvider() {
		if (this.hoverProvider) {
			this.hoverProvider.dispose()
		}
		this.hoverProvider = vscode.languages.registerHoverProvider("*", {
			provideHover: (document, position) => {
				const key = `${document.uri.fsPath}:${position.line}:${position.character}`
				const anchor = this.anchorRanges.get(key)
				if (anchor) {
					const suggestion = anchor.suggestion
					const markdown = new vscode.MarkdownString()
					markdown.isTrusted = true
					markdown.appendMarkdown(`**QaxNextEdit Suggestion**\n\n`)
					markdown.appendMarkdown(`${suggestion.description}\n\n`)
					if (suggestion.suggestedEdit) {
						markdown.appendMarkdown(`**Suggested Change:**\n`)
						markdown.appendCodeblock(suggestion.suggestedEdit.newText, "typescript")
						markdown.appendMarkdown(`\n`)
					}
					markdown.appendMarkdown(`Priority: ${suggestion.priority}/10\n\n`)
					// 添加操作按钮
					const applyCommand = `command:qaxNextEdit.applySuggestion?${encodeURIComponent(JSON.stringify(suggestion))}`
					const ignoreCommand = `command:qaxNextEdit.ignoreSuggestion?${encodeURIComponent(JSON.stringify(suggestion))}`
					markdown.appendMarkdown(`[Apply](${applyCommand}) | [Ignore](${ignoreCommand})`)
					return new vscode.Hover(markdown, anchor.range)
				}
				return null
			},
		})
		this.disposables.push(this.hoverProvider)
	}
	/**
	 * 导航到下一个建议
	 */
	navigateToNextSuggestion() {
		if (this.currentSuggestions.length === 0) return
		this.currentSuggestionIndex = (this.currentSuggestionIndex + 1) % this.currentSuggestions.length
		this.showSuggestion(this.currentSuggestions[this.currentSuggestionIndex])
		this.updateStatusBar()
	}
	/**
	 * 导航到上一个建议
	 */
	navigateToPreviousSuggestion() {
		if (this.currentSuggestions.length === 0) return
		this.currentSuggestionIndex =
			(this.currentSuggestionIndex - 1 + this.currentSuggestions.length) % this.currentSuggestions.length
		this.showSuggestion(this.currentSuggestions[this.currentSuggestionIndex])
		this.updateStatusBar()
	}
	/**
	 * 获取当前建议
	 */
	getCurrentSuggestion() {
		if (this.currentSuggestions.length === 0) return null
		return this.currentSuggestions[this.currentSuggestionIndex]
	}
	/**
	 * 更新状态栏
	 */
	updateStatusBar() {
		if (this.currentSuggestions.length === 0) {
			this.statusBarItem.hide()
			return
		}
		const current = this.currentSuggestionIndex + 1
		const total = this.currentSuggestions.length
		this.statusBarItem.text = `$(search) QaxNextEdit: ${current}/${total}`
		this.statusBarItem.show()
	}
	/**
	 * 清理 UI
	 */
	clearUI() {
		// 清除装饰
		if (this.currentEditor) {
			this.currentEditor.setDecorations(this.decorationType, [])
			this.currentEditor.setDecorations(this.overlayDecorationType, [])
		}
		// 隐藏状态栏
		this.statusBarItem.hide()
		// 清除数据
		this.currentSuggestions = []
		this.currentSuggestionIndex = 0
		this.anchorRanges.clear()
		// 清除悬停提供者
		if (this.hoverProvider) {
			this.hoverProvider.dispose()
			this.hoverProvider = null
		}
		console.log("📋 QaxNextEdit: UI cleared")
	}
	/**
	 * 设置事件回调
	 */
	setEventCallback(callback) {
		this.eventCallback = callback
	}
	/**
	 * 发送事件
	 */
	emitEvent(event) {
		if (this.eventCallback) {
			this.eventCallback(event)
		}
	}
	/**
	 * 清理资源
	 */
	dispose() {
		this.clearUI()
		this.disposables.forEach((d) => d.dispose())
		this.disposables = []
		this.decorationType.dispose()
		this.overlayDecorationType.dispose()
		this.statusBarItem.dispose()
		if (this.hoverProvider) {
			this.hoverProvider.dispose()
		}
		if (this.hoverTimeout) {
			clearTimeout(this.hoverTimeout)
		}
	}
}
exports.QaxNextEditUIProvider = QaxNextEditUIProvider
