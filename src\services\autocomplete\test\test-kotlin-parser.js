const { loadRequiredLanguageParsers } = require("./out/services/tree-sitter/languageParser.js")

async function testKotlinParser() {
	console.log("Testing Kotlin parser...")

	try {
		// Test loading the Kotlin parser
		console.log("Loading Kotlin parser...")
		const parsers = await loadRequiredLanguageParsers(["test.kt"])

		if (parsers.kt) {
			console.log("✅ Kotlin parser loaded successfully!")
			console.log("Parser:", parsers.kt.parser)
			console.log("Query:", parsers.kt.query)

			// Test parsing some simple Kotlin code
			const kotlinCode = `
class MyClass {
    fun myFunction(): String {
        return "Hello, World!"
    }

    val myProperty: Int = 42
}

object MyObject {
    const val CONSTANT = "test"
}

typealias StringList = List<String>

interface MyInterface {
    fun interfaceMethod()
}
`

			console.log("Parsing Kotlin code...")
			const tree = parsers.kt.parser.parse(kotlinCode)
			console.log("✅ Code parsed successfully!")
			console.log("Root node:", tree.rootNode.type)
			console.log("Child count:", tree.rootNode.childCount)

			// Test the query
			console.log("Testing query...")
			const captures = parsers.kt.query.captures(tree.rootNode)
			console.log("✅ Query executed successfully!")
			console.log("Found captures:", captures.length)

			captures.forEach((capture, index) => {
				console.log(`  ${index + 1}. ${capture.name}: ${capture.node.text} (${capture.node.type})`)
			})

			// Additional debug: Traverse the tree to find interface node type
			console.log("Traversing tree for interface node type...")
			function traverse(node) {
				if (node.text.includes("interface MyInterface")) {
					console.log(`Found interface node: ${node.text} (Type: ${node.type})`)
				}
				for (let i = 0; i < node.childCount; i++) {
					traverse(node.child(i))
				}
			}
			traverse(tree.rootNode)
		} else {
			console.log("❌ Kotlin parser not found in loaded parsers")
		}
	} catch (error) {
		console.error("❌ Error testing Kotlin parser:", error.message)
		console.error("Stack:", error.stack)
	}
}

testKotlinParser().catch(console.error)
