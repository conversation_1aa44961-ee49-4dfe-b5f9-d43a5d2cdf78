import * as vscode from 'vscode'
import { QaxSymbolChange } from '../types/QaxNextEditTypes'

/**
 * 符号级别的变更检测器
 * 以符号和语句为基本单元进行检测，而不是字符级别
 */
export class QaxSymbolDetector {
	private readonly IDENTIFIER_REGEX = /\b[a-zA-Z_$][a-zA-Z0-9_$]*\b/g
	private readonly FUNCTION_CALL_REGEX = /\b[a-zA-Z_$][a-zA-Z0-9_$]*\s*\(/g
	private readonly STRING_LITERAL_REGEX = /(['"`])(?:(?!\1)[^\\]|\\.)*\1/g
	private readonly NUMBER_LITERAL_REGEX = /\b\d+(?:\.\d+)?(?:[eE][+-]?\d+)?\b/g

	/**
	 * 检测文档中的符号
	 */
	detectSymbols(document: vscode.TextDocument): QaxSymbolChange[] {
		const symbols: QaxSymbolChange[] = []
		const text = document.getText()

		// 检测标识符
		this.detectIdentifiers(document, text, symbols)
		
		// 检测函数调用
		this.detectFunctionCalls(document, text, symbols)
		
		// 检测字符串字面量
		this.detectStringLiterals(document, text, symbols)
		
		// 检测数字字面量
		this.detectNumberLiterals(document, text, symbols)

		return symbols
	}

	/**
	 * 比较两个文档的符号差异
	 */
	compareSymbols(beforeDoc: vscode.TextDocument, afterDoc: vscode.TextDocument): QaxSymbolChange[] {
		const changes: QaxSymbolChange[] = []

		// 获取两个文档的符号
		const beforeSymbols = this.detectSymbols(beforeDoc)
		const afterSymbols = this.detectSymbols(afterDoc)

		console.log(`🔍 QaxSymbolDetector: Before symbols: ${beforeSymbols.length}, After symbols: ${afterSymbols.length}`)

		// 使用更智能的符号匹配算法
		const matchedPairs = this.matchSymbols(beforeSymbols, afterSymbols)

		for (const { before, after } of matchedPairs) {
			if (before && after) {
				// 符号被修改
				if (before.oldText !== after.oldText) {
					changes.push({
						symbolType: before.symbolType,
						range: before.range,
						oldText: before.oldText,
						newText: after.oldText,
						symbolName: before.symbolName,
						context: before.context
					})
				}
			} else if (before && !after) {
				// 符号被删除
				changes.push({
					symbolType: before.symbolType,
					range: before.range,
					oldText: before.oldText,
					newText: '',
					symbolName: before.symbolName,
					context: before.context
				})
			} else if (!before && after) {
				// 符号被新增
				changes.push({
					symbolType: after.symbolType,
					range: after.range,
					oldText: '',
					newText: after.oldText,
					symbolName: after.symbolName,
					context: after.context
				})
			}
		}

		console.log(`🔍 QaxSymbolDetector: Found ${changes.length} symbol changes`)
		return changes
	}

	/**
	 * 智能匹配两个符号列表
	 */
	private matchSymbols(beforeSymbols: QaxSymbolChange[], afterSymbols: QaxSymbolChange[]): Array<{before: QaxSymbolChange | null, after: QaxSymbolChange | null}> {
		const matches: Array<{before: QaxSymbolChange | null, after: QaxSymbolChange | null}> = []
		const usedAfterIndices = new Set<number>()

		// 首先尝试按位置匹配
		for (const beforeSymbol of beforeSymbols) {
			let bestMatch: {symbol: QaxSymbolChange, index: number} | null = null
			let bestScore = 0

			for (let i = 0; i < afterSymbols.length; i++) {
				if (usedAfterIndices.has(i)) continue

				const afterSymbol = afterSymbols[i]
				const score = this.calculateMatchScore(beforeSymbol, afterSymbol)

				if (score > bestScore && score > 0.5) { // 只接受相似度超过50%的匹配
					bestScore = score
					bestMatch = {symbol: afterSymbol, index: i}
				}
			}

			if (bestMatch) {
				matches.push({before: beforeSymbol, after: bestMatch.symbol})
				usedAfterIndices.add(bestMatch.index)
			} else {
				matches.push({before: beforeSymbol, after: null})
			}
		}

		// 添加未匹配的新符号
		for (let i = 0; i < afterSymbols.length; i++) {
			if (!usedAfterIndices.has(i)) {
				matches.push({before: null, after: afterSymbols[i]})
			}
		}

		return matches
	}

	/**
	 * 计算两个符号的匹配分数
	 */
	private calculateMatchScore(symbol1: QaxSymbolChange, symbol2: QaxSymbolChange): number {
		let score = 0

		// 符号类型必须匹配
		if (symbol1.symbolType !== symbol2.symbolType) {
			return 0
		}

		// 位置相似度（权重：40%）
		const positionScore = this.calculatePositionSimilarity(symbol1.range, symbol2.range)
		score += positionScore * 0.4

		// 文本相似度（权重：60%）
		const textScore = this.calculateTextSimilarity(symbol1.oldText, symbol2.oldText)
		score += textScore * 0.6

		return score
	}

	/**
	 * 计算位置相似度
	 */
	private calculatePositionSimilarity(range1: vscode.Range, range2: vscode.Range): number {
		// 如果在同一行，相似度较高
		if (range1.start.line === range2.start.line) {
			const charDiff = Math.abs(range1.start.character - range2.start.character)
			return Math.max(0, 1 - charDiff / 100) // 字符差异越小，相似度越高
		}

		// 不同行的相似度较低
		const lineDiff = Math.abs(range1.start.line - range2.start.line)
		return Math.max(0, 1 - lineDiff / 10) // 行差异越小，相似度越高
	}

	/**
	 * 计算文本相似度
	 */
	private calculateTextSimilarity(text1: string, text2: string): number {
		if (text1 === text2) return 1.0
		if (text1.length === 0 || text2.length === 0) return 0.0

		// 使用简单的编辑距离算法
		const maxLen = Math.max(text1.length, text2.length)
		const editDistance = this.calculateEditDistance(text1, text2)
		return (maxLen - editDistance) / maxLen
	}

	/**
	 * 计算编辑距离
	 */
	private calculateEditDistance(str1: string, str2: string): number {
		const matrix: number[][] = []
		const len1 = str1.length
		const len2 = str2.length

		for (let i = 0; i <= len1; i++) {
			matrix[i] = [i]
		}

		for (let j = 0; j <= len2; j++) {
			matrix[0][j] = j
		}

		for (let i = 1; i <= len1; i++) {
			for (let j = 1; j <= len2; j++) {
				if (str1[i - 1] === str2[j - 1]) {
					matrix[i][j] = matrix[i - 1][j - 1]
				} else {
					matrix[i][j] = Math.min(
						matrix[i - 1][j] + 1,    // deletion
						matrix[i][j - 1] + 1,    // insertion
						matrix[i - 1][j - 1] + 1 // substitution
					)
				}
			}
		}

		return matrix[len1][len2]
	}

	/**
	 * 获取指定位置的符号
	 */
	getSymbolAtPosition(document: vscode.TextDocument, position: vscode.Position): QaxSymbolChange | null {
		const symbols = this.detectSymbols(document)
		
		for (const symbol of symbols) {
			if (symbol.range.contains(position)) {
				return symbol
			}
		}
		
		return null
	}

	/**
	 * 检测标识符
	 */
	private detectIdentifiers(document: vscode.TextDocument, text: string, symbols: QaxSymbolChange[]): void {
		let match: RegExpExecArray | null
		this.IDENTIFIER_REGEX.lastIndex = 0

		while ((match = this.IDENTIFIER_REGEX.exec(text)) !== null) {
			const startPos = document.positionAt(match.index)
			const endPos = document.positionAt(match.index + match[0].length)
			const range = new vscode.Range(startPos, endPos)

			symbols.push({
				symbolType: 'identifier',
				range,
				oldText: match[0],
				newText: match[0],
				symbolName: match[0],
				context: {
					scope: this.getScope(document, startPos)
				}
			})
		}
	}

	/**
	 * 检测函数调用
	 */
	private detectFunctionCalls(document: vscode.TextDocument, text: string, symbols: QaxSymbolChange[]): void {
		let match: RegExpExecArray | null
		this.FUNCTION_CALL_REGEX.lastIndex = 0

		while ((match = this.FUNCTION_CALL_REGEX.exec(text)) !== null) {
			const functionName = match[0].replace(/\s*\($/, '')
			const startPos = document.positionAt(match.index)
			const endPos = document.positionAt(match.index + functionName.length)
			const range = new vscode.Range(startPos, endPos)

			symbols.push({
				symbolType: 'function_call',
				range,
				oldText: functionName,
				newText: functionName,
				symbolName: functionName,
				context: {
					scope: this.getScope(document, startPos)
				}
			})
		}
	}

	/**
	 * 检测字符串字面量
	 */
	private detectStringLiterals(document: vscode.TextDocument, text: string, symbols: QaxSymbolChange[]): void {
		let match: RegExpExecArray | null
		this.STRING_LITERAL_REGEX.lastIndex = 0

		while ((match = this.STRING_LITERAL_REGEX.exec(text)) !== null) {
			const startPos = document.positionAt(match.index)
			const endPos = document.positionAt(match.index + match[0].length)
			const range = new vscode.Range(startPos, endPos)

			symbols.push({
				symbolType: 'string_literal',
				range,
				oldText: match[0],
				newText: match[0]
			})
		}
	}

	/**
	 * 检测数字字面量
	 */
	private detectNumberLiterals(document: vscode.TextDocument, text: string, symbols: QaxSymbolChange[]): void {
		let match: RegExpExecArray | null
		this.NUMBER_LITERAL_REGEX.lastIndex = 0

		while ((match = this.NUMBER_LITERAL_REGEX.exec(text)) !== null) {
			const startPos = document.positionAt(match.index)
			const endPos = document.positionAt(match.index + match[0].length)
			const range = new vscode.Range(startPos, endPos)

			symbols.push({
				symbolType: 'number_literal',
				range,
				oldText: match[0],
				newText: match[0]
			})
		}
	}



	/**
	 * 获取位置的作用域信息
	 */
	private getScope(document: vscode.TextDocument, position: vscode.Position): string {
		// 简单的作用域检测，可以根据需要扩展
		const line = document.lineAt(position.line).text
		
		// 检测是否在函数内
		if (line.includes('function') || line.includes('=>')) {
			return 'function'
		}
		
		// 检测是否在类内
		if (line.includes('class')) {
			return 'class'
		}
		
		return 'global'
	}
}
