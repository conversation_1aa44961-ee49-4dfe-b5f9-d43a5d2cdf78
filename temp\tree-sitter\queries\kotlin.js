"use strict"
Object.defineProperty(exports, "__esModule", { value: true })
exports.importQuery = exports.definitionQuery = void 0
/*
- class declarations
- function declarations
- object declarations
- property declarations
- enum entries
- annotations
*/
exports.definitionQuery = `
(class_declaration
  (type_identifier) @name.definition.class) @definition.class

(function_declaration
  (simple_identifier) @name.definition.function) @definition.function



(object_declaration
  (type_identifier) @name.definition.object) @definition.object

(property_declaration
  (simple_identifier) @name.definition.property) @definition.property



(enum_entry
  (simple_identifier) @name.definition.enum_entry) @definition.enum_entry



(annotation
  (type_identifier) @name.definition.annotation) @definition.annotation
`
/*
- Kotlin import statements
*/
exports.importQuery = `
; import package.Class
(import_header
  (identifier) @import.name) @import.statement

; import package.Class as Alias
(import_header
  (import_alias
    (identifier) @import.name
    (identifier) @import.alias)) @import.statement

; import package.*
(import_header
  (identifier) @import.source) @import.statement
`
// Default export for backward compatibility
exports.default = exports.definitionQuery
