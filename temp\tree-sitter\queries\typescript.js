"use strict"
Object.defineProperty(exports, "__esModule", { value: true })
exports.importQuery = exports.definitionQuery = void 0
/*
- function signatures and declarations
- method signatures and definitions
- abstract method signatures
- class declarations (including abstract classes)
- module declarations
*/
exports.definitionQuery = `
(function_signature
  name: (identifier) @name.definition.function) @definition.function

(method_signature
  name: (property_identifier) @name.definition.method) @definition.method

(abstract_method_signature
  name: (property_identifier) @name.definition.method) @definition.method

(abstract_class_declaration
  name: (type_identifier) @name.definition.class) @definition.class

(module
  name: (identifier) @name.definition.module) @definition.module

(function_declaration
  name: (identifier) @name.definition.function) @definition.function

(method_definition
  name: (property_identifier) @name.definition.method) @definition.method

(class_declaration
  name: (type_identifier) @name.definition.class) @definition.class
`
/*
- TypeScript import statements (extends JavaScript imports)
- Type-only imports and mixed imports
*/
exports.importQuery = `
; Named imports: import { foo, bar } from 'module'
(import_statement
  (import_clause
    (named_imports
      (import_specifier
        name: (identifier) @import.name))) @import.statement
  source: (string) @import.source) @import.full_statement

; Type-only imports: import type { Foo } from 'module'
(import_statement
  (import_clause
    (named_imports
      (import_specifier
        name: (identifier) @import.name))) @import.statement
  source: (string) @import.source) @import.full_statement

; Default imports: import foo from 'module'
(import_statement
  (import_clause
    (identifier) @import.name) @import.statement
  source: (string) @import.source) @import.full_statement

; Namespace imports: import * as foo from 'module'
(import_statement
  (import_clause
    (namespace_import
      (identifier) @import.namespace_name)) @import.statement
  source: (string) @import.source) @import.namespace_statement

; Side-effect imports: import 'module'
(import_statement
  source: (string) @import.source) @import.side_effect_statement

; CommonJS require: const foo = require('module')
(lexical_declaration
  (variable_declarator
    name: (identifier) @import.name
    value: (call_expression
      function: (identifier) @import.method
      arguments: (arguments (string) @import.source)))) @import.require_statement
  (#eq? @import.method "require")

; CommonJS require with var: var foo = require('module')
(variable_declaration
  (variable_declarator
    name: (identifier) @import.name
    value: (call_expression
      function: (identifier) @import.method
      arguments: (arguments (string) @import.source)))) @import.require_statement
  (#eq? @import.method "require")

; CommonJS destructuring: const { foo, bar } = require('module')
(lexical_declaration
  (variable_declarator
    name: (object_pattern
      (shorthand_property_identifier_pattern) @import.name)
    value: (call_expression
      function: (identifier) @import.method
      arguments: (arguments (string) @import.source)))) @import.statement
  (#eq? @import.method "require")

; CommonJS destructuring with var: var { foo, bar } = require('module')
(variable_declaration
  (variable_declarator
    name: (object_pattern
      (shorthand_property_identifier_pattern) @import.name)
    value: (call_expression
      function: (identifier) @import.method
      arguments: (arguments (string) @import.source)))) @import.statement
  (#eq? @import.method "require")
`
// Default export for backward compatibility
exports.default = exports.definitionQuery
